<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- 浅色 tint -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <!-- toolbar（actionbar）颜色 -->
        <!-- colorPrimary 应用的主要品牌颜色，主要用于主题 -->
        <item name="colorPrimary">@color/c1a1d20</item>
        <!-- 状态栏颜色 -->
        <item name="colorPrimaryDark">@color/cffffff</item>
        <item name="colorAccent">@color/ce35728</item>
        <item name="android:forceDarkAllowed" tools:ignore="NewApi">false</item>
        <!-- 其他主题属性 -->
        <item name="android:navigationBarColor">@color/cffffff</item>
        <item name="android:windowBackground">@color/cffffff</item>
        <item name="android:windowLightNavigationBar" tools:ignore="NewApi">true</item>
        <!-- 文字支持rtl -->
        <item name="android:textDirection">locale</item>
        <!-- 输入框支持rtl -->
        <item name="editTextStyle">@style/EditTextStyle.Rtl</item>
        <!-- 用于定义控件在获得焦点或被按下时的高亮颜色 -->
        <item name="colorControlHighlight">@color/transparent</item>
        <!-- 自定义颜色 -->
        <item name="mainLayoutBg">@color/cffffff</item>
        <!--  Brand Colors Primary 品牌色 -->
        <item name="color_c034854_ce35728">@color/c034854</item>
        <!--  background cards 背景 和 卡片 -->
        <item name="color_cf5f5f5_c1a1d20">@color/cf5f5f5</item>
        <item name="color_cffffff_c262930">@color/cffffff</item>
        <item name="color_c262930_cffffff">@color/c262930</item>

        <!--  text 文字 -->
        <item name="color_c1e1e1e_cebffffff">@color/c1e1e1e</item>
        <item name="color_ca61e1e1e_c99ffffff">@color/ca61e1e1e</item>
        <item name="color_c731e1e1e_c61ffffff">@color/c731e1e1e</item>
        <item name="color_c1f1e1e1e_c1fffffff">@color/c1f1e1e1e</item>
        <item name="color_cebffffff_c1e1e1e">@color/cebffffff</item>
        <!--  field  -->
        <item name="color_c0a1e1e1e_c262930">@color/c0a1e1e1e</item>
        <!--  分割线 & Order按钮  -->
        <item name="color_c0a1e1e1e_c0affffff">@color/c0a1e1e1e</item>
        <!--  frame 边框  -->
        <item name="color_c331e1e1e_c33ffffff">@color/c331e1e1e</item>
        <!-- Profile Verification Labels 配置文件验证标签 图标色值 -->
        <item name="color_ce8e8e8_c414348">@color/ce8e8e8</item>
        <item name="color_cfcebe5_c3e3535">@color/cfcebe5</item>
        <item name="color_ce5f7f3_c283b3d">@color/ce5f7f3</item>
        <item name="color_cf9ebe6_c3b2f2f">@color/cf9ebe6</item>
        <item name="color_cfae9e8_c3c2d32">@color/cfae9e8</item>
        <item name="color_ce0f0ff_c0c2c4d">@color/ce0f0ff</item>
        <!--  Share Background 分享卡片 / K线分享背景  -->
        <item name="color_cf3f3f3_c262930">@color/cf3f3f3</item>
        <item name="color_ce4e4e4_c16181b">@color/ce4e4e4</item>
        <!--  WhatsApp Buttons WhatsApp 按钮  -->
        <item name="color_cbf25d366_c3325d366">@color/cbf25d366</item>
        <!-- Grabber 下拉滑块 -->
        <item name="color_c4d1e1e1e_c4dffffff">@color/c4d1e1e1e</item>
        <!-- VolSeekBar -->
        <item name="color_cffffff_c1a1d20">@color/cffffff</item>
        <item name="color_ff1e1e1e_ebffffff">@color/c1e1e1e</item>
        <item name="color_0a1e1e1e_0affffff">@color/c0a1e1e1e</item>
        <!-- Trades新手引导 -->
        <item name="color_c1f1e1e1e_c262930">@color/c1f1e1e1e</item>
        <item name="color_c0a1e1e1e_c1fffffff">@color/c0a1e1e1e</item>
        <item name="color_c0a1e1e1e_c0ab2b2b2">@color/c0a1e1e1e</item>

        <item name="color_c0a1e1e1e_c1fffffff">@color/c0a1e1e1e</item>
        <item name="color_cffffff_cebffffff">@color/cffffff</item>
        <item name="color_c1e1e1e_cffffff">@color/c1e1e1e</item>

        <item name="mainTabTrades">@drawable/main_tab_trade</item>
        <item name="mainTabHome">@drawable/main_tab_home</item>
        <item name="mainTabDiscover">@drawable/main_tab_discover</item>
        <item name="mainTabPromo">@drawable/main_tab_promo</item>
        <item name="mainTabProfile">@drawable/main_tab_profile</item>
        <item name="mainTabClose">@drawable/main_tab_close</item>
        <item name="icon1Msg">@drawable/icon1_msg</item>
        <item name="icon1Cs">@drawable/icon1_cs</item>
        <item name="icon1Share">@drawable/icon1_share</item>
        <item name="icon1Share2">@drawable/icon1_share2</item>
        <item name="icon1Menu">@drawable/icon1_menu</item>
        <item name="icon1Info">@drawable/icon1_info</item>
        <item name="icon1Setting">@drawable/icon1_setting</item>
        <item name="icon1OrderSetting">@drawable/icon1_order_setting</item>
        <item name="icon1Drawing">@drawable/icon1_drawing</item>
        <item name="icon1Guide">@drawable/icon1_guide</item>
        <item name="icon1Faq">@drawable/icon1_faq</item>
        <item name="icon1Signals">@drawable/icon1_signals</item>
        <item name="icon1Refresh">@drawable/icon1_refresh</item>
        <item name="icon1AlertPrice">@drawable/icon1_alert_price</item>
        <item name="icon1SwitchAccount">@drawable/icon1_switch_account</item>
        <item name="icon1NoticeClear">@drawable/icon1_notice_clear</item>
        <item name="icon1WalletHistory">@drawable/icon1_wallet_history</item>
        <item name="icon2CloseCircle">@drawable/icon2_close_circle</item>
        <item name="icon2ECInactive">@drawable/icon2_ec_inactive</item>
        <item name="icon2ECOut">@drawable/icon2_ec_out</item>
        <item name="icon2Edit12x12">@drawable/icon2_edit_12x12</item>
        <item name="icon2Edit16x16">@drawable/icon2_edit_16x16</item>
        <item name="icon2EditStrategyFunds">@drawable/icon2_edit_strategy_copy</item>
        <item name="icon2NextInactive">@drawable/icon2_next_inactive</item>
        <item name="icon2ProfileUidCopy">@drawable/icon2_profile_uid_copy</item>
        <item name="icon2Sub">@drawable/icon2_sub</item>
        <item name="icon2SymbolSwitch">@drawable/icon2_symbol_switch</item>
        <item name="icon2CloseCircle12x12">@drawable/icon2_close_circle_12x12</item>
        <item name="icon2HistoryEnter">@drawable/icon2_history_enter</item>
        <item name="icon2Delete">@drawable/icon_source2_delete_tpsl</item>
        <item name="icon2EditTpSl">@drawable/icon2_edit_tpsl</item>
        <item name="icon2SharePosition">@drawable/icon2_share_position</item>
        <item name="icon2ReservePosition">@drawable/icon2_reserve_position</item>
        <item name="icon2ArrowBottom">@drawable/icon2_arrow_bottom</item>
        <item name="icon2BgSellUnselected">@drawable/icon2_bg_sell_unselected</item>
        <item name="icon2BgSellNoTrading">@drawable/icon2_bg_sell_no_trading</item>
        <item name="icon2BgBuyUnselected">@drawable/icon2_bg_buy_unselected</item>
        <item name="icon2BgBuyNoTrading">@drawable/icon2_bg_buy_no_trading</item>
        <item name="icon2CbSquareUncheck">@drawable/icon2_cb_square_uncheck</item>

        <item name="imgProfileWelcome">@drawable/img_profile_welcome</item>
        <item name="imgProfileDeposit">@drawable/img_profile_deposit</item>
        <item name="imgProfileTransfer">@drawable/img_profile_transfer</item>
        <item name="imgProfileWithdraw">@drawable/img_profile_withdraw</item>
        <item name="imgProfileFunds">@drawable/img_profile_funds</item>
        <item name="imgProfileCryptoWallet">@drawable/img_profile_crypto_wallet</item>
        <item name="imgProfileVantageRewards">@drawable/img_profile_vantage_rewards</item>
        <item name="imgProfileMissionCenter">@drawable/img_profile_mission_center</item>
        <item name="imgProfileCoupons">@drawable/img_profile_coupons</item>
        <item name="imgProfileReferrals">@drawable/img_profile_referrals</item>
        <item name="imgProfileFavourites">@drawable/img_profile_favourites</item>
        <item name="imgProfileIb">@drawable/img_profile_ib</item>
        <item name="imgProfilePriceAlter">@drawable/img_profile_alter</item>
        <item name="imgProfileSecurity">@drawable/img_profile_security</item>
        <item name="imgProfileTransferIB">@drawable/img_profile_transfer_ib</item>
        <item name="imgProfileSettings">@drawable/img_profile_setting</item>
        <item name="imgAlertOk">@drawable/img_alert_ok</item>
        <item name="imgAlertWrong">@drawable/img_alert_wrong</item>
        <item name="imgAlertProcessed">@drawable/img_alert_processed</item>
        <item name="imgAcademyRightTop">@drawable/img_academy_right_top</item>
        <item name="imgAcademyRightBottom">@drawable/img_academy_right_bottom</item>
        <item name="imgHelpFaqs">@drawable/img_help_faqs</item>
        <item name="imgHelpContactUs">@drawable/img_help_contact_us</item>
        <item name="imgSecurityPasskeyIcon">@drawable/img_security_passkey_icon</item>
        <item name="imgSecurityPasskeyAuth">@drawable/img_security_passkey_auth</item>
        <item name="imgSecurityPasskeyCreating">@drawable/img_security_passkey_creating</item>
        <item name="imgSecurityPasskeyCreatingFailed">@drawable/img_security_passkey_creating_failed</item>
        <item name="img2FALink">@drawable/img_2fa_link</item>
        <item name="imgAlertFail">@drawable/img_alert_fail</item>
        <item name="imgOpenLiveGuide1">@drawable/img_open_live_assets</item>
        <item name="imgOpenLiveGuide2">@drawable/img_open_live_spreads</item>
        <item name="imgOpenLiveGuide3">@drawable/img_open_live_fee</item>
        <item name="imgOpenLiveGuide4">@drawable/img_open_live_cs</item>
        <item name="imgTheme">@drawable/img_theme</item>
        <item name="imgUnlockTop">@drawable/img_unlock_top</item>
        <item name="imgUnlockOpen">@drawable/img_unlock_open</item>
        <item name="imgUnlockTrades">@drawable/img_unlock_trades</item>
        <item name="imgUnlockFingerprint">@drawable/img_unlock_fingerprint</item>
        <item name="imgOpenStepAccount">@drawable/img_open_step_account</item>
        <item name="imgOpenStepPersonal">@drawable/img_open_step_personal</item>
        <item name="imgOpenStepDeclaration">@drawable/img_open_step_declaration</item>
        <item name="imgOpenStepID">@drawable/img_open_step_id</item>
        <item name="imgOpenStepPOA">@drawable/img_open_step_poa</item>
        <item name="imgOpenStepUnSelectedAccount">@drawable/img_open_step_unselected_account</item>
        <item name="imgOpenStepUnSelectedPersonal">@drawable/img_open_step_unselected_personal</item>
        <item name="imgOpenStepUnSelectedDeclaration">@drawable/img_open_step_unselected_declaration</item>
        <item name="imgOpenStepUnSelectedID">@drawable/img_open_step_unselected_id</item>
        <item name="imgOpenStepUnSelectedPOA">@drawable/img_open_step_unselected_poa</item>
        <item name="imgPermissionWithdrawal">@drawable/img_permission_withdrawal</item>
        <item name="imgPermissionWithdrawalFailed">@drawable/img_permission_withdrawal_failed</item>
        <item name="imgDepositCreditCard">@drawable/img_deposit_credit_card</item>
        <item name="imgCardNumber">@drawable/img_card_number</item>
        <item name="imgPriceAlertMiss">@drawable/img_price_alert_miss</item>
        <item name="imgNoDataBase">@drawable/img_no_data_base</item>
        <item name="imgTradingViewDrawingBrush">@drawable/img_tradingview_brush</item>
        <item name="imgTradingViewDrawingHighLighter">@drawable/img_tradingview_highlighter</item>
        <item name="imgTradingViewDrawingEraser">@drawable/img_tradingview_eraser</item>
        <item name="imgTradingViewLineHorizontal">@drawable/img_tradingview_horizontalline</item>
        <item name="imgTradingViewLinePath">@drawable/img_tradingview_path</item>
        <item name="imgTradingViewLineTrend">@drawable/img_tradingview_trendline</item>
        <item name="imgTradingViewLineVertical">@drawable/img_tradingview_verticalline</item>
        <item name="imgTradingViewShapePolyline">@drawable/img_tradingview_polyline</item>
        <item name="imgTradingViewShapeRectangle">@drawable/img_tradingview_rectangle</item>
        <item name="icon2KlineScreenExpand">@drawable/icon2_kline_screen_expand</item>
        <!--新版k线绘图icon START-->
        <item name="imgKlineLogo">@drawable/img_kline_logo</item><!--水平线-->
        <item name="icon2KlineDrawLineHorizontalSelect">@drawable/select_kline_draw_horizontal_line</item><!--水平线-->
        <item name="icon2KlineDrawLineVerticalSelect">@drawable/select_kline_draw_vertical_line</item><!--垂直线-->
        <item name="icon2KlineDrawShapeBezierSelect">@drawable/select_kline_draw_bezier</item><!--自由线-->
        <item name="icon2KlineDrawShapeRectangleSelect">@drawable/select_kline_draw_rectangle</item><!--矩形-->
        <item name="icon2KlineDrawTrendLineSelect">@drawable/select_kline_draw_trend_line</item><!--趋势线-->
        <item name="icon2KlineDrawRaysSelect">@drawable/select_kline_draw_rays</item><!--射线-->
        <item name="icon2KlineDrawParallelLineSelect">@drawable/select_kline_draw_parallel_line</item><!--平行线-->
        <item name="icon2KlineDrawToolsContinueSelect">@drawable/select_kline_draw_tools_continue</item>
        <item name="icon2KlineDrawToolsShowSelect">@drawable/select_kline_draw_tools_show</item>
        <item name="icon2KlineDrawToolsClean">@drawable/icon2_kline_draw_tools_clean</item>
        <item name="icon2KlineDrawToolsMain">@drawable/icon2_kline_draw_tools_main</item>
        <item name="icon2KlineDrawToolsDraw">@drawable/icon2_kline_draw_tools_draw</item>
        <!--k线绘图工具栏-->
        <item name="icon2KlineDrawToolsHandle">@drawable/icon2_kline_draw_tools_handle</item>
        <item name="icon2KlineDrawToolsEdit">@drawable/icon_source2_kline_draw_tools_edit</item>
        <item name="icon2KlineDrawToolsSolid1">@drawable/icon_kline_draw_tools_solid_01</item>
        <item name="icon2KlineDrawToolsSolid2">@drawable/icon_kline_draw_tools_solid_02</item>
        <item name="icon2KlineDrawToolsSolid3">@drawable/icon_kline_draw_tools_solid_03</item>
        <item name="icon2KlineDrawToolsDelete">@drawable/icon2_kline_draw_tools_delete</item>
        <item name="icon2KlineDrawToolsArrowDown">@drawable/icon2_kline_draw_tools_arrow_down</item>
        <item name="icon2KlineDrawToolsArrowUp">@drawable/icon2_kline_draw_tools_arrow_up</item>
        <!--新版k线绘图icon END-->
        <item name="imgAddBank">@drawable/img_add_bank</item>
        <item name="imgShareEdit">@drawable/img_share_edit</item>
        <item name="imgShareCopyLink">@drawable/img_share_copy_link</item>
        <item name="imgShareMore">@drawable/img_share_more</item>
        <item name="imgShareSave">@drawable/img_share_save</item>
        <item name="imgShareThemeLogo">@drawable/img_share_theme_logo</item>
        <item name="imgShareThemeLogoSelect">@drawable/img_share_theme_logo_select</item>
        <item name="imgShareThemeFerrari">@drawable/img_share_theme_ferrari</item>
        <item name="imgShareThemeFerrariSelect">@drawable/img_share_theme_ferrari_select</item>
        <item name="imgShareLogo">@drawable/img_share_logo</item>
        <item name="imgBankStatement">@drawable/img_bank_statement</item>
        <item name="imgLogoMark">@drawable/img_logo_mark</item>
        <item name="imgLogo">@drawable/img_logo</item>
        <item name="imgDriversLicense">@drawable/img_drivers_license</item>
        <item name="imgPassport">@drawable/img_passport</item>
        <item name="imgIdentificationCard">@drawable/img_identification_card</item>
        <item name="imgUtilityBills">@drawable/img_utility_bills</item>
        <item name="imgLetter">@drawable/img_letter</item>
        <item name="imgFollower">@drawable/img_follower</item>
        <item name="img2faPhoneOtpSelect">@drawable/img_2fa_phone_otp_select</item>
        <item name="img2faPhoneOtpUnselect">@drawable/img_2fa_phone_otp_unselect</item>
        <item name="img2faSelect">@drawable/img_2fa_select</item>
        <item name="img2faUnselect">@drawable/img_2fa_unselect</item>
        <item name="img2faPwdSelect">@drawable/img_2fa_pwd_select</item>
        <item name="img2faPwdUnselect">@drawable/img_2fa_pwd_unselect</item>
        <item name="img2faEmailOtpSelect">@drawable/img_2fa_email_otp_select</item>
        <item name="img2faEmailOtpUnselect">@drawable/img_2fa_email_otp_unselect</item>
        <item name="imgCircleRight">@drawable/img_circle_right</item>
        <item name="imgTelegramNotLinked">@drawable/img_telegram_not_linked</item>
        <item name="imgNoticeTrade">@drawable/img_notice_trade</item>
        <item name="imgNoticeCopyTrading">@drawable/img_notice_copy_trading</item>
        <item name="imgNoticePriceAlerts">@drawable/img_notice_price_alert</item>
        <item name="imgNoticeAccount">@drawable/img_notice_account</item>
        <item name="imgNoticeAnnouncements">@drawable/img_notice_announcements</item>
        <item name="imgNoticeNews">@drawable/img_notice_news</item>
        <item name="imgInApp">@drawable/img_in_app</item>
        <item name="imgAskUnselected">@drawable/img_ask_unselected</item>
        <item name="imgClassicUnselected">@drawable/img_classic_unselected</item>
        <item name="imgNotSort">@drawable/img_sort_not</item>
        <item name="imgUpSort">@drawable/img_sort_up</item>
        <item name="imgDownSort">@drawable/img_sort_down</item>
        <item name="imgTradesGuideTop">@drawable/img_trades_guide_top</item>
        <item name="imgTradeNeverLogin">@drawable/img_trade_never_login</item>
        <item name="imgTradeNotLogin">@drawable/img_trade_not_login</item>
        <item name="imgItemChecked">@drawable/img_item_checked</item>
        <item name="imgVerifyLv1">@drawable/img_verify_lv1</item>
        <item name="imgVerifyLv2">@drawable/img_verify_lv2</item>
        <item name="imgVerifyLv3">@drawable/img_verify_lv3</item>
        <item name="imgUnVerification">@drawable/img_un_verification</item>
        <item name="imgWelcomeDeposit">@drawable/img_kyc_welcome_deposit</item>
        <item name="imgWelcomeWithdraw">@drawable/img_kyc_welcome_withdraw</item>
        <item name="imgWelcomeTrades">@drawable/img_kyc_welcome_trades</item>
        <item name="imgFeatureLock">@drawable/img_kyc_feature_lock</item>
        <item name="imgFeatureWallet">@drawable/img_kyc_feature_wallet</item>
        <item name="bgMt4Confirm">@drawable/img_select_mt4_confirm</item>
        <item name="bgServerMaintenance">@drawable/img_no_data_maintenance</item>
        <item name="icNoConnection">@drawable/img_no_data_connection</item>
        <item name="icNoDataBase">@drawable/img_no_data_base</item>
        <item name="imgReverseOrder">@drawable/img_source_arrow_down_reverse</item>
        <item name="imgNotReceiveCodeTips1">@drawable/img_not_receive_code_tips1</item>
        <item name="imgNotReceiveCodeTips2">@drawable/img_not_receive_code_tips2</item>
        <item name="imgNotReceiveCodeTips3">@drawable/img_not_receive_code_tips3</item>
        <item name="imgNotReceiveCodeTips4">@drawable/img_not_receive_code_tips4</item>
    </style>
    <!-- Base application theme.-->
    <style name="TintAppTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <!-- toolbar（actionbar）颜色 -->
        <!-- colorPrimary 应用的主要品牌颜色，主要用于主题 -->
        <item name="colorPrimary">@color/cffffff</item>
        <!-- 状态栏颜色 -->
        <item name="colorPrimaryDark">@color/c1a1d20</item>
        <item name="colorAccent">@color/ce35728</item>
        <item name="android:forceDarkAllowed" tools:ignore="NewApi">false</item>
        <item name="android:navigationBarColor">@color/c1a1d20</item>
        <item name="android:windowLightNavigationBar" tools:ignore="NewApi">false</item>
        <item name="android:windowBackground">@color/c1a1d20</item>
        <!-- 文字支持rtl -->
        <item name="android:textDirection">locale</item>
        <!-- 输入框支持rtl -->
        <item name="editTextStyle">@style/EditTextStyle.Rtl</item>
        <!-- 用于定义控件在获得焦点或被按下时的高亮颜色 -->
        <item name="colorControlHighlight">@color/transparent</item>
        <!-- 自定义颜色 -->
        <item name="mainLayoutBg">@color/c1a1d20</item>
        <!--  Brand Colors Primary 品牌色 -->
        <item name="color_c034854_ce35728">@color/ce35728</item>
        <!--  background cards 背景 和 卡片 -->
        <item name="color_cf5f5f5_c1a1d20">@color/c1a1d20</item>
        <item name="color_cffffff_c262930">@color/c262930</item>
        <item name="color_c262930_cffffff">@color/cffffff</item>

        <!--  text 文字 -->
        <item name="color_c1e1e1e_cebffffff">@color/cebffffff</item>
        <item name="color_ca61e1e1e_c99ffffff">@color/c99ffffff</item>
        <item name="color_c731e1e1e_c61ffffff">@color/c61ffffff</item>
        <item name="color_c1f1e1e1e_c1fffffff">@color/c1fffffff</item>
        <item name="color_cebffffff_c1e1e1e">@color/c1e1e1e</item>
        <!--  field  -->
        <item name="color_c0a1e1e1e_c262930">@color/c262930</item>
        <!--  分割线 & Order按钮  -->
        <item name="color_c0a1e1e1e_c0affffff">@color/c0affffff</item>
        <!--  frame 边框  -->
        <item name="color_c331e1e1e_c33ffffff">@color/c33ffffff</item>
        <!-- Profile Verification Labels 配置文件验证标签 图标色值 -->
        <item name="color_ce8e8e8_c414348">@color/c414348</item>
        <item name="color_cfcebe5_c3e3535">@color/c3e3535</item>
        <item name="color_ce5f7f3_c283b3d">@color/c283b3d</item>
        <item name="color_cf9ebe6_c3b2f2f">@color/c3b2f2f</item>
        <item name="color_cfae9e8_c3c2d32">@color/c3c2d32</item>
        <item name="color_ce0f0ff_c0c2c4d">@color/c0c2c4d</item>
        <!--  Share Background 分享卡片 / K线分享背景  -->
        <item name="color_cf3f3f3_c262930">@color/c262930</item>
        <item name="color_ce4e4e4_c16181b">@color/c16181b</item>
        <!--  WhatsApp Buttons WhatsApp 按钮  -->
        <item name="color_cbf25d366_c3325d366">@color/c3325d366</item>
        <!-- Grabber 下拉滑块 -->
        <item name="color_c4d1e1e1e_c4dffffff">@color/c4dffffff</item>
        <!-- VolSeekBar -->
        <item name="color_cffffff_c1a1d20">@color/c1a1d20</item>
        <item name="color_ff1e1e1e_ebffffff">@color/cebffffff</item>
        <item name="color_0a1e1e1e_0affffff">@color/c0affffff</item>
        <!-- Trades新手引导 -->
        <item name="color_c1f1e1e1e_c262930">@color/c262930</item>
        <item name="color_c0a1e1e1e_c0ab2b2b2">@color/c0ab2b2b2</item>

        <item name="color_c0a1e1e1e_c1fffffff">@color/c1fffffff</item>
        <item name="color_cffffff_cebffffff">@color/cebffffff</item>
        <item name="color_c1e1e1e_cffffff">@color/cffffff</item>

        <item name="mainTabTrades">@drawable/main_tab_trade_d</item>
        <item name="mainTabHome">@drawable/main_tab_home_d</item>
        <item name="mainTabDiscover">@drawable/main_tab_discover_d</item>
        <item name="mainTabPromo">@drawable/main_tab_promo_d</item>
        <item name="mainTabProfile">@drawable/main_tab_profile_d</item>
        <item name="mainTabClose">@drawable/main_tab_close_d</item>
        <item name="icon1Msg">@drawable/icon1_msg_d</item>
        <item name="icon1Cs">@drawable/icon1_cs_d</item>
        <item name="icon1Share">@drawable/icon1_share_d</item>
        <item name="icon1Share2">@drawable/icon1_share2_d</item>
        <item name="icon1Menu">@drawable/icon1_menu_d</item>
        <item name="icon1Info">@drawable/icon1_info_d</item>
        <item name="icon1Setting">@drawable/icon1_setting_d</item>
        <item name="icon1OrderSetting">@drawable/icon1_order_setting_d</item>
        <item name="icon1Drawing">@drawable/icon1_drawing_d</item>
        <item name="icon1Guide">@drawable/icon1_guide_d</item>
        <item name="icon1Faq">@drawable/icon1_faq_d</item>
        <item name="icon1Signals">@drawable/icon1_signals_d</item>
        <item name="icon1Refresh">@drawable/icon1_refresh_d</item>
        <item name="icon1AlertPrice">@drawable/icon1_alert_price_d</item>
        <item name="icon1SwitchAccount">@drawable/icon1_switch_account_d</item>
        <item name="icon1NoticeClear">@drawable/icon1_notice_clear_d</item>
        <item name="icon1WalletHistory">@drawable/icon1_wallet_history_d</item>
        <item name="icon2CloseCircle">@drawable/icon2_close_circle_d</item>
        <item name="icon2ECInactive">@drawable/icon2_ec_inactive_d</item>
        <item name="icon2ECOut">@drawable/icon2_ec_out_d</item>
        <item name="icon2Edit12x12">@drawable/icon2_edit_12x12_d</item>
        <item name="icon2Edit16x16">@drawable/icon2_edit_16x16_d</item>
        <item name="icon2EditStrategyFunds">@drawable/icon2_edit_strategy_copy_d</item>
        <item name="icon2NextInactive">@drawable/icon2_next_inactive_d</item>
        <item name="icon2ProfileUidCopy">@drawable/icon2_profile_uid_copy_d</item>
        <item name="icon2Sub">@drawable/icon2_sub_d</item>
        <item name="icon2SymbolSwitch">@drawable/icon2_symbol_switch_d</item>
        <item name="icon2CloseCircle12x12">@drawable/icon2_close_circle_12x12_d</item>
        <item name="icon2HistoryEnter">@drawable/icon2_history_enter_d</item>
        <item name="icon2Delete">@drawable/icon_source2_delete_tpsl_d</item>
        <item name="icon2EditTpSl">@drawable/icon2_edit_tpsl_d</item>
        <item name="icon2SharePosition">@drawable/icon2_share_position_d</item>
        <item name="icon2ReservePosition">@drawable/icon2_reserve_position_d</item>
        <item name="icon2ArrowBottom">@drawable/icon2_arrow_bottom_d</item>
        <item name="icon2BgSellUnselected">@drawable/icon2_bg_sell_unselected_d</item>
        <item name="icon2BgSellNoTrading">@drawable/icon2_bg_sell_no_trading_d</item>
        <item name="icon2BgBuyUnselected">@drawable/icon2_bg_buy_unselected_d</item>
        <item name="icon2BgBuyNoTrading">@drawable/icon2_bg_buy_no_trading_d</item>
        <item name="icon2CbSquareUncheck">@drawable/icon2_cb_square_uncheck_d</item>

        <item name="imgProfileWelcome">@drawable/img_profile_welcome_d</item>
        <item name="imgProfileDeposit">@drawable/img_profile_deposit_d</item>
        <item name="imgProfileTransfer">@drawable/img_profile_transfer_d</item>
        <item name="imgProfileWithdraw">@drawable/img_profile_withdraw_d</item>
        <item name="imgProfileFunds">@drawable/img_profile_funds_d</item>
        <item name="imgProfileCryptoWallet">@drawable/img_profile_crypto_wallet_d</item>
        <item name="imgProfileVantageRewards">@drawable/img_profile_vantage_rewards_d</item>
        <item name="imgProfileMissionCenter">@drawable/img_profile_mission_center_d</item>
        <item name="imgProfileCoupons">@drawable/img_profile_coupons_d</item>
        <item name="imgProfileReferrals">@drawable/img_profile_referrals_d</item>
        <item name="imgProfileFavourites">@drawable/img_profile_favourites_d</item>
        <item name="imgProfileIb">@drawable/img_profile_ib_d</item>
        <item name="imgProfilePriceAlter">@drawable/img_profile_alter_d</item>
        <item name="imgProfileSecurity">@drawable/img_profile_security_d</item>
        <item name="imgProfileTransferIB">@drawable/img_profile_transfer_ib_d</item>
        <item name="imgProfileSettings">@drawable/img_profile_setting_d</item>
        <item name="imgAlertOk">@drawable/img_alert_ok_d</item>
        <item name="imgAlertWrong">@drawable/img_alert_wrong_d</item>
        <item name="imgAlertProcessed">@drawable/img_alert_processed_d</item>
        <item name="imgAcademyRightTop">@drawable/img_academy_right_top_d</item>
        <item name="imgAcademyRightBottom">@drawable/img_academy_right_bottom_d</item>
        <item name="imgHelpFaqs">@drawable/img_help_faqs_d</item>
        <item name="imgHelpContactUs">@drawable/img_help_contact_us_d</item>
        <item name="imgSecurityPasskeyIcon">@drawable/img_security_passkey_icon_d</item>
        <item name="imgSecurityPasskeyAuth">@drawable/img_security_passkey_auth_d</item>
        <item name="imgSecurityPasskeyCreating">@drawable/img_security_passkey_creating_d</item>
        <item name="imgSecurityPasskeyCreatingFailed">
            @drawable/img_security_passkey_creating_failed_d
        </item>
        <item name="img2FALink">@drawable/img_2fa_link_d</item>
        <item name="imgAlertFail">@drawable/img_alert_fail_d</item>
        <item name="imgOpenLiveGuide1">@drawable/img_open_live_assets_d</item>
        <item name="imgOpenLiveGuide2">@drawable/img_open_live_spreads_d</item>
        <item name="imgOpenLiveGuide3">@drawable/img_open_live_fee_d</item>
        <item name="imgOpenLiveGuide4">@drawable/img_open_live_cs_d</item>
        <item name="imgTheme">@drawable/img_theme_d</item>
        <item name="imgUnlockTop">@drawable/img_unlock_top_d</item>
        <item name="imgUnlockOpen">@drawable/img_unlock_open_d</item>
        <item name="imgUnlockTrades">@drawable/img_unlock_trades_d</item>
        <item name="imgUnlockFingerprint">@drawable/img_unlock_fingerprint_d</item>
        <item name="imgOpenStepAccount">@drawable/img_open_step_account_d</item>
        <item name="imgOpenStepPersonal">@drawable/img_open_step_personal_d</item>
        <item name="imgOpenStepDeclaration">@drawable/img_open_step_declaration_d</item>
        <item name="imgOpenStepID">@drawable/img_open_step_id_d</item>
        <item name="imgOpenStepPOA">@drawable/img_open_step_poa_d</item>
        <item name="imgOpenStepUnSelectedAccount">@drawable/img_open_step_unselected_account_d</item>
        <item name="imgOpenStepUnSelectedPersonal">@drawable/img_open_step_unselected_personal_d</item>
        <item name="imgOpenStepUnSelectedDeclaration">@drawable/img_open_step_unselected_declaration_d</item>
        <item name="imgOpenStepUnSelectedID">@drawable/img_open_step_unselected_id_d</item>
        <item name="imgOpenStepUnSelectedPOA">@drawable/img_open_step_unselected_poa_d</item>
        <item name="imgPermissionWithdrawal">@drawable/img_permission_withdrawal_d</item>
        <item name="imgPermissionWithdrawalFailed">@drawable/img_permission_withdrawal_failed_d</item>
        <item name="imgDepositCreditCard">@drawable/img_deposit_credit_card_d</item>
        <item name="imgCardNumber">@drawable/img_card_number_d</item>
        <item name="imgPriceAlertMiss">@drawable/img_price_alert_miss_dark</item>
        <item name="imgNoDataBase">@drawable/img_no_data_base_d</item>
        <item name="imgTradingViewDrawingBrush">@drawable/img_tradingview_brush_d</item>
        <item name="imgTradingViewDrawingHighLighter">@drawable/img_tradingview_highlighter_d</item>
        <item name="imgTradingViewDrawingEraser">@drawable/img_tradingview_eraser_d</item>
        <item name="imgTradingViewLineHorizontal">@drawable/img_tradingview_horizontalline_d</item>
        <item name="imgTradingViewLinePath">@drawable/img_tradingview_path_d</item>
        <item name="imgTradingViewLineTrend">@drawable/img_tradingview_trendline_d</item>
        <item name="imgTradingViewLineVertical">@drawable/img_tradingview_verticalline_d</item>
        <item name="imgTradingViewShapePolyline">@drawable/img_tradingview_polyline_d</item>
        <item name="imgTradingViewShapeRectangle">@drawable/img_tradingview_rectangle_d</item>
        <item name="icon2KlineScreenExpand">@drawable/icon2_kline_screen_expand_d</item>
        <!--新版k线绘图icon START-->
        <item name="imgKlineLogo">@drawable/img_kline_logo_d</item>
        <item name="icon2KlineDrawLineHorizontalSelect">@drawable/select_kline_draw_horizontal_line</item><!--水平线-->
        <item name="icon2KlineDrawLineVerticalSelect">@drawable/select_kline_draw_vertical_line</item><!--垂直线-->
        <item name="icon2KlineDrawShapeBezierSelect">@drawable/select_kline_draw_bezier</item><!--自由线-->
        <item name="icon2KlineDrawShapeRectangleSelect">@drawable/select_kline_draw_rectangle</item><!--矩形-->
        <item name="icon2KlineDrawTrendLineSelect">@drawable/select_kline_draw_trend_line</item><!--趋势线-->
        <item name="icon2KlineDrawRaysSelect">@drawable/select_kline_draw_rays</item><!--射线-->
        <item name="icon2KlineDrawParallelLineSelect">@drawable/select_kline_draw_parallel_line</item><!--平行线-->
        <item name="icon2KlineDrawToolsContinueSelect">@drawable/select_kline_draw_tools_continue</item>
        <item name="icon2KlineDrawToolsShowSelect">@drawable/select_kline_draw_tools_show_d</item>
        <item name="icon2KlineDrawToolsClean">@drawable/icon2_kline_draw_tools_clean_d</item>
        <item name="icon2KlineDrawToolsMain">@drawable/icon2_kline_draw_tools_main_d</item>
        <item name="icon2KlineDrawToolsDraw">@drawable/icon2_kline_draw_tools_draw_d</item>
        <!--k线绘图工具栏-->
        <item name="icon2KlineDrawToolsHandle">@drawable/icon2_kline_draw_tools_handle_d</item>
        <item name="icon2KlineDrawToolsEdit">@drawable/icon_source2_kline_draw_tools_edit_d</item>
        <item name="icon2KlineDrawToolsSolid1">@drawable/icon_kline_draw_tools_solid_01_d</item>
        <item name="icon2KlineDrawToolsSolid2">@drawable/icon_kline_draw_tools_solid_02_d</item>
        <item name="icon2KlineDrawToolsSolid3">@drawable/icon_kline_draw_tools_solid_03_d</item>
        <item name="icon2KlineDrawToolsDelete">@drawable/icon2_kline_draw_tools_delete_d</item>
        <item name="icon2KlineDrawToolsArrowDown">@drawable/icon2_kline_draw_tools_arrow_down_d</item>
        <item name="icon2KlineDrawToolsArrowUp">@drawable/icon2_kline_draw_tools_arrow_up_d</item>
        <!--新版k线绘图icon END-->
        <item name="imgAddBank">@drawable/img_add_bank_d</item>
        <item name="imgShareEdit">@drawable/img_share_edit_d</item>
        <item name="imgShareCopyLink">@drawable/img_share_copy_link_d</item>
        <item name="imgShareMore">@drawable/img_share_more_d</item>
        <item name="imgShareSave">@drawable/img_share_save_d</item>
        <item name="imgShareThemeLogo">@drawable/img_share_theme_logo_d</item>
        <item name="imgShareThemeLogoSelect">@drawable/img_share_theme_logo_select_d</item>
        <item name="imgShareThemeFerrari">@drawable/img_share_theme_ferrari_d</item>
        <item name="imgShareThemeFerrariSelect">@drawable/img_share_theme_ferrari_select_d</item>
        <item name="imgShareLogo">@drawable/img_share_logo_d</item>
        <item name="imgBankStatement">@drawable/img_bank_statement_d</item>
        <item name="imgLogoMark">@drawable/img_logo_mark_d</item>
        <item name="imgLogo">@drawable/img_logo_d</item>
        <item name="imgDriversLicense">@drawable/img_drivers_license_d</item>
        <item name="imgPassport">@drawable/img_passport_d</item>
        <item name="imgIdentificationCard">@drawable/img_identification_card_d</item>
        <item name="imgUtilityBills">@drawable/img_utility_bills_d</item>
        <item name="imgLetter">@drawable/img_letter_d</item>
        <item name="imgFollower">@drawable/img_follower_d</item>
        <item name="img2faPhoneOtpSelect">@drawable/img_2fa_phone_otp_select_d</item>
        <item name="img2faPhoneOtpUnselect">@drawable/img_2fa_phone_otp_unselect_d</item>
        <item name="img2faSelect">@drawable/img_2fa_select_d</item>
        <item name="img2faUnselect">@drawable/img_2fa_unselect_d</item>
        <item name="img2faPwdSelect">@drawable/img_2fa_pwd_select_d</item>
        <item name="img2faPwdUnselect">@drawable/img_2fa_pwd_unselect_d</item>
        <item name="img2faEmailOtpSelect">@drawable/img_2fa_email_otp_select_d</item>
        <item name="img2faEmailOtpUnselect">@drawable/img_2fa_email_otp_unselect_d</item>
        <item name="imgCircleRight">@drawable/img_circle_right_d</item>
        <item name="imgTelegramNotLinked">@drawable/img_telegram_not_linked_d</item>
        <item name="imgNoticeTrade">@drawable/img_notice_trade_d</item>
        <item name="imgNoticeCopyTrading">@drawable/img_notice_copy_trading_d</item>
        <item name="imgNoticePriceAlerts">@drawable/img_notice_price_alert_d</item>
        <item name="imgNoticeAccount">@drawable/img_notice_account_d</item>
        <item name="imgNoticeAnnouncements">@drawable/img_notice_announcements_d</item>
        <item name="imgNoticeNews">@drawable/img_notice_news_d</item>
        <item name="imgInApp">@drawable/img_in_app_d</item>
        <item name="imgAskUnselected">@drawable/img_ask_unselected_d</item>
        <item name="imgClassicUnselected">@drawable/img_classic_unselected_d</item>
        <item name="imgNotSort">@drawable/img_sort_not_d</item>
        <item name="imgUpSort">@drawable/img_sort_up_d</item>
        <item name="imgDownSort">@drawable/img_sort_down_d</item>
        <item name="imgTradesGuideTop">@drawable/img_trades_guide_top_d</item>
        <item name="imgTradeNeverLogin">@drawable/img_trade_never_login_d</item>
        <item name="imgTradeNotLogin">@drawable/img_trade_not_login_d</item>
        <item name="imgItemChecked">@drawable/img_item_checked_d</item>
        <item name="imgVerifyLv1">@drawable/img_verify_lv1_d</item>
        <item name="imgVerifyLv2">@drawable/img_verify_lv2_d</item>
        <item name="imgVerifyLv3">@drawable/img_verify_lv3_d</item>
        <item name="imgUnVerification">@drawable/img_un_verification_d</item>
        <item name="imgWelcomeDeposit">@drawable/img_kyc_welcome_deposit_d</item>
        <item name="imgWelcomeWithdraw">@drawable/img_kyc_welcome_withdraw_d</item>
        <item name="imgWelcomeTrades">@drawable/img_kyc_welcome_trades_d</item>
        <item name="imgFeatureLock">@drawable/img_kyc_feature_lock_d</item>
        <item name="imgFeatureWallet">@drawable/img_kyc_feature_wallet_d</item>
        <item name="bgMt4Confirm">@drawable/img_select_mt4_confirm_dark</item>
        <item name="bgServerMaintenance">@drawable/img_no_data_maintenance_d</item>
        <item name="icNoConnection">@drawable/img_no_data_connection_d</item>
        <item name="icNoDataBase">@drawable/img_no_data_base_d</item>
        <item name="imgReverseOrder">@drawable/img_source_arrow_down_reverse_d</item>
        <item name="imgNotReceiveCodeTips1">@drawable/img_not_receive_code_tips1_d</item>
        <item name="imgNotReceiveCodeTips2">@drawable/img_not_receive_code_tips2_d</item>
        <item name="imgNotReceiveCodeTips3">@drawable/img_not_receive_code_tips3_d</item>
        <item name="imgNotReceiveCodeTips4">@drawable/img_not_receive_code_tips4_d</item>
    </style>

    <style name="SplashTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <item name="android:windowBackground">@color/c1e1e1e</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowDisablePreview">false</item>
    </style>

    <style name="TranslucentStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item> <!-- 背景色透明 -->
        <item name="android:windowIsTranslucent">true</item> <!-- 是否有透明属性 -->
        <item name="android:backgroundDimEnabled">false</item> <!-- 背景是否半透明 -->
        <item name="android:statusBarColor">@android:color/transparent</item> <!-- 状态栏透明 -->
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent
        </item> <!-- activity窗口切换效果 -->
    </style>
    <!--订单图表-->
    <declare-styleable name="VFXOrderChart">
        <!-- 卖线颜色 -->
        <attr name="bid_color" format="color" />
        <!-- 买线颜色 -->
        <attr name="ask_color" format="color" />
        <!-- 虚线颜色 -->
        <attr name="line_virtual_color" format="color" />
        <!-- 现价文本颜色 -->
        <attr name="text_current_price_color" format="color" />
        <!-- 显示多少个价格点 -->
        <attr name="price_point_nums" format="integer" />
        <!-- Y轴刻度价格颜色 -->
        <attr name="text_scale_color" format="color" />
        <!-- Y轴刻度文本宽度 yScaleDataWidth -->
        <attr name="scale_width_y" format="dimension" />
        <!-- padding -->
        <attr name="oc_padding_top" format="dimension" />
        <attr name="oc_padding_right" format="dimension" />
        <attr name="oc_padding_bottom" format="dimension" />
        <attr name="oc_padding_left" format="dimension" />
        <!-- 文字大小 -->
        <attr name="oc_text_size" format="integer" />
    </declare-styleable>
    <!-- banner indicator -->
    <declare-styleable name="BannerIndicatorView">
        <!-- 显示多少个 indicator -->
        <attr name="indicator_count" format="integer" />
        <!-- indicator 宽高 选中圆点单设置 indicator_select_width -->
        <attr name="indicator_select_width" format="dimension" />
        <attr name="indicator_select_height" format="dimension" />
        <attr name="indicator_normal_width" format="dimension" />
        <attr name="indicator_normal_height" format="dimension" />
        <attr name="indicator_margins" format="dimension" />
        <attr name="indicator_select_color" format="reference" />
        <attr name="indicator_normal_color" format="reference" />
    </declare-styleable>
    <declare-styleable name="ScrollTextView">
        <attr name="scroll_text_size" format="dimension" />
        <attr name="scroll_text_color" format="color" />
        <attr name="scroll_font_family" format="enum">
            <enum name="bold" value="0" />
            <enum name="medium" value="1" />
            <enum name="regular" value="2" />
            <enum name="semi_bold" value="3" />
        </attr>
    </declare-styleable>
    <!--财经日历详情折线图-->
    <declare-styleable name="CalendarLineChart">
        <!--线的颜色-->
        <attr name="line_straight_color" format="color" />
        <!--线左边数据文本颜色-->
        <attr name="text_data_color" format="color" />
        <!--线左边数据大小-->
        <attr name="text_data_size" format="dimension" />
        <!--底部日期颜色-->
        <attr name="text_date_color" format="color" />
        <!--底部日期大小-->
        <attr name="text_date_size" format="dimension" />
        <!--数据线颜色-->
        <attr name="line_broken_color" format="color" />
        <!--数据线宽度-->
        <attr name="line_broken_width" format="dimension" />
    </declare-styleable>

    <style name="LoadRequestDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <declare-styleable name="MaxLimitRecyclerView">
        <attr name="limit_maxHeight" format="reference|dimension" />
        <attr name="limit_maxWidth" format="reference|dimension" />
    </declare-styleable>
    <!-- TabLayout 下面的横线样式（R.layout.item_tab_level_2样式下面才有） -->
    <style name="TabLayoutBottomLineStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">?attr/color_c1f1e1e1e_c1fffffff</item>
    </style>
    <!-- 从底部弹出 -->
    <style name="popupAnimStyleBottom">
        <item name="android:windowEnterAnimation">@anim/popup_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/popup_bottom_out</item>
    </style>

    <style name="popupAnimStyleRight">
        <item name="android:windowEnterAnimation">@anim/popup_right_in</item>
        <item name="android:windowExitAnimation">@anim/popup_right_out</item>
    </style>

    <style name="UpdateDialog" parent="@android:style/Theme.Dialog">
        <!--是否有边框-->
        <item name="android:windowFrame">@null</item>
        <!--提示框是否是浮动的-->
        <item name="android:windowIsFloating">true</item>
        <!--提示框是滞是透明的 -->
        <item name="android:windowIsTranslucent">false</item>
        <!--提示框是否有标题-->
        <item name="android:windowNoTitle">true</item>
        <!--背景颜色是什么-->
        <item name="android:windowBackground">@color/transparent</item>
        <!--对话框的背景变暗。为true则充许变暗-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <declare-styleable name="TabLayout">
        <attr name="tabLineOffset" format="dimension" />
    </declare-styleable>
    <!--  设置宽高比 ImageView  -->
    <declare-styleable name="ScaleImageView">
        <attr name="siv_scale_mode">
            <enum name="fixed_width" value="0" />
            <enum name="fixed_height" value="1" />
        </attr>
        <attr name="siv_scale" format="string" />
    </declare-styleable>

    <style name="commonDialog" parent="@android:style/Theme.Dialog">
        <!--是否有边框-->
        <item name="android:windowFrame">@null</item>
        <!--提示框是否是浮动的-->
        <item name="android:windowIsFloating">true</item>
        <!--提示框是滞是透明的 -->
        <item name="android:windowIsTranslucent">false</item>
        <!--提示框是否有标题-->
        <item name="android:windowNoTitle">true</item>
        <!--背景颜色是什么-->
        <item name="android:windowBackground">@color/transparent</item>
        <!--对话框的背景变暗。为true则充许变暗-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="circleImageStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="roundImageStyle10">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <style name="roundImageStyle6">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">6dp</item>
    </style>

    <style name="top_image_Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">10dp</item>
        <item name="cornerSizeTopRight">10dp</item>
    </style>
    <!--分割线-->
    <style name="cut_off_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">?attr/color_c1f1e1e1e_c1fffffff</item>
    </style>

    <style name="signal_profile_cut_off_line">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">?attr/color_c1f1e1e1e_c1fffffff</item>
    </style>

    <style name="txt_info_order_left">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_marginEnd">@dimen/margin_horizontal_base</item>
        <item name="android:gravity">center_vertical|start</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/gilroy_medium</item>
    </style>

    <style name="main_txt_start_theme">
        <item name="fontFamily">@font/gilroy_medium</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="person_signal_info_title">
        <item name="android:layout_width">180dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="fontFamily">@font/gilroy_medium</item>
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingBottom">13dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">16dp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginStart">@dimen/margin_horizontal_base</item>
    </style>

    <style name="person_signal_info_desc">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="fontFamily">@font/gilroy_medium</item>
        <item name="android:layout_marginEnd">@dimen/margin_horizontal_base</item>
        <item name="android:gravity">end|center_vertical</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">14dp</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAlignment">viewEnd</item>
    </style>

    <style name="main_bottom_button_theme">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/height_button_main</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">?attr/color_cebffffff_c1e1e1e</item>
        <item name="android:textSize">16dp</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/draw_shape_c1e1e1e_cebffffff_r100</item>
        <item name="android:layout_marginBottom">@dimen/margin_bottom_next_to_layout</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="fontFamily">@font/gilroy_semi_bold</item>
    </style>

    <declare-styleable name="WeeklyTrendBesselChart">
        <attr name="lineColor" format="color" />
        <attr name="strokewidth" format="float" />
        <attr name="jingLineColor" format="color" />
        <attr name="weiLineColor" format="color" />
    </declare-styleable>
    <!--字體-->
    <style name="bold_semi_font"><!--font-weight: 600-->
        <item name="fontFamily">@font/gilroy_semi_bold</item>
    </style>

    <style name="medium_font"><!--font-weight: 500-->
        <item name="fontFamily">@font/gilroy_medium</item>
    </style>

    <style name="regular_font"><!--font-weight: 400-->
        <item name="fontFamily">@font/gilroy_regular</item>
    </style>

    <style name="light_font">
        <item name="fontFamily">@font/gilroy_light</item>
    </style>

    <style name="gilroy_700">
        <item name="fontFamily">@font/gilroy_bold</item>
    </style>

    <style name="gilroy_600">
        <item name="fontFamily">@font/gilroy_semi_bold</item>
    </style>

    <style name="gilroy_500">
        <item name="fontFamily">@font/gilroy_medium</item>
    </style>

    <style name="gilroy_400">
        <item name="fontFamily">@font/gilroy_regular</item>
    </style>

    <style name="gilroy_300">
        <item name="fontFamily">@font/gilroy_light</item>
    </style>
    <!-- EditText支持rtl -->
    <style name="EditTextStyle.Rtl" parent="@android:style/Widget.EditText">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textDirection">firstStrongLtr</item> <!-- 设置光标固定在右侧 -->
    </style>
    <!--region 历史持仓-->
    <style name="time_filter_checkbox_style" parent="gilroy_500">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">@drawable/selector_time_filter</item>
        <item name="android:button">@null</item>
        <item name="android:textSize">12dp</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="position_list_item_title_value_style" parent="gilroy_600">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">2</item>
        <item name="android:textSize">14dp</item>
    </style>

    <style name="position_list_item_title_style" parent="gilroy_400">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">?attr/color_ca61e1e1e_c99ffffff</item>
        <item name="android:textSize">12dp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="position_detail_item_title_style" parent="gilroy_400">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">12dp</item>
        <item name="android:textColor">?attr/color_ca61e1e1e_c99ffffff</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textSize">14dp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="position_detail_item_value_style" parent="gilroy_500">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:gravity">end|center_vertical</item>
        <item name="android:textAlignment">viewEnd</item>
        <item name="android:textColor">?attr/color_c1e1e1e_cebffffff</item>
        <item name="android:textSize">14dp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">2</item>
    </style>
    <!--endregion-->
    <declare-styleable name="login_edit_text_base">
        <!--提示文案-->
        <attr name="text_hide" format="string" />
        <!--顶部提示文案-->
        <attr name="text_hide_top" format="string" />
        <!--顶部提示文本颜色-->
        <attr name="text_color_top" format="color" />
        <!--输入框输入文案颜色-->
        <attr name="text_edit_color" format="color" />
        <!--输入框提示文案颜色-->
        <attr name="text_edit_hide_color" format="color" />
        <!--padding-->
        <attr name="padding_top" format="dimension" />
        <attr name="padding_right" format="dimension" />
        <attr name="padding_bottom" format="dimension" />
        <attr name="padding_left" format="dimension" />
    </declare-styleable>
    <declare-styleable name="step_open_account">
        <attr name="step_num" format="integer" />
        <attr name="step_num_total" format="integer" />
    </declare-styleable>
    <declare-styleable name="view_text_serial">
        <attr name="serial_text" format="string" />
        <attr name="content_text" />
        <attr name="serial_text_color" format="color" />
        <attr name="content_max_lines" />
        <attr name="hyperlink_color" />
    </declare-styleable>
    <attr name="content_text" format="string" />
    <attr name="text_color" format="color" />
    <attr name="content_max_lines" format="integer" />
    <attr name="hyperlink_color" format="color" />
    <declare-styleable name="OpenAccount.Option.Text">
        <attr name="hint_text" format="string" />
        <attr name="title_text" format="string" />
        <attr name="must_fill" format="boolean" />
        <attr name="can_edit" format="boolean" />
        <attr name="show_arrow" format="boolean" />
        <attr name="popMode">
            <enum name="attach" value="0" />
            <enum name="bottom" value="1" />
        </attr>
    </declare-styleable>
    <declare-styleable name="OpenAccount.Permission.Text">
        <attr name="type" format="string" />
        <attr name="pass" format="boolean" />
        <attr name="hint_show" format="boolean" />
    </declare-styleable>

    <style name="dialog" parent="@android:style/Theme.Holo.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <!--阴影  -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--弹窗背景是否变暗-->
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="volume_brightness_theme" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowAnimationStyle">@style/volume_brightness_anim</item>
    </style>

    <style name="volume_brightness_anim" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <style name="MainTheme" parent="AppTheme">
        <item name="android:windowDisablePreview">true</item>
    </style>
    <!--垂直滚动-->
    <declare-styleable name="VerticalRollingLayout">
        <!--滚动方向-->
        <attr name="rolling_type" format="enum">
            <enum name="top2bottom" value="0" />
            <enum name="bottom2top" value="1" />
        </attr>
        <!--是否重复 default false-->
        <!--<attr name="repeat" format="boolean" />-->
        <!--滚动时间间隔  毫秒 millisecond  default 1000-->
        <attr name="rolling_interval" format="integer" />
        <!--动画时间-->
        <attr name="rolling_duration" format="integer" />
    </declare-styleable>
    <declare-styleable name="ClearAndHideEditText">
        <attr name="is_show" format="boolean" />
        <attr name="is_show_open" format="integer" />
        <attr name="is_show_close" format="integer" />
        <attr name="focus_res" format="reference|color" />
        <attr name="un_focus_res" format="reference|color" />
        <attr name="drawableStart" format="reference|color" />
        <attr name="drawableTop" format="reference|color" />
        <attr name="drawableEnd" format="reference|color" />
        <attr name="drawableBottom" format="reference|color" />
        <attr name="hint" format="string" />
        <attr name="drawableTint" format="color" />
        <attr name="rootBackground" format="reference|color" />
        <attr name="inputType">
            <!-- There is no content type.  The text is not editable. -->
            <flag name="none" value="0x00000000" />
            <!-- Just plain old text.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_NORMAL}. -->
            <flag name="text" value="0x00000001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request capitalization of all characters.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_CHARACTERS}. -->
            <flag name="textCapCharacters" value="0x00001001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request capitalization of the first character of every word.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_WORDS}. -->
            <flag name="textCapWords" value="0x00002001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request capitalization of the first character of every sentence.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_SENTENCES}. -->
            <flag name="textCapSentences" value="0x00004001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request auto-correction of text being input.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_AUTO_CORRECT}. -->
            <flag name="textAutoCorrect" value="0x00008001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 specify that this field will be doing its own auto-completion and
                 talking with the input method appropriately.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_AUTO_COMPLETE}. -->
            <flag name="textAutoComplete" value="0x00010001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 allow multiple lines of text in the field.  If this flag is not set,
                 the text field will be constrained to a single line.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_MULTI_LINE}.

                 Note: If this flag is not set and the text field doesn't have max length limit, the
                 framework automatically set maximum length of the characters to 5000 for the
                 performance reasons.
                 -->
            <flag name="textMultiLine" value="0x00020001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 indicate that though the regular text view should not be multiple
                 lines, the IME should provide multiple lines if it can.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_IME_MULTI_LINE}. -->
            <flag name="textImeMultiLine" value="0x00040001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 indicate that the IME should not show any
                 dictionary-based word suggestions.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_NO_SUGGESTIONS}. -->
            <flag name="textNoSuggestions" value="0x00080001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 indicate that if there is extra information, the IME should provide
                 {@link android.view.inputmethod.TextAttribute}.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_ENABLE_TEXT_CONVERSION_SUGGESTIONS}. -->
            <flag name="textEnableTextConversionSuggestions" value="0x00100001" />
            <!-- Text that will be used as a URI.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_URI}. -->
            <flag name="textUri" value="0x00000011" />
            <!-- Text that will be used as an e-mail address.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_EMAIL_ADDRESS}. -->
            <flag name="textEmailAddress" value="0x00000021" />
            <!-- Text that is being supplied as the subject of an e-mail.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_EMAIL_SUBJECT}. -->
            <flag name="textEmailSubject" value="0x00000031" />
            <!-- Text that is the content of a short message.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_SHORT_MESSAGE}. -->
            <flag name="textShortMessage" value="0x00000041" />
            <!-- Text that is the content of a long message.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_LONG_MESSAGE}. -->
            <flag name="textLongMessage" value="0x00000051" />
            <!-- Text that is the name of a person.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_PERSON_NAME}. -->
            <flag name="textPersonName" value="0x00000061" />
            <!-- Text that is being supplied as a postal mailing address.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_POSTAL_ADDRESS}. -->
            <flag name="textPostalAddress" value="0x00000071" />
            <!-- Text that is a password.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_PASSWORD}. -->
            <flag name="textPassword" value="0x00000081" />
            <!-- Text that is a password that should be visible.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_VISIBLE_PASSWORD}. -->
            <flag name="textVisiblePassword" value="0x00000091" />
            <!-- Text that is being supplied as text in a web form.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_EDIT_TEXT}. -->
            <flag name="textWebEditText" value="0x000000a1" />
            <!-- Text that is filtering some other data.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_FILTER}. -->
            <flag name="textFilter" value="0x000000b1" />
            <!-- Text that is for phonetic pronunciation, such as a phonetic name
                 field in a contact entry.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_PHONETIC}. -->
            <flag name="textPhonetic" value="0x000000c1" />
            <!-- Text that will be used as an e-mail address on a web form.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_EMAIL_ADDRESS}. -->
            <flag name="textWebEmailAddress" value="0x000000d1" />
            <!-- Text that will be used as a password on a web form.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_PASSWORD}. -->
            <flag name="textWebPassword" value="0x000000e1" />
            <!-- A numeric only field.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_VARIATION_NORMAL}. -->
            <flag name="number" value="0x00000002" />
            <!-- Can be combined with <var>number</var> and its other options to
                 allow a signed number.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_FLAG_SIGNED}. -->
            <flag name="numberSigned" value="0x00001002" />
            <!-- Can be combined with <var>number</var> and its other options to
                 allow a decimal (fractional) number.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_FLAG_DECIMAL}. -->
            <flag name="numberDecimal" value="0x00002002" />
            <!-- A numeric password field.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_VARIATION_PASSWORD}. -->
            <flag name="numberPassword" value="0x00000012" />
            <!-- For entering a phone number.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_PHONE}. -->
            <flag name="phone" value="0x00000003" />
            <!-- For entering a date and time.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_DATETIME} |
                 {@link android.text.InputType#TYPE_DATETIME_VARIATION_NORMAL}. -->
            <flag name="datetime" value="0x00000004" />
            <!-- For entering a date.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_DATETIME} |
                 {@link android.text.InputType#TYPE_DATETIME_VARIATION_DATE}. -->
            <flag name="date" value="0x00000014" />
            <!-- For entering a time.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_DATETIME} |
                 {@link android.text.InputType#TYPE_DATETIME_VARIATION_TIME}. -->
            <flag name="time" value="0x00000024" />
        </attr>
        <attr name="imeOptions">
            <!-- There are no special semantics associated with this editor. -->
            <flag name="normal" value="0x00000000" />
            <!-- There is no specific action associated with this editor, let the
                 editor come up with its own if it can.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_NULL}. -->
            <flag name="actionUnspecified" value="0x00000000" />
            <!-- This editor has no action associated with it.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_NONE}. -->
            <flag name="actionNone" value="0x00000001" />
            <!-- The action key performs a "go"
                 operation to take the user to the target of the text they typed.
                 Typically used, for example, when entering a URL.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_GO}. -->
            <flag name="actionGo" value="0x00000002" />
            <!-- The action key performs a "search"
                 operation, taking the user to the results of searching for the text
                 the have typed (in whatever context is appropriate).
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_SEARCH}. -->
            <flag name="actionSearch" value="0x00000003" />
            <!-- The action key performs a "send"
                 operation, delivering the text to its target.  This is typically used
                 when composing a message.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_SEND}. -->
            <flag name="actionSend" value="0x00000004" />
            <!-- The action key performs a "next"
                 operation, taking the user to the next field that will accept text.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_NEXT}. -->
            <flag name="actionNext" value="0x00000005" />
            <!-- The action key performs a "done"
                 operation, closing the soft input method.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_DONE}. -->
            <flag name="actionDone" value="0x00000006" />
            <!-- The action key performs a "previous"
                 operation, taking the user to the previous field that will accept text.
                 Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_ACTION_PREVIOUS}. -->
            <flag name="actionPrevious" value="0x00000007" />
            <!-- Used to request that the IME should not update any personalized data such as typing
                 history and personalized language model based on what the user typed on this text
                 editing object. Typical use cases are:
                 <ul>
                     <li>When the application is in a special mode, where user's activities are expected
                     to be not recorded in the application's history. Some web browsers and chat
                     applications may have this kind of modes.</li>
                     <li>When storing typing history does not make much sense.  Specifying this flag in
                     typing games may help to avoid typing history from being filled up with words that
                     the user is less likely to type in their daily life.  Another example is that when
                     the application already knows that the expected input is not a valid word (e.g. a
                     promotion code that is not a valid word in any natural language).</li>
                 </ul>
                 <p>Applications need to be aware that the flag is not a guarantee, and some IMEs may
                 not respect it.</p> -->
            <flag name="flagNoPersonalizedLearning" value="0x1000000" />
            <!-- Used to request that the IME never go
                 into fullscreen mode.  Applications need to be aware that the flag is not
                 a guarantee, and not all IMEs will respect it.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_NO_FULLSCREEN}. -->
            <flag name="flagNoFullscreen" value="0x2000000" />
            <!-- Like flagNavigateNext, but
                 specifies there is something interesting that a backward navigation
                 can focus on.  If the user selects the IME's facility to backward
                 navigate, this will show up in the application as an actionPrevious
                 at {@link android.view.inputmethod.InputConnection#performEditorAction(int)
                 InputConnection.performEditorAction(int)}.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_NAVIGATE_PREVIOUS}. -->
            <flag name="flagNavigatePrevious" value="0x4000000" />
            <!-- Used to specify that there is something
                 interesting that a forward navigation can focus on. This is like using
                 actionNext, except allows the IME to be multiline (with
                 an enter key) as well as provide forward navigation.  Note that some
                 IMEs may not be able to do this, especially when running on a small
                 screen where there is little space.  In that case it does not need to
                 present a UI for this option.  Like actionNext, if the
                 user selects the IME's facility to forward navigate, this will show up
                 in the application at
                 {@link android.view.inputmethod.InputConnection#performEditorAction(int)
                 InputConnection.performEditorAction(int)}.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_NAVIGATE_NEXT}. -->
            <flag name="flagNavigateNext" value="0x8000000" />
            <!-- Used to specify that the IME does not need
                 to show its extracted text UI.  For input methods that may be fullscreen,
                 often when in landscape mode, this allows them to be smaller and let part
                 of the application be shown behind.  Though there will likely be limited
                 access to the application available from the user, it can make the
                 experience of a (mostly) fullscreen IME less jarring.  Note that when
                 this flag is specified the IME may <em>not</em> be set up to be able
                 to display text, so it should only be used in situations where this is
                 not needed.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_NO_EXTRACT_UI}. -->
            <flag name="flagNoExtractUi" value="0x10000000" />
            <!-- Used in conjunction with a custom action, this indicates that the
                 action should not be available as an accessory button when the
                 input method is full-screen.
                 Note that by setting this flag, there can be cases where the action
                 is simply never available to the user.  Setting this generally means
                 that you think showing text being edited is more important than the
                 action you have supplied.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_NO_ACCESSORY_ACTION}. -->
            <flag name="flagNoAccessoryAction" value="0x20000000" />
            <!-- Used in conjunction with a custom action,
                 this indicates that the action should not be available in-line as
                 a replacement for the "enter" key.  Typically this is
                 because the action has such a significant impact or is not recoverable
                 enough that accidentally hitting it should be avoided, such as sending
                 a message.    Note that {@link android.widget.TextView} will
                 automatically set this flag for you on multi-line text views.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_NO_ENTER_ACTION}. -->
            <flag name="flagNoEnterAction" value="0x40000000" />
            <!-- Used to request that the IME should be capable of inputting ASCII
                 characters.  The intention of this flag is to ensure that the user
                 can type Roman alphabet characters in a {@link android.widget.TextView}
                 used for, typically, account ID or password input.  It is expected that IMEs
                 normally are able to input ASCII even without being told so (such IMEs
                 already respect this flag in a sense), but there could be some cases they
                 aren't when, for instance, only non-ASCII input languages like Arabic,
                 Greek, Hebrew, Russian are enabled in the IME.  Applications need to be
                 aware that the flag is not a guarantee, and not all IMEs will respect it.
                 However, it is strongly recommended for IME authors to respect this flag
                 especially when their IME could end up with a state that has only non-ASCII
                 input languages enabled.
                 <p>Corresponds to
                 {@link android.view.inputmethod.EditorInfo#IME_FLAG_FORCE_ASCII}. -->
            <flag name="flagForceAscii" value="0x80000000" />
        </attr>
        <attr name="digits" format="string" />
        <attr name="textColor" format="reference|color" />
        <attr name="textColorHint" format="reference|color" />
        <attr name="etTextSize" format="integer" />
        <attr name="etFontFamily" format="enum">
            <enum name="bold" value="0" />
            <enum name="medium" value="1" />
            <enum name="regular" value="2" />
            <enum name="light" value="3" />
            <enum name="semi_bold" value="4" />
        </attr>
    </declare-styleable>

    <style name="BottomSheetAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_out_bottom</item>
    </style>
    <!-- 登录注册模块，下一步按钮的样式 -->
    <style name="login_tvNext_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/height_button_main</item>
        <item name="android:background">@drawable/selector_login_btn_bg</item>
        <item name="android:ellipsize">end</item>
        <item name="android:enabled">false</item>
        <item name="android:maxLines">1</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/selector_login_btn_tv_color</item>
        <item name="android:textSize">16dp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="fontFamily">@font/gilroy_semi_bold</item>
    </style>

    <style name="BottomDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowAnimationStyle">@style/BottomSheetAnimation</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
</resources>
