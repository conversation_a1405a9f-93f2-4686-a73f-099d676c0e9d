<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        style="@style/DialogBottomContentStyle"
        android:layout_width="match_parent"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Please confirm to close all selected orders at current market price. Any orders that are from a closed market will not be affected."
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSubTitle"
        style="@style/DialogBottomSubTitleStyle"
        tools:text="Select Account"
        app:layout_goneMarginTop="0dp"
        android:layout_marginTop="12dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvContent"
        tools:visibility="gone"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSubContent"
        style="@style/DialogBottomSubContentStyle"
        android:layout_width="match_parent"
        android:layout_marginTop="4dp"
        app:layout_goneMarginTop="12dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
        tools:text="Please confirm to close all selected orders at current market price. Any orders that are from a closed market will not be affected."
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>

