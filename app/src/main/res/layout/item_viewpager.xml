<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/c034854">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/bitmap_img_welcome_top" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="66dp"
        android:textColor="@color/cffffff"
        android:textSize="36dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivTop"
        tools:text="Trade better" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="66dp"
        android:gravity="center"
        android:textColor="@color/cffffff"
        android:textSize="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="Lower trading costs peace of mind" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLogo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="170dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:srcCompat="@drawable/img_logo_d" />

    <View
        android:id="@+id/viewIndicator1"
        android:layout_width="3dp"
        android:layout_height="2dp"
        android:layout_marginTop="45dp"
        android:background="@drawable/draw_shape_c61ffffff_r2"
        app:layout_constraintEnd_toStartOf="@+id/viewIndicator2"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivLogo" />

    <View
        android:id="@+id/viewIndicator2"
        android:layout_width="3dp"
        android:layout_height="2dp"
        android:layout_marginHorizontal="2dp"
        android:layout_marginTop="45dp"
        android:background="@drawable/draw_shape_c61ffffff_r2"
        app:layout_constraintEnd_toStartOf="@+id/viewIndicator3"
        app:layout_constraintStart_toEndOf="@+id/viewIndicator1"
        app:layout_constraintTop_toBottomOf="@+id/ivLogo" />

    <View
        android:id="@+id/viewIndicator3"
        android:layout_width="3dp"
        android:layout_height="2dp"
        android:layout_marginTop="45dp"
        android:background="@drawable/draw_shape_c61ffffff_r2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/viewIndicator2"
        app:layout_constraintTop_toBottomOf="@+id/ivLogo" />

</androidx.constraintlayout.widget.ConstraintLayout>