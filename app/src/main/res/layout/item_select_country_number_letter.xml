<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/viewTopLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="?attr/color_c331e1e1e_c33ffffff" />

    <TextView
        android:id="@+id/tvItemLetter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/margin_horizontal_base"
        android:paddingVertical="8dp"
        android:paddingEnd="0dp"
        android:textColor="@color/ce35728"
        android:textSize="14dp"
        style="@style/gilroy_600"
        tools:text="Popular" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="?attr/color_c331e1e1e_c33ffffff" />

</LinearLayout>
