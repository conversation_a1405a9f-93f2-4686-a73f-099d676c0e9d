<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tvViewMore"
    style="@style/gilroy_400"
    android:layout_width="104dp"
    android:layout_height="match_parent"
    android:layout_marginEnd="@dimen/margin_horizontal_base"
    android:background="@drawable/draw_main_card"
    android:gravity="center"
    android:text="@string/view_more"
    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
    android:textSize="12dp"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />
