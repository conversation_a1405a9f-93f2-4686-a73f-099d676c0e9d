<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="isMt5"
            type="Boolean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvReviewTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/gilroy_600"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:text="@string/money_in_pending_review_status"/>

                <TextView
                    android:id="@+id/tvReviewContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginBottom="@dimen/margin_vertical_button"
                    android:text="@string/the_money_allocated_please_review_tab"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvEquityTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/equity"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvEquityContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/equity_mt4_formula"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvPnlTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/floating_pnl"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvPnlContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/the_profit_and_open_positions_including_swap_rates"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvMarginLevelTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/margin_level"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvMarginLevelContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/margin_level_mt4_formula"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <cn.com.vau.common.view.system.LinkSpanTextView
                    android:id="@+id/tvLearnMore"
                    android:fontFamily="@font/gilroy_regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/learn_more"
                    android:textColor="@color/ce35728"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvCreditTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/credit"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvCreditContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/the_amount_of_be_withdrawn"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvBalanceTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/balance"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvBalanceContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/the_total_amount_not_open_positions"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvMarginTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/margin"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvMarginContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/a_portion_of_open_position"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvFreeMarginTitle"
                    style="@style/gilroy_600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_vertical_button"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/free_margin"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tvFreeMarginContent"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/free_margin_mt4_formula"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</layout>