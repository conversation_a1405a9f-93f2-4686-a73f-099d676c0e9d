<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingConstraints"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <TextView
        android:id="@+id/tvEquityTitle"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:text="@string/equity"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvEquity"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:text="..."
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="26dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvEquityTitle"
        tools:ignore="HardcodedText"
        tools:text="0.00" />

    <TextView
        android:id="@+id/tvCurrency"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:gravity="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@id/tvEquity"
        app:layout_constraintBottom_toBottomOf="@+id/tvEquity"
        app:layout_constraintStart_toEndOf="@+id/tvEquity"
        tools:text="USD" />

    <TextView
        android:id="@+id/tvDepositTip"
        style="@style/gilroy_700"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/deposit_to_start_trading_journey"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvEquity" />

    <TextView
        android:id="@+id/tvDepositTip2"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:text="@string/access_1000_global_assets"
        android:textColor="@color/ce35728"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDepositTip" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeposit"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginVertical="@dimen/margin_vertical_base"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="10dp"
        android:text="@string/deposit"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        app:autoSizeMaxTextSize="16dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeStepGranularity="1dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDepositTip2" />

</merge>