<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clVolume"
        android:layout_width="0dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/select_open_order_et_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSub"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="?attr/icon2Sub"
        android:paddingHorizontal="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivAdd"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
        android:paddingHorizontal="12dp"
        app:layout_constraintEnd_toStartOf="@id/line1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tvVolumeTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/volume"
        android:textSize="14dp"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toTopOf="@id/etVolume"
        app:layout_constraintStart_toEndOf="@id/ivSub"
        app:layout_constraintEnd_toStartOf="@id/ivAdd"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etVolume"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="4dp"
        android:background="@null"
        android:inputType="numberDecimal"
        android:singleLine="true"
        android:gravity="center"
        android:visibility="gone"
        android:textAlignment="center"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivSub"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintEnd_toStartOf="@id/ivAdd"
        app:layout_constraintTop_toBottomOf="@id/tvVolumeTitle"
        tools:visibility="visible"/>

    <View
        android:id="@+id/line1"
        android:layout_width="1dp"
        android:layout_height="20dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvLots"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLots"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:text="@string/lots"
        android:textSize="12dp"
        style="@style/gilroy_500"
        android:gravity="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:paddingEnd="32dp"
        android:paddingStart="12dp"
        app:layout_constraintEnd_toEndOf="@id/ivVolumeDown"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivVolumeDown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="?attr/icon2ArrowBottom"
        android:paddingHorizontal="12dp"
        android:paddingVertical="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvTransfer"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:paddingHorizontal="6dp"
        android:textSize="10dp"
        android:gravity="center"
        android:textAlignment="center"
        android:layout_marginBottom="2dp"
        android:textColor="?color_cebffffff_c1e1e1e"
        android:background="@drawable/draw_shape_c262930_cffffff_r4"
        app:layout_constraintStart_toStartOf="@id/clVolume"
        app:layout_constraintBottom_toTopOf="@id/clVolume"
        android:visibility="gone"
        tools:text="≈ 1000 USD"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/tvVolumeTip"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:textColor="@color/cf44040"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clVolume"
        tools:text="Max value: 250000.00"
        tools:visibility="visible"/>

    <cn.com.vau.common.view.custom.VolSeekBar
        android:id="@+id/volSeekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="-12dp"
        android:paddingBottom="9dp"
        app:layout_constraintTop_toBottomOf="@id/tvVolumeTip" />

    <TextView
        android:id="@+id/tvMinOpen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0"
        style="@style/gilroy_500"
        android:textSize="12dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        app:layout_constraintTop_toBottomOf="@id/volSeekBar"
        app:layout_constraintStart_toStartOf="@id/volSeekBar"
        tools:ignore="HardcodedText" />

    <TextView
        android:id="@+id/tvMaxOpen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        style="@style/gilroy_500"
        android:text="--"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        app:layout_constraintTop_toBottomOf="@id/volSeekBar"
        app:layout_constraintEnd_toEndOf="@id/volSeekBar"
        tools:text="Max open 2.50"
        tools:ignore="HardcodedText" />
</merge>