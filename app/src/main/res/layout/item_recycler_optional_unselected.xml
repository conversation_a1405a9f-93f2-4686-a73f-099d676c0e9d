<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvName"
            style="@style/regular_font"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingEnd="10dp"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="14dp"
            tool:text="CADUSD+" />

        <ImageView
            android:id="@+id/ivAdd"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="20dp"
            android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_gravity="bottom"
        android:background="?attr/color_c1f1e1e1e_c1fffffff" />

</FrameLayout>
