<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlTop"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivEvent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/color_c731e1e1e_c61ffffff"
            android:scaleType="fitXY" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ifvBack"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="8dp"
            android:padding="9dp"
            android:src="@drawable/icon_source2_close_16x16"
            android:tint="@color/c61ffffff"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTc"
            style="@style/gilroy_400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:layout_marginBottom="@dimen/margin_horizontal_base"
            android:autoLink="all"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/tcs_event_newcomer"
            android:textColor="@color/cffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ctlTop">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:minHeight="20dp"
            android:paddingBottom="22dp"
            app:layout_constraintTop_toTopOf="@id/ivEvent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlTimeLimitedPromo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_main_card"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:paddingTop="12dp"
                android:paddingBottom="20dp"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvTimeLimitedPromo"
                    style="@style/bold_semi_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Time-Limited Promo"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    style="@style/bold_semi_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="123:23:34"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="20dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tvTimeLimitedPromo"
                    app:layout_constraintEnd_toEndOf="parent" />

                <TextView
                    android:id="@+id/tvTimeLimitedPromoContent"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    tools:text="活动文案活动文案"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvTimeLimitedPromo" />

                <TextView
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
                    android:paddingStart="30dp"
                    android:paddingTop="6dp"
                    android:paddingEnd="30dp"
                    android:paddingBottom="8dp"
                    android:text="@string/button_claim"
                    android:textColor="?attr/color_cebffffff_c1e1e1e"
                    android:textSize="14dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tvTimeLimitedPromoContent"
                    app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@+id/ctlTimeLimitedPromo">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlOpenAccount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/draw_main_card"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvOpenAccountTitle"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        android:paddingStart="@dimen/margin_horizontal_base"
                        android:paddingEnd="@dimen/margin_horizontal_base"
                        android:text="@string/button_live"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvOpenAccountInfo"
                        style="@style/medium_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/participate_in_one_of_them"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenAccountTitle"
                        app:layout_constraintStart_toEndOf="@+id/tvOpenAccountTitle"
                        tools:visibility="visible" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/openRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                        app:layout_constraintTop_toBottomOf="@+id/tvOpenAccountTitle" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlDeposit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/draw_main_card"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/ctlOpenAccount"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvDepositTitle"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        android:paddingStart="@dimen/margin_horizontal_base"
                        android:paddingEnd="@dimen/margin_horizontal_base"
                        android:text="@string/button_deposit"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvDepositInfo"
                        style="@style/medium_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/participate_in_one_of_them"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvDepositTitle"
                        app:layout_constraintStart_toEndOf="@+id/tvDepositTitle"
                        tools:visibility="visible" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/depositRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                        app:layout_constraintTop_toBottomOf="@+id/tvDepositTitle" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlTrade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/draw_main_card"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/ctlDeposit">

                    <TextView
                        android:id="@+id/tvTradeTitle"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        android:paddingStart="@dimen/margin_horizontal_base"
                        android:paddingEnd="@dimen/margin_horizontal_base"
                        android:text="@string/signal_trade"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvTradeInfo"
                        style="@style/medium_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/participate_in_one_of_them"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvTradeTitle"
                        app:layout_constraintStart_toEndOf="@+id/tvTradeTitle" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/tradeRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                        app:layout_constraintTop_toBottomOf="@+id/tvTradeTitle" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlRefer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/draw_main_card"
                    android:paddingTop="12dp"
                    android:paddingBottom="4dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/ctlTrade">

                    <TextView
                        android:id="@+id/tvReferTitle"
                        style="@style/bold_semi_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        android:paddingStart="@dimen/margin_horizontal_base"
                        android:paddingEnd="@dimen/margin_horizontal_base"
                        android:text="@string/invite"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="18dp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvReferInfo"
                        style="@style/medium_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/participate_in_one_of_them"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvReferTitle"
                        app:layout_constraintStart_toEndOf="@+id/tvReferTitle" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/referRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_horizontal_base"
                        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
                        app:layout_constraintTop_toBottomOf="@+id/tvReferTitle" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>