<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="MissingConstraints">

    <TextView
        android:id="@+id/tvNewMessage"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="10dp"
        android:text="@string/you_received_a_piece_of_the_latest_news"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="10dp"
        android:visibility="gone"
        tools:visibility="visible" />

    <include
        android:id="@+id/layoutRefresh"
        layout="@layout/fragment_refresh" />

</LinearLayout>