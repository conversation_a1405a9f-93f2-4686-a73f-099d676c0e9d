<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvIndex"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:minWidth="20dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1." />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvQuestion"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvIndex"
        app:layout_constraintTop_toTopOf="@+id/tvIndex"
        tools:text="When you trade OTC Derivative products ?" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tvAnswer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvQuestion"
        app:layout_constraintTop_toBottomOf="@+id/tvQuestion" />

    <View
        style="@style/cut_off_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/tvAnswer" />

</androidx.constraintlayout.widget.ConstraintLayout>