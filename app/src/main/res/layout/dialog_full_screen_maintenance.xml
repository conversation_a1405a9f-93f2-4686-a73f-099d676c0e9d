<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/mainLayoutBg"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    android:paddingBottom="50dp"
    android:translationZ="10dp">

    <ImageView
        android:id="@+id/mImageView"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="16dp"
        android:contentDescription="@string/app_name"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tool:src="?attr/bgServerMaintenance" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:paddingBottom="20dp"
        android:text="@string/server_maintenance"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/guideline_h50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvContentTitle"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingBottom="6dp"
        android:text="@string/app_maintenance_from"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        />

    <TextView
        android:id="@+id/tvContent"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:paddingBottom="16dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvContentTitle"
        tool:text="1st April 08:00 to 2nd April 23:00 (GMT +8) 2023" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvContentTitle,tvContent"
        tool:visibility="visible" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/linkTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gilroy_regular"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/dialog_maintenance_customer_service_msg_x"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvContent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_h50"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>
