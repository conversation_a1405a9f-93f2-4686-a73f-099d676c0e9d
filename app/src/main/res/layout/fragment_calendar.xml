<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvDate"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginTop="@dimen/margin_vertical_button"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:drawablePadding="4dp"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="24/05/2024" />

        <TextView
            android:id="@+id/tvFilter"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:text="@string/filters"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@+id/mRecyclerView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0px"
            android:layout_marginTop="@dimen/margin_vertical_button"
            app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDate" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>
