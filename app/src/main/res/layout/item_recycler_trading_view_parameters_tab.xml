<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.AppCompatTextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tabTitle"
    style="@style/gilroy_600"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/margin_horizontal_base"
    android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
    android:button="@null"
    android:checked="true"
    android:gravity="center"
    android:paddingHorizontal="8dp"
    android:paddingVertical="4dp"
    android:textColor="?attr/color_cebffffff_c1e1e1e"
    android:textSize="11dp"
    tools:text="MA" />