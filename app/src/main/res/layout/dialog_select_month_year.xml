<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingStart="26dp"
    android:paddingTop="36dp"
    android:paddingEnd="26dp"
    android:paddingBottom="36dp">

    <TextView
        android:id="@+id/tvCancel"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:text="@string/expiration_date"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="22dp"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/tvFinish"
        app:layout_constraintTop_toTopOf="@+id/tvFinish"
        tools:ignore="SpUsage" />

    <TextView
        style="@style/medium_font"
        android:id="@+id/tvFinish"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:textColor="@color/ce35728"
        android:textSize="16dp"
        android:text="@string/finish"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <cn.com.vau.common.view.timeSelection.PickerView
        android:id="@+id/pvMonth"
        android:layout_width="0dp"
        android:layout_height="150dp"
        android:layout_marginTop="25dp"
        app:layout_constraintEnd_toStartOf="@+id/pvYear"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvFinish" />

    <cn.com.vau.common.view.timeSelection.PickerView
        android:id="@+id/pvYear"
        android:layout_width="0dp"
        android:layout_marginTop="25dp"
        android:layout_height="150dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/pvMonth"
        app:layout_constraintTop_toBottomOf="@+id/tvFinish" />

</androidx.constraintlayout.widget.ConstraintLayout>
