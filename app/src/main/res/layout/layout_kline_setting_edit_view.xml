<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle1"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/etValue1"
        app:layout_constraintStart_toStartOf="@id/tabRV"
        app:layout_constraintTop_toTopOf="@+id/etValue1"
        tools:text="EMA1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue1"
        app:layout_constraintStart_toEndOf="@+id/tvTitle1"
        app:layout_constraintTop_toTopOf="@+id/etValue1" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue1"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountUp1"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown1"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountUp1"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue1"
        app:layout_constraintStart_toEndOf="@+id/etValue1"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitle1,ivHandCountDown1,etValue1,ivHandCountUp1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle2"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/etValue2"
        app:layout_constraintTop_toTopOf="@+id/etValue2"
        tools:text="EMA1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue2"
        app:layout_constraintStart_toEndOf="@+id/tvTitle2"
        app:layout_constraintTop_toTopOf="@+id/etValue2" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue2"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountUp2"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown2"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountUp2"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="8dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue2"
        app:layout_constraintStart_toEndOf="@+id/etValue2"
        app:layout_constraintTop_toBottomOf="@+id/ivHandCountUp1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="tvTitle2,ivHandCountDown2,etValue2,ivHandCountUp2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle3"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/etValue3"
        app:layout_constraintTop_toTopOf="@+id/etValue3"
        tools:text="EMA1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue3"
        app:layout_constraintStart_toEndOf="@+id/tvTitle3"
        app:layout_constraintTop_toTopOf="@+id/etValue3" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue3"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountUp3"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown3"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountUp3"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="8dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue3"
        app:layout_constraintStart_toEndOf="@+id/etValue3"
        app:layout_constraintTop_toBottomOf="@+id/ivHandCountUp2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="tvTitle3,ivHandCountDown3,etValue3,ivHandCountUp3" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNoConfig"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/this_indicator_doesn_any_configuration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountUp3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</merge>

