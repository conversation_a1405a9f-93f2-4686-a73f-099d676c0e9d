<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.com.vau.trade.fragment.deal.DealItemOptionalFragment">

    <cn.com.vau.common.view.RefreshSlideLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <cn.com.vau.common.view.system.MyRecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </cn.com.vau.common.view.RefreshSlideLayout>

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/vs_layout_no_data_scroll" />

</androidx.constraintlayout.widget.ConstraintLayout>