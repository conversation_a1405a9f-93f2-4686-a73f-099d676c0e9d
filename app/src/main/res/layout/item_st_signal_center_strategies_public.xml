<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/padding_card_base"
    android:paddingBottom="@dimen/padding_bottom_button_card"
    tools:ignore="ContentDescription,HardcodedText">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="2dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvNick"
        app:shapeAppearanceOverlay="@style/roundImageStyle6" />

    <TextView
        android:id="@+id/tvNick"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/ivShare"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Strategy Name" />

    <TextView
        android:id="@+id/tvStrategyId"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintStart_toStartOf="@+id/tvNick"
        tools:text="Strategy ID：123456" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShare"
        android:layout_width="18dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:paddingVertical="5dp"
        android:src="@drawable/draw_bitmap2_share_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar" />

    <View
        android:id="@+id/viewLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="16dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvStrategyId" />

    <FrameLayout
        android:id="@+id/flActiveCopiers"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="3dp"
        app:layout_constraintEnd_toStartOf="@+id/tvKey2"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@+id/viewLine">

        <TextView
            android:id="@+id/tvKey1"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="3dp"
            android:drawablePadding="4dp"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:maxLines="1"
            android:text="@string/active_copiers"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
    </FrameLayout>

    <TextView
        android:id="@+id/tvCurrentCopiers"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="@+id/flActiveCopiers"
        app:layout_constraintTop_toBottomOf="@+id/flActiveCopiers"
        tools:text="33" />

    <TextView
        android:id="@+id/tvKey2"
        style="@style/gilroy_500"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="3dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/settlement"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvKey3"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/flActiveCopiers"
        app:layout_constraintTop_toTopOf="@+id/flActiveCopiers" />

    <TextView
        android:id="@+id/tvSettlement"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="@+id/tvKey2"
        app:layout_constraintStart_toStartOf="@+id/tvKey2"
        app:layout_constraintTop_toTopOf="@+id/tvCurrentCopiers"
        tools:text="Weekly" />

    <TextView
        android:id="@+id/tvKey3"
        style="@style/gilroy_500"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:text="@string/profit_sharing"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvKey2"
        app:layout_constraintTop_toTopOf="@+id/flActiveCopiers" />

    <TextView
        android:id="@+id/tvProfitSharing"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="@+id/tvKey3"
        app:layout_constraintTop_toTopOf="@+id/tvCurrentCopiers"
        tools:text="20%" />

    <LinearLayout
        android:id="@+id/llAum"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toStartOf="@+id/llTotalHistoricalPayout"
        app:layout_constraintStart_toStartOf="@+id/flActiveCopiers"
        app:layout_constraintTop_toBottomOf="@+id/tvCurrentCopiers">

        <TextView
            android:id="@+id/tvKey4"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="3dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/copy_aum"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/tvAum"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            tools:text="60000.00 USD" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llTotalHistoricalPayout"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="3dp"
        app:layout_constraintEnd_toStartOf="@+id/llUnpaidAmount"
        app:layout_constraintStart_toEndOf="@+id/llAum"
        app:layout_constraintTop_toTopOf="@+id/llAum">

        <TextView
            android:id="@+id/tvKey5"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/historical_payout"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/tvTotalHistoricalPayout"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            tools:text="100.00 USD" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llUnpaidAmount"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="@+id/tvKey3"
        app:layout_constraintStart_toEndOf="@+id/llTotalHistoricalPayout"
        app:layout_constraintTop_toTopOf="@+id/llAum">

        <TextView
            android:id="@+id/tvKey6"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/unpaid_amount"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/tvUnpaidAmount"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            tools:text="100.00 USD" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvEdit"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="32dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/edit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/tvDelistOrPublic"
        app:layout_constraintStart_toStartOf="@+id/flActiveCopiers"
        app:layout_constraintTop_toBottomOf="@+id/llAum" />

    <TextView
        android:id="@+id/tvDelistOrPublic"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/delist"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@+id/tvMore"
        app:layout_constraintStart_toEndOf="@+id/tvEdit"
        app:layout_constraintTop_toTopOf="@+id/tvEdit"
        app:layout_goneMarginEnd="0px" />

    <TextView
        android:id="@+id/tvMore"
        android:layout_width="74dp"
        android:layout_height="32dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="..."
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@+id/tvKey3"
        app:layout_constraintStart_toEndOf="@+id/tvDelistOrPublic"
        app:layout_constraintTop_toTopOf="@+id/tvEdit"
        tools:ignore="HardcodedText" />
</androidx.constraintlayout.widget.ConstraintLayout>