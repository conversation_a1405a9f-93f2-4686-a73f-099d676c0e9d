<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center"
        android:text="@string/two_factor_authentication"
        android:textAlignment="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivClose" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="ivClose,tvTitle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="28dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:padding="18dp"
        android:src="@drawable/img_source_2fa_bind"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_goneMarginTop="@dimen/margin_vertical_base" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPrompt1"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/when_enabled_we_authentication_2fa"
        android:textAlignment="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@id/tvPrompt1" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvPrompt2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:fontFamily="@font/gilroy_medium"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/tap_here_to_or_your_choice"
        android:textAlignment="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:text="@string/enable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvPromptBottom"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:fontFamily="@font/gilroy_regular"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/by_linking_an_you_the_disclaimer"
        android:textAlignment="center"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>