<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:elevation="0dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlAccountInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    android:background="@drawable/draw_main_card"
                    android:paddingHorizontal="@dimen/padding_horizontal_base"
                    android:paddingVertical="12dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivAccountInfoGlossary"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:contentDescription="@string/app_name"
                        android:scaleType="centerInside"
                        app:layout_constraintBottom_toBottomOf="@id/tvAccount"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tvAccount"
                        app:srcCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        tools:ignore="RtlSymmetry" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvAccount"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/equity"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvEquity"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:singleLine="true"
                        android:text="..."
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="26dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvAccount"
                        tools:text="1210.25" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCurrency"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="6dp"
                        android:paddingTop="6dp"
                        android:paddingEnd="0dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@+id/tvEquity"
                        app:layout_constraintStart_toEndOf="@+id/tvEquity"
                        app:layout_constraintTop_toTopOf="@+id/tvEquity"
                        tools:text="USD" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvFloatingPnLTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="@string/pnl"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvEquity" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvFloatingPnL"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="6dp"
                        android:singleLine="true"
                        android:text="..."
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toStartOf="@+id/guideline_t_v50"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvFloatingPnLTitle"
                        app:layout_goneMarginStart="0dp"
                        tools:text="-13.42" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvBalanceTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:text="@string/balance"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvFloatingPnLTitle"
                        app:layout_constraintStart_toEndOf="@+id/guideline_t_v50" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvBalance"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical|start"
                        android:singleLine="true"
                        android:text="..."
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="16dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvFloatingPnL"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@id/tvBalanceTitle"
                        tools:text="59,155.23" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCreditTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:drawablePadding="4dp"
                        android:text="@string/credit"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvFloatingPnL"
                        tools:ignore="UseCompatTextViewDrawableXml" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCredit"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:singleLine="true"
                        android:text="..."
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="16dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCreditTitle"
                        tools:text="1000 USD" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvMarginTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/free_margin"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvCreditTitle"
                        app:layout_constraintStart_toStartOf="@id/tvBalanceTitle" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvFreeMargin"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="..."
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="16dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
                        app:layout_constraintStart_toStartOf="@id/tvBalanceTitle"
                        tools:text="1000 USD" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline_t_v50"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.50" />

                    <!--                    <com.airbnb.lottie.LottieAnimationView-->
                    <!--                        android:id="@+id/ivLoading"-->
                    <!--                        android:layout_width="60dp"-->
                    <!--                        android:layout_height="60dp"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        app:layout_constraintBottom_toBottomOf="parent"-->
                    <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                    <!--                        app:layout_constraintStart_toStartOf="parent"-->
                    <!--                        app:layout_constraintTop_toTopOf="parent"-->
                    <!--                        app:lottie_loop="true"-->
                    <!--                        app:lottie_rawRes="@raw/loading"-->
                    <!--                        app:lottie_speed="1.5"-->
                    <!--                        tools:visibility="visible" />-->

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <cn.com.vau.common.view.tablayout.DslTabLayout
                android:id="@+id/mTabLayout"
                android:layout_width="match_parent"
                android:layout_height="33dp"
                android:layout_marginTop="8dp" />
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/llOrderTab"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <cn.com.vau.common.view.MultiNestedScrollableHost
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/mViewPager2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </cn.com.vau.common.view.MultiNestedScrollableHost>

        </LinearLayout>

        <!--<include
            android:id="@+id/layoutMarketMaintenance"
            layout="@layout/layout_market_maintenance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />-->
        <ViewStub
            android:id="@+id/mViewStubMarketMaintenance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout="@layout/layout_market_maintenance_new"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
