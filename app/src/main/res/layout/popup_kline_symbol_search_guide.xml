<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="HardcodedText">

    <View
        android:id="@+id/viewBg"
        android:layout_width="292dp"
        android:layout_height="0dp"
        android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c262930_solid_cffffff_c262930_r8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivTop" />

    <ImageView
        android:id="@+id/ivTop"
        android:layout_width="16dp"
        android:layout_height="8dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="-2dp"
        android:contentDescription="@string/app_name"
        android:src="?attr/imgTradesGuideTop"
        app:layout_constraintBottom_toTopOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:gravity="start"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:text="@string/search_launched_swap_symbol_instantly"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="14dp"
        android:paddingVertical="3dp"
        android:text="@string/confirm"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>