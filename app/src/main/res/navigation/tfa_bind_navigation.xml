<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tfa_bind_navigation"
    app:startDestination="@id/TFABindPromptFragment">

    <fragment
        android:id="@+id/TFABindPromptFragment"
        android:name="cn.com.vau.profile.activity.twoFactorAuth.fragment.TFABindPromptFragment"
        android:label="TFABindPromptFragment" />
    <fragment
        android:id="@+id/TFALinkFragment"
        android:name="cn.com.vau.profile.activity.twoFactorAuth.fragment.TFALinkFragment"
        android:label="TFALinkFragment" />
    <fragment
        android:id="@+id/TFAPwdFragment"
        android:name="cn.com.vau.profile.activity.twoFactorAuth.fragment.TFAPwdFragment"
        android:label="TFAPwdFragment" />
    <fragment
        android:id="@+id/TFAVerifyFragment"
        android:name="cn.com.vau.profile.activity.twoFactorAuth.fragment.TFAVerifyFragment"
        android:label="TFAVerifyFragment" />
    <fragment
        android:id="@+id/TFAResultFragment"
        android:name="cn.com.vau.profile.activity.twoFactorAuth.fragment.TFAResultFragment"
        android:label="TFAResultFragment" />
    <action
        android:id="@+id/action_global_TFABindPromptFragment"
        app:destination="@id/TFABindPromptFragment" />
    <action
        android:id="@+id/action_global_bind_TFALinkFragment"
        app:destination="@id/TFALinkFragment" />
    <action
        android:id="@+id/action_global_bind_TFAPwdFragment"
        app:destination="@id/TFAPwdFragment" />
    <action
        android:id="@+id/action_global_bind_TFAVerifyFragment"
        app:destination="@id/TFAVerifyFragment" />
    <action
        android:id="@+id/action_global_bind_TFAResultFragment"
        app:destination="@id/TFAResultFragment" />
</navigation>