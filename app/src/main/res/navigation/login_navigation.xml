<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/login_navigation"
    app:startDestination="@+id/loginFragment">

    <fragment
        android:id="@+id/loginFragment"
        android:name="cn.com.vau.page.user.login.LoginPwdFragment"
        android:label="fragment_register_first"
        tools:layout="@layout/fragment_login_pwd">

        <action
            android:id="@+id/actionForgetFirstPwd"
            app:destination="@id/forgetPwdFirstFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:launchSingleTop="true"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/actionLoginBind"
            app:destination="@id/loginBindFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_loginFragment_to_forgetPwdSecondFragment"
            app:destination="@id/forgetPwdSecondFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />

    </fragment>

    <fragment
        android:id="@+id/forgetPwdFirstFragment"
        android:name="cn.com.vau.page.user.login.ForgetPwdFirstFragment"
        android:label="fragment_forget_pwd_first"
        tools:layout="@layout/fragment_forget_pwd_first">
        <action
            android:id="@+id/actionForgetFirstPwdtoSecond"
            app:destination="@id/forgetPwdSecondFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <fragment
        android:id="@+id/loginBindFragment"
        android:name="cn.com.vau.page.user.loginBind.LoginBindFragment"
        android:label="fragment_login_bind"
        tools:layout="@layout/fragment_login_bind">
        <action
            android:id="@+id/action_bind_first_to_second"
            app:destination="@id/fragment_bind_second"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <fragment
        android:id="@+id/fragment_bind_second"
        android:name="cn.com.vau.page.user.loginBind.LoginBindSecondFragment"
        android:label="fragment_login_bind_second"
        tools:layout="@layout/fragment_login_bind_second" />

    <fragment
        android:id="@+id/forgetPwdSecondFragment"
        android:name="cn.com.vau.page.user.login.ForgetPwdSecondFragment"
        android:label="fragment_forget_pwd_second"
        tools:layout="@layout/fragment_forget_pwd_second">
        <action
            android:id="@+id/action_forget_second_to_third"
            app:destination="@id/fragment_forget_pwd_third"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_forgetPwdSecondFragment_to_loginBindFragment"
            app:destination="@id/loginBindFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <fragment
        android:id="@+id/fragment_forget_pwd_third"
        android:name="cn.com.vau.page.user.login.ForgetPwdThirdFragment"
        android:label="fragment_forget_pwd_third"
        tools:layout="@layout/fragment_forget_pwd_third" />

</navigation>