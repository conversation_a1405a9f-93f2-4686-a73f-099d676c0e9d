import{_ as a,r as s}from"./index-M11nEPjl.js";import{n as o}from"./navbar-DfgFEjpa.js";import{I as n,D as r,s as t,K as e,J as l,j as i,X as c}from"./vue-vendor-DjIN0JG5.js";const d={components:{Navbar:o},props:{loading:{type:Boolean,default:!1}},methods:{back(){s("501")}}},m={class:"sc","loading-img":"au"};const p=a(d,[["render",function(a,s,o,d,p,f){const u=n("Navbar"),v=r("loading");return t((e(),l("div",m,[i(u,{class:"pro-navbar",onClickLeft:f.back},null,8,["onClickLeft"]),c(a.$slots,"default")])),[[v,o.loading]])}]]);export{p as w};
