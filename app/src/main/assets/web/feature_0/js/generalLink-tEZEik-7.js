import{C as t,d as e}from"./vant-vendor-D8PsFlrJ.js";import{_ as a,s as i}from"./index-M11nEPjl.js";import{_ as o}from"./image-Bw2isvnQ.js";import{J as s,K as r,S as n,j as d,L as l,F as c,Y as h,M as p,A as f,B as u,V as m}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const y={class:"container"},g={class:"accordion"},v=["innerHTML"],C=["onClick"];const k=a({name:"GeneralLink",data(){return{activeNames:"i",data:[{title:this.$t("What is Copy Trading?"),content:[this.$t("Copy Trading is a trading strategy and function that automatically copies the entire portfolio of any other trader. Copy Trading mainly consists of 2 roles, which are:"),this.$t("Signal Providers"),this.$t("Users who publicly share their trades for other users to copy."),this.$t("Copiers"),this.$t("Users who copy the publicly shared trades of Signal Providers.")]}],links:[{href:"./beAware",text:"What to be aware of in Copy Trading"},{href:"./benefitAndLimit",text:"Benefits and Limitations of Copy Trading"},{href:"./copyVsManual",text:"Copy trading vs manual trading"},{href:"./history",text:"The history of Copy Trading"},{href:"./risk",text:"What is Risk Band"},{href:"./findTheRIght",text:"Find the right trades to copy"},{href:"./idealCopyMode",text:"Pick Your ideal Copy Mode"},{href:"./infoSupport",text:"Info and Support"}]}},methods:{handleClick(t){this.$router.push({path:t}),i({code:"250",iconList:["CLOSE"]})}}},[["render",function(a,i,k,x,T,b){const w=t,L=e;return r(),s("div",y,[i[3]||(i[3]=n("div",{class:"tab-banner-img"},[n("img",{src:o,alt:""})],-1)),n("div",g,[d(L,{modelValue:T.activeNames,"onUpdate:modelValue":i[0]||(i[0]=t=>T.activeNames=t),accordion:"",border:!1},{"right-icon":l((()=>i[1]||(i[1]=[n("div",{class:"icon-arrow_down"},null,-1)]))),default:l((()=>[(r(!0),s(c,null,h(T.data,((t,e)=>(r(),p(w,{class:"van-collapse-item--border",size:"large",title:t.title,name:"i",key:e},{default:l((()=>[(r(!0),s(c,null,h(t.content,((e,a)=>(r(),s("div",{key:a,style:f({paddingTop:[1,3].includes(a)?"5px":0,paddingBottom:1===a&&a!==t.content.length-1?"16px":0}),innerHTML:e},null,12,v)))),128))])),_:2},1032,["title"])))),128))])),_:1},8,["modelValue"])]),(r(!0),s(c,null,h(T.links,(t=>(r(),s("div",{key:t.text,class:"collapse-link",onClick:e=>b.handleClick(t.href)},[u(m(a.$t(t.text))+" ",1),i[2]||(i[2]=n("div",{class:"icon-arrow_right"},null,-1))],8,C)))),128))])}],["__scopeId","data-v-12c72a53"]]);export{k as default};
