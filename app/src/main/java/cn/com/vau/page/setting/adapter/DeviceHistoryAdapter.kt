package cn.com.vau.page.setting.adapter

import cn.com.vau.R
import cn.com.vau.data.account.DeviceHistoryObj
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: DeviceHistoryAdapter.kt
 * Author: GG
 * Date: 2023/11/29
 * Description:
 */
class DeviceHistoryAdapter : BaseQuickAdapter<DeviceHistoryObj.Obj, BaseViewHolder>(R.layout.item_device_history) {

    override fun convert(holder: BaseViewHolder, item: DeviceHistoryObj.Obj) {
        holder.setText(R.id.tvTitle, "${item.model} (${item.systemType})")
            .setText(R.id.tvIPAddress, "${context.getString(R.string.ip_address)}: ${item.ipAddress ?: "--"}")
            .setText(R.id.tvLocation, "${context.getString(R.string.login_location)}: ${item.ipLocation ?: "--"}")
            .setText(R.id.tvTime, item.lastLoginDateTime)
            .setVisible(R.id.tvCurrentDevice, item.currentDevice)
    }
}