package cn.com.vau.page.setting.viewmodel

import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 24 15:02
 * @updateUser:
 * @updateDate: 2025 4月 24 15:02
 */
class DeviceHistoryViewModel : BaseViewModel() {

    init {
        userDeviceGetHistory()
    }

    fun userDeviceGetHistory() {
        requestNet({
            baseService.userDeviceGetHistory(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess())
                return@requestNet
            sendEvent(it.data?.obj)
        }, isShowDialog = true)
    }
}