package cn.com.vau.page.user.forgotPwdSecond

import android.os.Bundle
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.util.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class ForgotPwdSecondPresenter : ForgetPwdSecondContract.Presenter() {

    var mobile: String? = null
    var email: String? = null
    var txId: String? = null
    var pwd: String? = null
    var firstEmailLogin = false // crm注册来源用户，未绑定手机号

    // 上个页面输入的验证码
    var randStr: String? = null
    var countryCode: String? = null
    var code: String? = null
    var smsSendType: String = VerificationActivity.TYPE_SEND_SMS
    var handleType = 0

    var isFirstCount = true

    private val sendCodeUtil by lazy { SendCodeUtil() }

    override fun goEditPwdApi(pwd: String?, pwdAgain: String?, verificationCode: String?) {

        if (TextUtils.isEmpty(verificationCode) || verificationCode?.length != 6) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_enter_the_code))
            return
        }
        mView?.showNetDialog()
        val map = HashMap<String, Any>()
        if (smsSendType == VerificationActivity.TYPE_SEND_EMAIL) {
            map["email"] = email ?: ""
            map["txId"] = txId ?: ""
        } else {
            map["userTel"] = mobile ?: ""
            map["phoneCountryCode"] = countryCode ?: ""
            map["code"] = code ?: ""
        }
        map["randStr"] = randStr ?: ""
        map["userNewPassword"] = pwd ?: ""
        map["userPasswordConfirm"] = pwdAgain ?: ""
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mModel?.goEditPwdApi(paramMap, object : BaseObserver<ChangeUserInfoSuccessBean>() {
            override fun onNext(data: ChangeUserInfoSuccessBean) {

                mView?.hideNetDialog()

                if (data.resultCode == "V00000")
                    mView?.back()

                ToastUtil.showToast(data.msgInfo)

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 获取修改密码的验证码
     */
    override fun getVerificationCodeApi(validateCode: String) {
        mView?.showNetDialog()
        val map = hashMapOf<String, Any>()
        if (validateCode.isNotEmpty()) {
            map["recaptcha"] = validateCode
        }
        map["smsSendType"] = smsSendType
        if (validateCode.isNotEmpty()) {
            map["recaptcha"] = validateCode
            map["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        if (smsSendType == VerificationActivity.TYPE_SEND_EMAIL) {
            map["count"] = email ?: ""
        } else {
            map["count"] = mobile ?: ""
            map["countryCode"] = countryCode ?: ""
            map["code"] = code ?: ""
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mModel?.getVerificationCodeApi(paramMap, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onNext(data: ForgetPwdVerificationCodeBean) {
                SpManager.putSmsCodeId("")
                mView?.hideNetDialog()
                if (data.resultCode == "V00000") startSendCodeUtil()
                if (data.resultCode == "V10060") { //易盾 需要滑动窗口
                    mView?.showCaptcha()
                    SpManager.putSmsCodeId(data.data?.obj?.smsCodeId ?: "")
                    return
                }
                ToastUtil.showToast(data.msgInfo)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun validateSmsForgetPwdCodeApi(validateCode: String) {
        mView?.showNetDialog()
        val paramMap = HashMap<String, Any>()
        paramMap["code"] = code ?: ""
        paramMap["phoneNum"] = mobile ?: ""
        paramMap["validateCode"] = validateCode
        mModel?.smsValidateSmsForgetPwdCodeApi(paramMap, object : BaseObserver<BaseBean>() {
            override fun onNext(baseData: BaseBean?) {
                mView?.hideNetDialog()
                if (baseData?.resultCode != "V00000") {
                    ToastUtil.showToast(baseData?.msgInfo ?: "")
                    return
                }
                mView?.goThird(validateCode)

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }

    override fun validateEmailForgetPwdCodeApi(validateCode: String) {
        mView?.showNetDialog()
        val paramMap = HashMap<String, Any>()
        paramMap["email"] = email ?: ""
        paramMap["txId"] = txId ?: ""
        paramMap["code"] = validateCode
        mModel?.validateEmailForgetPwdCodeApi(paramMap, object : BaseObserver<BaseBean>() {
            override fun onNext(baseData: BaseBean?) {
                mView?.hideNetDialog()
                if (baseData?.resultCode != "V00000") {
                    ToastUtil.showToast(baseData?.msgInfo ?: "")
                    return
                }
                mView?.goThird(validateCode)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }

    override fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener) {
        sendCodeUtil.initData(60, listener)
    }

    override fun startSendCodeUtil() {
        if (sendCodeUtil.isAlive() != false) {
            sendCodeUtil.start()
            mRxManager.add(sendCodeUtil.disposable)
        }
    }

    override fun stopSendCodeUtil() {
        sendCodeUtil.cancel()
    }

    /**
     * 获取出金限制横幅文案
     */
    override fun getWithdrawRestrictionMsgApi(type: Int) {
        val map = hashMapOf<String, Any>()
        map["userToken"] = UserDataUtil.loginToken()
        map["type"] = type //1=更新手机号；2=更改登录密码；3=重置密码

        mModel?.getWithdrawRestrictionMsgApi(map, object : BaseObserver<DataObjStringBean>() {
            override fun onNext(baseBean: DataObjStringBean) {
                if (baseBean.resultCode == "00000000") {
                    mView?.showWithdrawRestrictionMsg(baseBean.data?.obj)
                } else {
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }

    /**
     * 校验邮箱验证码
     */
    override fun validateEmailFirstLoginCodeApi(validateCode: String) {
        mView?.showNetDialog()
        val map = hashMapOf<String, Any?>()
        map["email"] = email
        map["bizType"] = 4 // 4-首次登录验证邮箱
        map["code"] = validateCode
        map["txId"] = txId
        mModel?.validateEmailFirstLoginCodeApi(map, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: BaseBean) {
                mView?.hideNetDialog()
                if (baseBean.resultCode != "V00000") {
                    ToastUtil.showToast(baseBean.msgInfo)
                    return
                }
                val bundle = Bundle()
                bundle.putString(Constants.USER_EMAIL, email)
                bundle.putString(Constants.USER_PWD, pwd)
                bundle.putInt(Constants.HANDLE_TYPE, handleType)
                mView?.goBindPhone(bundle)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 发送邮箱验证码
     */
    override fun emailSendEmailCodeApi() {
        mView?.showNetDialog()
        val params = HashMap<String, Any?>()
        params["email"] = email // 邮箱
        params["bizType"] = 4 // 4-首次登录验证邮箱
        mModel?.emailSendEmailCodeApi(params, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(data: ForgetPwdVerificationCodeBean) {
                mView?.hideNetDialog()
                if ("V00000" != data.resultCode) {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }
                ToastUtil.showToast(data.msgInfo)
                startSendCodeUtil()
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }
}