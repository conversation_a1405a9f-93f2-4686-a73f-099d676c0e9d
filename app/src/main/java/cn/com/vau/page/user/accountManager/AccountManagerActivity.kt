package cn.com.vau.page.user.accountManager

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.*
import android.widget.*
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.AsicQuestionUtil
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.popup.AccountListPop
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.databinding.ActivityManageAccountBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.accountManager.adapter.*
import cn.com.vau.page.user.bindEmail.BindEmailActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.page.user.openAccoGuide.demo.OpenDemoGuideActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity
import cn.com.vau.ui.common.activity.AccountErrorDialogActivity
import cn.com.vau.util.*
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import org.greenrobot.eventbus.*

class AccountManagerActivity : BaseFrameActivity<AccountManagerPresenter, AccountManagerModel>(), AccountManagerContract.View {

    private val binding by lazy { ActivityManageAccountBinding.inflate(layoutInflater) }

    var isFrom = 0 // 1来自登录注册流程页面  2来自我的 3虚拟账户过期 4真实账户过期 5跟單賬戶申請
    private var mDemoAdapter = DemoAccountAdapter()
    private var mLiveAdapter = LiveAccountAdapter()
    private var mCopyTradingAdapter = CopyTradingAccountAdapter()
    private val demoListPop: AccountListPop by lazy { AccountListPop(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Account_Create_First)
        setContentView(binding.root)
        PerfTraceUtil.firstFrameTrace(binding.root, PerfTraceUtil.StartTrace.Perf_v6_Account_Create_First, PerfTraceUtil.StartTrace.Perf_v6_Account_First_Finish)
        EventBus.getDefault().register(this)
    }

    override fun initParam() {
        super.initParam()
        isFrom = intent?.extras?.getInt(Constants.IS_FROM) ?: 2
        mPresenter.currentAccountDealType = UserDataUtil.accountDealType()
        if (isFrom != 2) {
            // 在Activity中处理返回手势
            onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {}
            })
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        binding.demoRecyclerView.adapter = mDemoAdapter
        binding.liveRecyclerView.adapter = mLiveAdapter
        binding.copyTradeRecyclerView.adapter = mCopyTradingAdapter
        mPresenter.getAccountFirst()

        binding.ivTopClose.setOnClickListener(this)
        binding.smartRefreshLayout.setEnableLoadMore(false)
        binding.smartRefreshLayout.setOnRefreshListener {
            mPresenter.getAccountFirst()
            resetView()
        }

        binding.ivLeftBack.setOnClickListener(this)
        binding.tvStart.setOnClickListener(this)
        binding.tvNewLiveAccount.setOnClickListener(this)
        binding.tvLinkYourAccount.setOnClickListener(this)
        binding.tvOpenDemoAccount.setOnClickListener(this)
        if (isFrom != 2) {
            hideBackBtn()
        } else {
            binding.clTitlaBar.visibility = View.VISIBLE
            binding.tvUserName.visibility = View.GONE
            binding.ivTopClose.visibility = View.GONE
        }

        binding.tvTitle.text = getString(R.string.account_management)

        LogEventUtil.setLogEvent(
            BuryPointConstant.V330.PROFILE_ACC_MGMT_PAGE_VIEW, hashMapOf(
                "Position" to if (isFrom != 2) "Log_In_Page" else "Profile_Page"
            )
        )
    }

    private fun resetView() {
        binding.clOpenAccountError.visibility = View.GONE
        binding.tvOpenDemoAccount.visibility = View.GONE
        binding.tvLinkYourAccount.visibility = View.GONE
    }

    override fun initListener() {
        super.initListener()
        binding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setBackgroundColor(AttrResourceUtil.getColor(this@AccountManagerActivity, R.attr.mainLayoutBg))
                vs.mNoDataScrollView.setHintMessage(getString(R.string.something_went_wrong_try_again))
                vs.mNoDataScrollView.setBottomBtnText(getString(R.string.try_again))
                vs.mNoDataScrollView.setBottomBtnViewClickListener {
                    mPresenter.getAccountFirst()
                    LogEventUtil.setLogEvent(BuryPointConstant.V330.PROFILE_ACC_MGMT_TRY_AGAIN_BUTTON_CLICK)
                }
            }
        })
        mDemoAdapter.setOnItemChildClickListener { adapter, view, position ->
            val bean = mDemoAdapter.getItem(position)
            when (view.id) {
                R.id.llExt -> {
                    bean.showAccountInfo = !bean.showAccountInfo
                    mDemoAdapter.notifyItemChanged(position)
                    if (bean.secondSuccess == "-1" || bean.secondSuccess == "2") {
                        queryAccountInfo(1, position)
                    }
                }

                R.id.tvReset, R.id.tvDemoReset -> {
                    addOrResetDemoAccountLauncher.launch(OpenDemoGuideActivity.createIntent(this, 1))
                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V330.PROFILE_ACC_MGMT_RESET_DEMO_BUTTON_CLICK,
                        hashMapOf(
                            "Demo_Account_Status" to if (bean.isArchive == true) "Expired" else "Active",
                            "Previous_Account_Balance" to if (bean.equitySuccess == "1")
                                bean.detailData?.equity?.numCurrencyFormat(bean.detailData?.currencyType.ifNull())
                                    .ifNull() else ""
                        )
                    )
                }

                R.id.ivSwitch -> {
                    if (mPresenter.switchDemoAccountList.isNullOrEmpty()) {
                        mPresenter.queryDemoAccountList(true)
                    } else {
                        popupWindow()
                    }
                }
                // Demo账户没有修改昵称功能
            }
        }
        mDemoAdapter.setOnItemClickListener { _, _, position ->
            selectCommonAcount(mDemoAdapter.getItem(position))
        }
        mLiveAdapter.setOnItemChildClickListener { adapter, view, position ->
            val bean = mLiveAdapter.getItem(position)
            when (view.id) {
                R.id.llExt -> {
                    bean.showAccountInfo = !bean.showAccountInfo
                    mLiveAdapter.notifyItemChanged(position)
                    if (bean.secondSuccess == "-1" || bean.secondSuccess == "2") {
                        queryAccountInfo(2, position)
                    }
                }

                R.id.tvLiveUpgrade -> { //升级账户
                    showConfirmUpgradeProECNDialog(position, bean.acountCd.ifNull())
                }

                R.id.linearLayout -> {
                    if (bean.isArchive == true) {
                        ToastUtil.showToast(getString(R.string.this_account_has_been_archived_please_customer_service))
                    } else {
                        KeyboardUtil.showSoftInput(this)
                        val etNick = adapter.getViewByPosition(position, R.id.etNick) as EditText
                        (adapter.getViewByPosition(position, R.id.tvAccountType) as TextView).visibility = View.INVISIBLE
                        (adapter.getViewByPosition(position, R.id.linearLayout) as LinearLayout).visibility = View.GONE
                        (adapter.getViewByPosition(position, R.id.llEditNick) as LinearLayout).visibility = View.VISIBLE
                        var lessThan20Nick = if (bean.nickName.ifNull().length > 20) {
                            bean.nickName.ifNull().substringCatching(0, 20)
                        } else {
                            bean.nickName.ifNull()
                        }
                        etNick.setText(lessThan20Nick)
                        etNick.requestFocus()
                        etNick.setSelection(lessThan20Nick.length)
                    }
                }

                R.id.ivClose -> {
                    (adapter.getViewByPosition(position, R.id.tvAccountType) as TextView).visibility = View.VISIBLE
                    (adapter.getViewByPosition(position, R.id.linearLayout) as LinearLayout).visibility = View.VISIBLE
                    (adapter.getViewByPosition(position, R.id.llEditNick) as LinearLayout).visibility = View.GONE
                    KeyboardUtil.hideSoftInput(this)
                }

                R.id.ivSave -> {
                    val etNick = adapter.getViewByPosition(position, R.id.etNick) as EditText
                    if (SpManager.isV1V2()) {
                        if (etNick.text.length > 20) {
                            ToastUtil.showToast(getString(R.string.enter_0_20_characters))
                            return@setOnItemChildClickListener
                        }
                    } else {
                        if (etNick.text.length !in 4..20) {
                            ToastUtil.showToast(getString(R.string.enter_4_x_characters, "20"))
                            return@setOnItemChildClickListener
                        }
                    }
                    mPresenter.modifyNickName(bean, etNick.text.toString().trim(), 2, position)
                    KeyboardUtil.hideSoftInput(this)
                }

                R.id.tvSetupNow -> {
                    val account = mLiveAdapter.getItem(position)
                    KycVerifyHelper.showKycDialog(
                        this,
                        mapOf(
                            Constants.GoldParam.CODE to Constants.GoldParam.CODE_COMPLETE_INFO,
                        )
                    )
                }
            }
        }
        mLiveAdapter.setOnItemClickListener { _, _, position ->
            val bean = mLiveAdapter.getItem(position)
            if (bean.accountDealType != "0") {
                selectCommonAcount(mLiveAdapter.getItem(position))
            }
        }
        mCopyTradingAdapter.setOnItemChildClickListener { adapter, view, position ->
            val bean = mCopyTradingAdapter.getItem(position)
            when (view.id) {
                R.id.llExt -> {
                    val bean = mCopyTradingAdapter.getItem(position)
                    bean.showAccountInfo = !bean.showAccountInfo
                    mCopyTradingAdapter.notifyItemChanged(position)
                    if (bean.secondSuccess == "-1" || bean.secondSuccess == "2") {
                        queryAccountInfo(3, position)
                    }
                }

                R.id.linearLayout -> {
                    KeyboardUtil.showSoftInput(this)
                    val etNick = adapter.getViewByPosition(position, R.id.etNick) as EditText
                    (adapter.getViewByPosition(position, R.id.tvAccountType) as TextView).visibility = View.INVISIBLE
                    (adapter.getViewByPosition(position, R.id.linearLayout) as LinearLayout).visibility = View.GONE
                    (adapter.getViewByPosition(position, R.id.llEditNick) as LinearLayout).visibility = View.VISIBLE
                    var lessThan20Nick = if (bean.nickName.ifNull().length > 20) {
                        bean.nickName.ifNull().substringCatching(0, 20)
                    } else {
                        bean.nickName.ifNull()
                    }
                    etNick.setText(lessThan20Nick)
                    etNick.requestFocus()
                    etNick.setSelection(lessThan20Nick.length)
                }

                R.id.ivClose -> {
                    (adapter.getViewByPosition(position, R.id.tvAccountType) as TextView).visibility = View.VISIBLE
                    (adapter.getViewByPosition(position, R.id.linearLayout) as LinearLayout).visibility = View.VISIBLE
                    (adapter.getViewByPosition(position, R.id.llEditNick) as LinearLayout).visibility = View.GONE
                    KeyboardUtil.hideSoftInput(this)
                }

                R.id.ivSave -> {
                    val etNick = adapter.getViewByPosition(position, R.id.etNick) as EditText
                    if (SpManager.isV1V2()) {
                        if (etNick.text.length > 20) {
                            ToastUtil.showToast(getString(R.string.enter_0_20_characters))
                            return@setOnItemChildClickListener
                        }
                    } else {
                        if (etNick.text.length !in 4..20) {
                            ToastUtil.showToast(getString(R.string.enter_4_x_characters, "20"))
                            return@setOnItemChildClickListener
                        }
                    }
                    mPresenter.modifyStNickName(bean, etNick.text.toString().trim(), position)
                    KeyboardUtil.hideSoftInput(this)
                }

                R.id.tvReactivate -> {
                    showReactivateDialog()
                }
            }
        }
        mCopyTradingAdapter.setOnItemClickListener { _, _, position ->
            val bean = mCopyTradingAdapter.getItem(position)
            if (true == bean.isArchive) {
                ToastUtil.showToast(getString(R.string.this_account_has_been_archived_please_customer_service))
                return@setOnItemClickListener
            }
            selectSocialTradingAccount(mCopyTradingAdapter.getItem(position))
        }

    }

    override fun setDemoAccountNum(number: Int) {
        mDemoAdapter.demoAccountNum = number
        if (number > 1) {
            mPresenter.queryDemoAccountList(false)
        }
    }

    override fun popupWindow() {
        demoListPop.setData(mPresenter.switchDemoAccountList)
            ?.setCallBack(object : AccountListPop.OnSelectItemListener {
                override fun onSelectItem(item: AccountTradeBean) {
//                mPresenter.selectCommonAccount(item)
                    mPresenter.synDemo(item)
                }
            })?.show()
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivTopClose -> {
                if (!UserDataUtil.isLogin()) {
                    CenterActionDialog.Builder(this@AccountManagerActivity)
                        .setContent(getString(R.string.are_you_sure_log_out))
                        .setEndText(getString(R.string.yes_confirm))
                        .setOnEndListener {
                            EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
                            finish()
                        }
                        .build()
                        .showDialog()
                    return
                }
                //退出神策登录
                SensorsDataUtil.logout()
                finish()
            }

            R.id.tvNewLiveAccount -> {
                if (SpManager.isV1V2()) {
                    KycVerifyHelper.showKycDialog(
                        this,
                        mapOf(
                            Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                        )
                    )
                } else {
                    queryMT4AccountType(false)
                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V330.REGISTER_TRAFFIC_LIVE_BUTTON_CLICK, hashMapOf(
                            "Position" to "Acc_management_new"
                        )
                    )
                }
            }

            R.id.tvLinkYourAccount -> goBind()
            R.id.tvOpenDemoAccount -> {
                addOrResetDemoAccountLauncher.launch(OpenDemoGuideActivity.createIntent(this, 0))
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V330.REGISTER_TRAFFIC_DEMO_BUTTON_CLICK, hashMapOf(
                        "Position" to "Acc_management_new"
                    )
                )
            }

            R.id.ivLeftBack -> finish()

            R.id.tvStart -> {
                showQuestionDialog()
            }
        }
    }

    override fun finish() {
        super.finish()
        ActivityManagerUtil.getInstance().finishActivity(LoginPwdActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(TFAVerifyActivity::class.java)
    }

    private fun showReactivateDialog() {
        CenterActionWithIconDialog.Builder(this)
            .setTitle(getString(R.string.re_activate_account))
            .setLottieIcon(
                R.raw.lottie_dialog_ok
            )
            .setSingleButton(true)
            .setContent(getString(R.string.this_account_has_been_archived_please_customer_service))
            .setSingleButtonText(getString(R.string.customer_supports))
            .setOnSingleButtonListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build().showDialog()
    }

    /**
     * 升级账户确认弹框
     */
    private fun showConfirmUpgradeProECNDialog(clickPos: Int, mt4Account: String) {
        val supervise = SpManager.getSuperviseNum("")
        CenterActionWithIconDialog.Builder(this)
            .setTitle(
                getString(R.string.upgrade_to_x, if (supervise == "1") "Raw Premium" else "Pro ECN")
            )
            .setContent(getString(R.string.confirm_to_upgrade))
            .setOnEndListener {
                mPresenter.accountUpgradeGroup(clickPos, mt4Account)
            }
            .setLinkText(getString(R.string.learn_more))
            .setLinkListener {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putString(
                        "url",
                        if (supervise == "1") "https://www.vantagemarkets.com/en-au/trading/accounts/raw-premium/"
                        else "https://plus.vantagemarkets.com/premium-clients/premium-trading-accounts/"
                    )
                    putInt("tradeType", 3)
                    putString("title", "")
                })
            }
            .build()
            .showDialog()
    }

    /**
     * 升级账户类型是否成功
     */
    override fun accountUpgradeIsSuccess(isSuccess: Boolean, clickPos: Int) {
        if (isSuccess) {
            queryAccountInfo(2, clickPos)
            showUpgradeProECNSuccessDialog()
        } else {
            showUpgradeProECNFailDialog()
        }
    }

    /**
     * 升级账户成功弹框
     */
    private fun showUpgradeProECNSuccessDialog() {
        val supervise = SpManager.getSuperviseNum("")
        CenterActionDialog.Builder(this)
            .setTitle(getString(R.string.congratulations))
            .setContent(
                getString(R.string.your_account_has_x, if (supervise == "1") "Raw Premium" else "Pro ECN")
            )
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .build()
            .showDialog()
    }

    /**
     * 升级账户失败弹框
     */
    private fun showUpgradeProECNFailDialog() {
        val supervise = SpManager.getSuperviseNum("")
        CenterActionWithIconDialog.Builder(this)
            .setTitle(getString(R.string.upgrade_failed))
            .setContent(
                getString(R.string.your_account_does_x_please_again_later, if (supervise == "1") "Raw Premium" else "Pro ECN")
            )
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .setLinkText(getString(R.string.learn_more))
            .setLinkListener {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putString(
                        "url",
                        if (supervise == "1") "https://www.vantagemarkets.com/en-au/trading/accounts/raw-premium/"
                        else "https://plus.vantagemarkets.com/premium-clients/premium-trading-accounts/"
                    )
                    putInt("tradeType", 3)
                    putString("title", "")
                })
            }
            .build()
            .showDialog()
    }

    override fun syncLiveCard() {
        if (mPresenter.openAccountData != null) {
            val emptyLayout = mLiveAdapter.emptyLayout
            val btn = emptyLayout?.findViewById<TextView>(R.id.tvOpenLiveAccount)
            when (mPresenter.openAccountData?.type) {
                1 -> {
                    if ("4" == mPresenter.openAccountData?.step) {
                        // Modify
                        btn?.text = getString(R.string.modify_profile)
                        setAccountErrorGuide(
                            getString(R.string.id_verification_required),
                            getString(R.string.upload_your_id_to_proceed),
                            getString(R.string.upload)
                        ) {
                            queryMT4AccountType(false)
                            LogEventUtil
                                .setLogEvent(BuryPointConstant.V330.PROFILE_ACC_MGMT_UPLOAD_BUTTON_CLICK)
                            LogEventUtil.setLogEvent(
                                BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK,
                                hashMapOf("Position" to "Account_management")
                            )
                        }
                    } else {
                        btn?.text = getString(R.string.tv_continue)
                    }
                }

                3 -> {
                    btn?.text = getString(R.string.modify_profile)
                    setAccountErrorGuide(
                        getString(R.string.verification_required),
                        getString(R.string.update_account_info_proceed),
                        getString(R.string.modify_profile)
                    ) {
                        queryMT4AccountType(false)
                        LogEventUtil
                            .setLogEvent(BuryPointConstant.V330.PROFILE_ACC_MGMT_MODIFY_BUTTON_CLICK)
                    }
                }

                4 -> {
                    btn?.text = getString(R.string.modify_profile)
                    setAccountErrorGuide(
                        getString(R.string.id_verification_unsuccessful),
                        getString(R.string.please_reupload_id_proceed),
                        getString(R.string.reupload)
                    ) {
                        queryMT4AccountType(false)
                        LogEventUtil
                            .setLogEvent(BuryPointConstant.V330.PROFILE_ACC_MGMT_REUPLOAD_BUTTON_CLICK)
                    }
                }
            }
        }
    }

    private fun setAccountErrorGuide(
        title: String,
        info: String,
        btnText: String,
        onClick: () -> Unit
    ) {
        binding.clOpenAccountError.visibility = View.VISIBLE
        binding.tvErrorTitle.text = title
        binding.tvErrorInfo.text = info
        binding.tvErrorBtn.text = btnText
        binding.tvErrorBtn.setOnClickListener {
            onClick()
        }
    }

    // 展示调查问卷的题目
    override fun showQuestionDialog() {
        AsicQuestionUtil(this).showQuestionDialog()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun freshDemoAdapter(position: Int) {
        if (position == -1) {
            mDemoAdapter.notifyDataSetChanged()
        } else if (position >= 0 && position < mDemoAdapter.data.size) {
            mDemoAdapter.notifyItemChanged(position)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun freshLiveAdapter(position: Int) {
        if (position == -1) {
            mLiveAdapter.notifyDataSetChanged()
        } else if (position >= 0 && position < mLiveAdapter.data.size) {
            mLiveAdapter.notifyItemChanged(position)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun freshCopyTradingAdapter(position: Int) {
        if (position == -1) {
            mCopyTradingAdapter.notifyDataSetChanged()
        } else if (position >= 0 && position < mCopyTradingAdapter.data.size) {
            mCopyTradingAdapter.notifyItemChanged(position)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun setAdapterData() {
        binding.smartRefreshLayout.finishRefresh(true)
        binding.tvDemoGroupTitle.text = getString(R.string.demo_account)
        binding.tvLinkYourAccount.visibility = if (mPresenter.needShowBindBtn && SpManager.isV1V2().not()) View.VISIBLE else View.GONE

        binding.tvDemoGroupTitle.isVisible = true
        if (mPresenter.demoListData.isNotEmpty()) {
            binding.tvOpenDemoAccount.visibility = View.VISIBLE
            if (mDemoAdapter.data.size > 0) {
                mDemoAdapter.setList(mPresenter.demoListData)
            } else {
                mDemoAdapter.setNewInstance(mPresenter.demoListData)
            }
        } else {
            binding.tvOpenDemoAccount.visibility = View.GONE
            val emptyView = LayoutInflater.from(this)
                .inflate(R.layout.layout_manage_account_demo_empty, binding.demoRecyclerView, false)
            mDemoAdapter.setEmptyView(emptyView)
            emptyView.findViewById<TextView>(R.id.tvSignUp).setOnClickListener {
                addOrResetDemoAccountLauncher.launch(OpenDemoGuideActivity.createIntent(this, 0))
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V330.REGISTER_TRAFFIC_DEMO_BUTTON_CLICK, hashMapOf(
                        "Position" to "Acc_management"
                    )
                )
            }
        }
        mLiveAdapter.notifyDataSetChanged()
        binding.tvLiveGroupTitle.isVisible = true

        // asic用户 不判断live账户 只根据后台字段来显示这个提示区域
        binding.clAsicPrompt.isVisible = !mPresenter.isAppraisal
        // 判断是否是Pending账户
        val isPending =
            mPresenter.liveListData.size == 1 && mPresenter.liveListData[0].accountDealType == "4"
        // 判断是否有虚拟MT5账户
        val isVirtualAccount = mPresenter.listTradingAccount.any { it.accountDealType == "6" }
        // 判断是否有可用账户 (Live + CopyTrading)
        val isHasLiveAccount = mPresenter.listTradingAccount.any { it.state == "2" }

        binding.tvNewLiveAccount.isVisible = isVirtualAccount || isHasLiveAccount
        // 有可用Live账户
        if (mPresenter.liveListData.isNotEmpty() && !isPending) {
            // sort
            if (mPresenter.currentAccountCd?.isNotEmpty() == true) {
                mPresenter.liveListData.sortWith(compareByDescending { it.acountCd == mPresenter.currentAccountCd })
            }
            if (mLiveAdapter.data.isNotEmpty()) {
                mLiveAdapter.setList(mPresenter.liveListData)
            } else {
                mLiveAdapter.setNewInstance(mPresenter.liveListData)
            }
        } else {    // 无可用Live账户
            val emptyView = LayoutInflater.from(this)
                .inflate(R.layout.layout_manage_account_live_empty, binding.liveRecyclerView, false)
            mLiveAdapter.setEmptyView(emptyView)
            val nextBtn = emptyView.findViewById<TextView>(R.id.tvOpenLiveAccount)
            nextBtn.text =
                if (isPending) getString(R.string.tv_continue) else getString(R.string.open_live_account)
            nextBtn.setOnClickListener {
                if (SpManager.isV1V2()) {
                    KycVerifyHelper.showKycDialog(
                        this,
                        mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT)
                    )
                } else {
                    queryMT4AccountType(false)
                }

                LogEventUtil.setLogEvent(
                    BuryPointConstant.V330.REGISTER_TRAFFIC_LIVE_BUTTON_CLICK, hashMapOf(
                        "Position" to "Acc_management"
                    )
                )
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V334.REGISTER_LIVE_LVL1_BUTTON_CLICK, hashMapOf(
                        "Position" to "Account_management"
                    )
                )
            }
        }

        val isShowCopyTrading = mPresenter.isShowStAccount == true
        binding.tvCopyTradingGroupTitle.isVisible = isShowCopyTrading
        binding.copyTradeRecyclerView.isVisible = isShowCopyTrading
        if (isShowCopyTrading) {
            if (mPresenter.copyTradingListData.isNotEmpty()) {
                // sort
                if (mPresenter.currentAccountCd?.isNotEmpty() == true) {
                    mPresenter.copyTradingListData.sortWith(compareByDescending { it.acountCd == mPresenter.currentAccountCd })
                }
                if (mCopyTradingAdapter.data.isNotEmpty()) {
                    mCopyTradingAdapter.setList(mPresenter.copyTradingListData)
                } else {
                    mCopyTradingAdapter.setNewInstance(mPresenter.copyTradingListData)
                }
            } else {
                mPresenter.getCopyTradingDefaultImg()
                val emptyView = LayoutInflater.from(this).inflate(
                    R.layout.layout_manage_account_copy_trading_empty,
                    binding.copyTradeRecyclerView, false
                )
                val image = emptyView.findViewById<ImageView>(R.id.ivGuideCopyTrading)
                val cachePath = SpManager.getAccountManageCopyTradingUrl("")
                ImageLoaderUtil.loadImage(
                    image,
                    cachePath.ifEmpty { R.drawable.img_guide_copy_trading },
                    image
                )
                mCopyTradingAdapter.setEmptyView(emptyView)
                emptyView.findViewById<TextView>(R.id.tvStartCopy).setOnClickListener {
                    if (SpManager.isV1V2()) {
                        KycVerifyHelper.showKycDialog(
                            this,
                            mapOf(
                                Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                Constants.GoldParam.CREATE_COPY_TRADING_ACCOUNT to "1"
                            )
                        )
                    } else {
                        if (mPresenter.needShowNewLiveBtn) { //已開通Live時跳至開通跟單帳號頁
                            queryMT4AccountType(true, isSelectedCopyTrading = true)
                        } else { //若無開通過Live則走開通Live流程
                            queryMT4AccountType(false, isSelectedCopyTrading = true)
                        }

                        if (mPresenter.liveListData.isEmpty() || isPending) {
                            LogEventUtil
                                .setLogEvent(BuryPointConstant.V330.CT_REGISTER_LIVE_BUTTON_CLICK)
                        }
                        LogEventUtil.setLogEvent("open_social_trading_account_click")
                    }
                }
            }
        }

        PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Account_First_Finish)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun freshAllAdapter() {
        mDemoAdapter.notifyDataSetChanged()
        mLiveAdapter.notifyDataSetChanged()
        mCopyTradingAdapter.notifyDataSetChanged()
    }

    override fun refreshCopyTradingImg() {
        val emptyView = mCopyTradingAdapter.emptyLayout
        val image = emptyView?.findViewById<ImageView>(R.id.ivGuideCopyTrading)
        val cachePath = SpManager.getAccountManageCopyTradingUrl("")
        image?.let {
            ImageLoaderUtil.loadImage(image, cachePath.ifEmpty { R.drawable.img_guide_copy_trading }, image)
        }
    }

    override fun hideBackBtn() {
        binding.clTitlaBar.visibility = View.GONE
        binding.tvUserName.visibility = View.VISIBLE
        binding.ivTopClose.visibility = View.VISIBLE
        binding.tvUserName.text =
            getString(
                R.string.hi_x,
                if (TextUtils.isEmpty(UserDataUtil.nickname())) "" else ", ${UserDataUtil.nickname()}"
            )
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun refreshBottomButton() {
    }

    /**
     * 修改昵称弹窗
     */
    override fun showModifyNameDialog(position: Int, isModifyStAccount: Boolean) {}

    override fun refreshAccountState(position: Int) {}

    /**
     * 修改昵称完成以后刷新页面
     */
    override fun modifyNickNameFinish(position: Int) {}

    /**
     * 申请开通mt4账户号
     *
     * @param isSelectedCopyTrading 从开户页面未开通账户时点击Start Copy按钮，进入开户lv1第二步和开同名页面需要默认选中CopyTrading账户
     */
    fun queryMT4AccountType(isOpenStAccount: Boolean, isSelectedCopyTrading: Boolean = false) {
        noRepeat {
            if (SpManager.isV1V2()) {
                VAUStartUtil.openAccountToKyc(this, isSelectedCopyTrading = isSelectedCopyTrading)
            } else {
                if (isOpenStAccount) {
                    mPresenter.queryStAccountType(isSelectedCopyTrading)
                } else {
                    mPresenter.queryMT4AccountType(isSelectedCopyTrading)
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Constants.RESULT_CODE -> mPresenter.getAccountFirst()
            Constants.SUBMIT_SUCCESS -> {
                mPresenter.isAppraisal = true
                binding.clAsicPrompt.isVisible = false
            }

            102 -> mPresenter.getAccountFirst()
        }
    }

    override fun showDemoDialog() {
        CenterActionWithIconDialog.Builder(this)
            .setTitle(getString(R.string.congratulations))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setContent(getString(R.string.you_can_log_account))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.close))
            .setOnSingleButtonListener {
                mPresenter.getAccountFirst()
            }
            .build()
            .showDialog()
    }

    override fun goBind() {
        openActivity(BindEmailActivity::class.java)
    }

    override fun showGuidangDialog() {
        openActivity(AccountErrorDialogActivity::class.java, Bundle().apply {
            putInt("type_account_error", 2)
        })
    }

    // 返佣账号
    override fun selectRakebackAcount(position: Int) {}

    // 正常账户
    override fun selectCommonAcount(normalData: AccountTradeBean) {
        if (normalData.acountCd == mPresenter.currentAccountCd && normalData.accountDealType != "3") {  // 模拟账户每次点击都调接口进入
            finish()
        } else {
            mPresenter.selectCommonAccount(normalData)
        }
        if (normalData.state == "2") {
            LogEventUtil.setLogEvent("switch_account_click", Bundle().apply {
                putString("Account Type", "Live")
                putString("Account ID", normalData.acountCd)
            })
        } else if (normalData.state == "3") {
            LogEventUtil.setLogEvent("switch_account_click", Bundle().apply {
                putString("Account Type", "Demo")
                putString("Account ID", normalData.acountCd)
            })
        }
    }

    // st账号
    override fun selectSocialTradingAccount(stData: AccountTradeBean) {
        if (stData.acountCd == mPresenter.currentAccountCd) {
            finish()
        } else {
            mPresenter.selectSocialTradingAccount(stData)
        }
        LogEventUtil.setLogEvent("switch_account_click", Bundle().apply {
            putString("Account Type", "Copy Trading")
            putString("Account ID", stData.acountCd)
        })
    }

    override fun queryAccountInfo(adapterType: Int, position: Int) {
        mPresenter.queryAccountInfo(
            adapterType, position, when (adapterType) {
                1 -> mDemoAdapter.getItem(position)
                2 -> mLiveAdapter.getItem(position)
                3 -> mCopyTradingAdapter.getItem(position)
                else -> null
            }
        )
    }

    override fun initRetryView(isShowRetry: Boolean) {
        binding.smartRefreshLayout.finishRefresh()
        if (isShowRetry) {
            binding.mVsNoDataScroll.isVisible = true
            binding.nestedScrollView.visibility = View.GONE
            binding.llBottom.visibility = View.GONE
        } else {
            binding.mVsNoDataScroll.isVisible = false
            binding.nestedScrollView.visibility = View.VISIBLE
            binding.llBottom.visibility = View.VISIBLE
        }
    }

    /**
     * 会跳转一个新的MainActivity，旧的MainActivity会被销毁，销毁前会收到SWITCH_ACCOUNT通知)
     */
    override fun reStartApp() {
        SDKIntervalUtil.instance.removeAllCallBack()    // 强制清除所有页面订阅
        val intent0: Intent = Intent(context, MainActivity::class.java)
        intent0.addFlags(
            Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                    or Intent.FLAG_ACTIVITY_CLEAR_TASK
        )
        val bundle = Bundle()
        bundle.putBoolean("is_switch_account", true)
        intent0.putExtras(bundle)
        startActivity(intent0)
    }

    /**
     * 新增/重置 vts demo 回调
     */
    private val addOrResetDemoAccountLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == RESULT_OK) {
            showDemoDialog()
        }
    }

    override fun onBackPressed() {
        if (isFrom == 2) {
            super.onBackPressed()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        if (tag == NoticeConstants.REFRESH_ACCOUNT_MANAGER || tag == NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE) {
            mPresenter.getAccountFirst()
        }
        // 为防止账户列表页频繁刷新，已跟JK沟通，只在上传身份证明/护照/驾照页面成功后进行自动刷新，其余需用户自行下拉刷新
        else if (tag == NoticeConstants.UPLOAD_PHOTO_SUCCEED) {
            binding.smartRefreshLayout.autoRefresh()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().post(NoticeConstants.ACCOUNT_ERROR_STATE_INIT)
        EventBus.getDefault().unregister(this)
    }
}
