package cn.com.vau.page.user.accountManager

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjBooleanBean
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.AccountListFirstBean
import cn.com.vau.data.account.AccountOpeningGuideBean
import cn.com.vau.data.account.AccountsEquityData
import cn.com.vau.data.account.AloneAccountInfoData
import cn.com.vau.data.account.DemoAccountBean
import cn.com.vau.data.account.DemoAccountListData
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.account.StAccMarginBean
import cn.com.vau.data.account.StAccountLoginBean
import cn.com.vau.data.init.TradeAccountLoginBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * Created by Haipeng on 2017/10/12.
 */
class AccountManagerModel : AccountManagerContract.Model {

    override fun getAccountFirst(map: HashMap<String, Any>, baseObserver: BaseObserver<AccountListFirstBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().queryAccountList(map), baseObserver)
        return baseObserver.disposable
    }

    override fun modifyNickName(map: HashMap<String, String>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().updateAccountNickName(map), baseObserver)
        return baseObserver.disposable
    }

    override fun modifyStNickName(map: HashMap<String, String>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().updateStAccountNickName(map), baseObserver)
        return baseObserver.disposable
    }

    override fun tradeAccountLogin(body: RequestBody, baseObserver: BaseObserver<TradeAccountLoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService2().tradeAccountLogin(body), baseObserver)
        return baseObserver.disposable
    }

    override fun synDemo(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().synchroDemo(map), baseObserver)
        return baseObserver.disposable
    }

    override fun queryMT4AccountType(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().crmGetMt4AccountApplyType(map), baseObserver)
        return baseObserver.disposable
    }

    override fun accountOpeningGuide(map: HashMap<String, Any>, baseObserver: BaseObserver<AccountOpeningGuideBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().accountOpeningGuide(map), baseObserver)
        return baseObserver.disposable
    }

    override fun queryDemoAccountList(
        map: HashMap<String, Any>,
        baseObserver: BaseObserver<DemoAccountListData>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().queryDemoAccountList(map), baseObserver)
        return baseObserver.disposable
    }

    override fun queryDemoAccount(map: HashMap<String, Any>, baseObserver: BaseObserver<DemoAccountBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().queryDemoAccount(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun stAccountLogin(requestBody: RequestBody, baseObserver: BaseObserver<StAccountLoginBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().accountLoginWithAccount(requestBody),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun stAccountAccMargin(stToken: String, baseObserver: BaseObserver<StAccMarginBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().accountAccMargin(stToken),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun queryAccountEquitList(
        token: String,
        baseObserver: BaseObserver<AccountsEquityData>
    ): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().queryAccountEquitList(token),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun queryAccountInfo(
        paramMap: HashMap<String, Any>,
        baseObserver: BaseObserver<AloneAccountInfoData>
    ): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().queryAccountInfo(paramMap),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun getCopyTradingDefaultImg(
        token: String,
        baseObserver: BaseObserver<DataObjStringBean>
    ): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().getCopyTradingDefaultImg(token),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun accountUpgradeGroup(
        paramMap: HashMap<String, Any>,
        baseObserver: BaseObserver<DataObjBooleanBean>
    ): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().accountUpgradeGroup(paramMap), baseObserver
        )
        return baseObserver.disposable
    }

}
