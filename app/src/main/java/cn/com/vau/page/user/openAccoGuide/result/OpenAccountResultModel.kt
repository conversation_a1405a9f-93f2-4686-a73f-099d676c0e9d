package cn.com.vau.page.user.openAccoGuide.result

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.data.account.EventsTicketData
import cn.com.vau.page.user.openAccountFirst.OpenAccountCacheModel
import io.reactivex.disposables.Disposable


/**
 * Created by Haipeng on 2017/10/12.
 */
class OpenAccountResultModel : OpenAccountResultContract.Model, OpenAccountCacheModel() {

    override fun getEventsAndRookieTicket(
        map: HashMap<String, Any>,
        baseObserver: BaseObserver<EventsTicketData>
    ): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().getEventsAndRookieTicket(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun getAuditStatus(
        token: String,
        baseObserver: BaseObserver<AuditStatusData>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getAuditStatus(token), baseObserver)
        return baseObserver.disposable
    }

}
