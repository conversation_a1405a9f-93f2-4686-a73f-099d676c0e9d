package cn.com.vau.page.user.openAccountForth


import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.page.user.openAccountFirst.OpenAccountCacheContract
import cn.com.vau.data.account.RealAccountCacheObj

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface OpenAcountForthContract {
    interface Model : OpenAccountCacheContract.Model
    interface View : BaseView {
        fun registerRealAccountSuccess(msgInfo:String?)
        fun showRealInfo(data: RealAccountCacheObj?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun saveRealInfo()
        abstract fun getRealInfo()
    }

}
