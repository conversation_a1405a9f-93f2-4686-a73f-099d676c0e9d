package cn.com.vau.page.user.openAccoGuide.result

import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.data.enums.EnumAccountType
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import io.reactivex.disposables.Disposable
import org.json.JSONObject

/**
 * Created by Haipeng on 2017/10/12.
 */
class OpenAccountResultPresenter : OpenAccountResultContract.Presenter() {

    // 123
    var stepNum = "-1"

    // 账户类型
    var accountType = EnumAccountType.NO_PASS

    var permissionTitle = ""
    var permissionContent = ""

    var nextStr = ""
    var linkStr = ""

    var ticketData: EventsTicketData.Obj? = null

    override fun getAuditStatus() {

        mView?.showNetDialog()

        getEventsAndRookieTicket()

        mModel?.getAuditStatus(
            UserDataUtil.loginToken(),
            object : BaseObserver<AuditStatusData>() {
                override fun onNext(data: AuditStatusData?) {

                    if ("V00000" != data?.resultCode) {
                        mView?.hideNetDialog()
                        return
                    }
                    val dataObj = data.data?.obj

                    if (null == dataObj) {
                        mView?.hideNetDialog()
                        return
                    }

                    initAccountData(dataObj)

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                    mView?.initAccountView()
                }

            })
    }

    override fun getEventsAndRookieTicket() {

        val paramMap = hashMapOf<String, Any>()
        paramMap["userId"] = UserDataUtil.userId()
        mModel?.getEventsAndRookieTicket(paramMap, object : BaseObserver<EventsTicketData>() {
            override fun onNext(data: EventsTicketData?) {
                if (data?.resultCode != "V00000") return

                ticketData = data.data?.obj
                // 显示优惠券
                if (!TextUtils.isEmpty(ticketData?.couponId)) {
                    mView?.initCouponView()
                    return
                }
                // 显示活动
                if ((ticketData?.adsenseContents?.size ?: 0) > 0) {
                    mView?.initEventsView()
                }

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }

        })
    }

    private fun initAccountData(dataObj: AuditStatusData.Obj) {

        val lv1Status = dataObj.accountAuditStatus
        val lv2Status = dataObj.poiAuditStatus

        // ASIC 不会进入新版开户
        var buryPointMsg = when (SpManager.getSuperviseNum("")) {
            "8" -> "VFSC-"
            "13" -> "FCA-"
            else -> "VFSC2-"
        }

        LogUtil.i("OpenAccountResult --- ${buryPointMsg}")

        buryPointMsg += when (stepNum) {
            "1" -> "Lvl1-4-"
            "2" -> "Lvl2-3-"
            "3" -> "Lvl3-3-"
            else -> ""
        }

        when (stepNum) {
            "1" -> {

                // 默认只读
                accountType = EnumAccountType.ONLY_READ
                permissionTitle = context.getString(R.string.congratulations)
                permissionContent = context.getString(R.string.open_account_result_lv1_permission_content_only_read)
                nextStr = context.getString(R.string.deposit_preparation)
                linkStr = context.getString(R.string.complete_id_verification)

                // 只读
                if ("1" == lv1Status) {
                    accountType = EnumAccountType.ONLY_READ
                    buryPointMsg += "read-only"
                }
                // 没开通任何账户 ( 直接跳 ID 审核流程页面 )
                if ("0" == lv1Status || "4" == lv1Status) {
                    accountType = EnumAccountType.NO_PASS
                }
            }

            "2" -> {

                // 默认未开通任何账户
                permissionTitle = context.getString(R.string.id_authentication_under_review)
                permissionContent =
                    context.getString(R.string.open_account_result_lv2_permission_content_no_pass)
                nextStr = context.getString(R.string.use_demo_account)

                // 只读
//                if ("1" == lv1Status && ("0" == lv2Status || "4" == lv2Status)) {
                if ("1" == lv1Status && "2" != lv2Status) {
                    accountType = EnumAccountType.ONLY_READ
                    permissionTitle = context.getString(R.string.id_authentication_under_review)
                    permissionContent =
                        context.getString(R.string.open_account_result_lv2_permission_content_only_read)
                    nextStr = context.getString(R.string.deposit_preparation)
                    buryPointMsg += "read-only"
                }
                // 没开通任何账户
//                if ("0" == lv1Status || "4" == lv1Status) {
                if ("1" != lv1Status) {
                    accountType = EnumAccountType.NO_PASS
                    buryPointMsg += "review"
                }
                // 交易账户
                if ("1" == lv1Status && "2" == lv2Status) {
                    accountType = EnumAccountType.TRADE
                    permissionTitle = context.getString(R.string.id_authentication_passed)
                    permissionContent =
                        context.getString(R.string.available_for_trading)
                    nextStr = context.getString(R.string.deposit_preparation)
                    buryPointMsg += "success"
                }
            }

            "3" -> {

                permissionTitle = context.getString(R.string.poa_authentication_under_review)
                permissionContent =
                    context.getString(R.string.open_account_result_lv3_permission_content)

                // 默认未开通任何账户
                nextStr = context.getString(R.string.use_demo_account)

                // 只读
//                if ("1" == lv1Status && ("0" == lv2Status || "4" == lv2Status)) {
                if ("1" == lv1Status && "2" != lv2Status) {
                    accountType = EnumAccountType.ONLY_READ
                    nextStr = context.getString(R.string.deposit_preparation)
                    buryPointMsg += "read-only"
                }
                // 没开通任何账户
//                if ("0" == lv1Status || "4" == lv1Status) {
                if ("1" != lv1Status) {
                    accountType = EnumAccountType.NO_PASS
                    buryPointMsg += "review"
                }
                // 交易账户
                if ("1" == lv1Status && "2" == lv2Status) {
                    accountType = EnumAccountType.TRADE
                    nextStr = context.getString(R.string.deposit_preparation)
                    permissionContent =
                        context.getString(R.string.open_account_result_lv3_permission_content_pass)
                    buryPointMsg += "success"
                }
            }
            // 结论是：
            // lv1未通过：全不亮
            // lv1已通过 && lv2已通过 （账号、入金、交易权限）
            // lv1已通过 && lv2除已通过的其他 （账号、入金）只读

        }

        LogEventUtil.setLogEvent(
            BuryPointConstant.V334.REGISTER_LIVE_PAGE_VIEW, hashMapOf(
                "Page_name" to buryPointMsg
            )
        )

        mView?.initAccountView()

    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     *
     * @param position 100->入金  101->完成身份验证  102->切demo
     */
    fun sensorsTrackClick(position: Int) {
        val properties = JSONObject()
        var level = ""
        var step = ""
        var buttonName = ""
        when (stepNum) {
            "1" -> {
                level = "Lv1.Account Opening"
                step = when (position) {
                    100 -> "Live account passed"
                    101 -> "Account Authentication passed"
                    else -> "" // lv1没有 切demo 埋点
                }
                buttonName = when (position) {
                    100 -> "Deposit Preparation"
                    101 -> "Complete ID Verification"
                    else -> "" // lv1没有 切demo 埋点
                }
            }

            "2" -> {
                level = "Lv2.ID Authentication"
                step = when (accountType) {
                    EnumAccountType.ONLY_READ -> "ID Under review"
                    EnumAccountType.TRADE -> "ID Passed"
                    EnumAccountType.NO_PASS -> "ID Failed"
                }
                buttonName = when (position) {
                    100 -> "Deposit Preparation"
                    101 -> "Complete ID Verification"
                    102 -> "Use Demo Account"
                    else -> ""
                }
            }

            "3" -> {
                level = "Lv3.POA Authentication"
                step = when (accountType) {
                    EnumAccountType.ONLY_READ -> "POA Under review"
                    EnumAccountType.TRADE -> "POA Passed"
                    EnumAccountType.NO_PASS -> "POA Failed"
                }
                buttonName = when (position) {
                    100 -> "Deposit Preparation"
                    102 -> "Use Demo Account"
                    else -> "" // lv3没有 完成身份验证 埋点
                }
            }
        }
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, level) // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, step) // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }
}