package cn.com.vau.page.start

import android.annotation.SuppressLint
import android.graphics.drawable.AnimatedVectorDrawable
import android.net.Uri
import android.os.*
import android.text.TextUtils
import android.view.*
import androidx.core.view.isVisible
import cn.com.vau.MainActivity
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.data.msg.*
import cn.com.vau.databinding.ActivitySplashBinding
import cn.com.vau.signals.activity.EconomicCalendarActivity
import cn.com.vau.util.*
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.opt.PerfTraceUtil.StartTrace.Perf_v6_Start_AppCreate_SplashCreate
import cn.com.vau.util.opt.PerfTraceUtil.StartTrace.Perf_v6_Start_SplashCreate_SplashFirst
import cn.com.vau.util.tracking.AppsFlyerBuryPoint
import cn.com.vau.util.widget.FirebaseManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.firebase.dynamiclinks.ktx.dynamicLinks
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.metrics.AddTrace

class SplashActivity : BaseMvvmActivity<ActivitySplashBinding, SplashViewModel>() {

    // 深入链接
    private var deepLinkString: String? = null

    // App 应用链接
    private var appLinkType: Int = -1

    // Fcm营销推送 (App退出后收到推送的情况)
    private val type: String by lazy { intent.getStringExtra(Constants.FCM_TYPE) ?: "" }
    private val parcelableData: PushParam? by lazy {
        intent.extras?.getParcelable<PushParam>(
            Constants.FCM_DATA_P
        )
    }

    private var isSwitchAccount: Boolean = false

    private val runnable by lazy { Runnable { openNextActivity() } }

    private val handler by lazy { Handler(Looper.getMainLooper()) }

    private val delayTime = 2000L

    //防止重复执行startActivity
    private var isExecuteStartActivity = false

    @AddTrace(name = "Method->SplashActivity_onCreate", enabled = true)
    override fun onCreate(savedInstanceState: Bundle?) {
        val window: Window = window
        BarUtil.transparentStatusBar(window)
        BarUtil.setBarIconColor(window, true)
        super.onCreate(savedInstanceState)
//        if (VauApplication.abOptNetParallel) {
//            xhLoge("注册callback")
//            LinkStateManager.registerCallback(netStateChangeCallback)
//        }
        processTrace()
        BarUtil.transparentNavigationBar(window)

        PerfTraceUtil.firstFrameTrace(
            window.decorView, Perf_v6_Start_SplashCreate_SplashFirst,
            PerfTraceUtil.StartTrace.Perf_v6_Start_SplashFirst_StartMain,
            intent
        )

        FirebaseManager.updateAppInstanceId()
        AppsFlyerBuryPoint.requestRequireParam()
        isSwitchAccount = intent.extras?.getBoolean("isSwitchAccount", false) ?: false
        ActivityManagerUtil.getInstance().finishOtherActivities(SplashActivity::class.java)

        // 日志
        if (UserDataUtil.isLogin()) {
            DbManager.getInstance().saveDealLog(DealLogInfo().apply {
                timeStamp = System.currentTimeMillis().toString()
                date = PickerDateUtil.currentTimeMillisToString("dd/MM/yyyy HH:mm:ss")
                val timeZone = AppUtil.getTimeZoneRawOffsetToHour()
                log = "model:${AppUtil.getSystemModel()}  " +
                        "os:${AppUtil.getSystemVersion()}  " +
                        "versions:${AppUtil.getVersionName()}  " +
                        "time zone:${if (timeZone > 0) "+" else ""}$timeZone"
                tel = UserDataUtil.userTel()
            })
        }

        val appLinkData = intent?.data
        if (!TextUtils.isEmpty(appLinkData?.toString()) && appLinkData?.toString()
                ?.startsWith("https://vantagemarketapp.com") == true
        ) {
            val linkStr = appLinkData.toString()
            when {
                linkStr.contains("Open_Live") -> appLinkType = 1
                linkStr.contains("Deposit") -> appLinkType = 2
                linkStr.contains("h5/app/H5") -> {
                    if (linkStr.contains("link=")) {
                        SpManager.putUrlH5(linkStr.split("link=").getOrNull(1).ifNull())
                        appLinkType = 3
                    } else {
                        if (linkStr.contains("Coupon")) {
                            appLinkType = 4
                        } else if (linkStr.contains("TopTrader")) {
                            appLinkType = 5
                        }
                    }
                }

            }
        } else if (appLinkData.toString().startsWith("https://vantageapp.onelink.me/")) {
            // 注册邀请码 // https://vantageapp.onelink.me/qaPD?af_xp=referral&pid=SHARE-SP&deep_link_value=code-ABC123&deep_link_sub1=spid-123456
            appLinkData?.getQueryParameter("deep_link_value")?.let { value ->
                if (value.contains("code-")) {
                    appLinkType = 7
                    SpManager.putInviteCodeRegister(value.drop("code-".length))
                } else if (value.contains("mt4id-")) {
                    appLinkType = 7
                    SpManager.putInviteCodeRegister(value.drop("mt4id-".length))
                }
            }
            appLinkData?.getQueryParameter("deep_link_sub1")?.let { value ->
                if (UserDataUtil.isLogin()) {
                    if (value.contains("spid-")) {
                        appLinkType = 8
                        SpManager.putIdSignalSource(value.drop("spid-".length))
                    }
                }
            }
        } else if (appLinkData.toString().startsWith("https://vantagefxapp.onelink.me/")) {
            // 跳转下单页面 https://vantagefxapp.onelink.me/Qgw0?deep_link_value=Trade_USDJPY
            appLinkData?.getQueryParameter("deep_link_value")?.let { value ->
                if (value.lowercase().contains("trade_")) {
                    deepLinkString = value
                }
            }
        }

        appLinkData?.getQueryParameter("deep_link_value")?.let { value ->
            if (value.contains("rs-")) {
                SpManager.putResourceCodeRegister(
                    value.drop("rs-".length)
                )
            }
        }

        Firebase.dynamicLinks
            .getDynamicLink(intent)
            .addOnSuccessListener(this) { pendingDynamicLinkData ->
                var deepLink: Uri? = null
                if (pendingDynamicLinkData != null) {
                    deepLink = pendingDynamicLinkData.link
                    deepLinkString = deepLink.toString().trim()
                }
            }.addOnFailureListener(this) { e ->
                // LogUtil.i("SplashActivity ---- getDynamicLink:onFailure, $e")
            }

        initThemeData()
        LottieHelper.preLoadLottieJson()

    }

    //    private val netStateChangeCallback = object : LinkStateManager.StateChangeCallback(){
//        override fun onProductAndWebsocketSuccess() {
//            xhLoge("不用等待2s，   直接进入主页")
//            openNextActivity()
//            finish()
//        }
//    }
    private fun initThemeData() {
        val isFollowSystem = SpManager.getStyleFollowSystem(true)
        if (isFollowSystem) {
            SpManager.putStyleState(if (AppUtil.isNightMode()) 1 else 0)
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun initParam(savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }

    }

    override fun initView() {
        val imageUrl = SpManager.getAppStartUpImageUrl("")
        if (imageUrl.isNotBlank()) {
            mBinding.ivLogo.isVisible = false
            ImageLoaderUtil.loadImageWithCacheStrategy(
                mBinding.mImageView,
                imageUrl,
                mBinding.mImageView,
                DiskCacheStrategy.DATA
            )
        } else {
            mBinding.ivLogo.isVisible = true
            (mBinding.ivLogo.drawable as? AnimatedVectorDrawable)?.start()
        }
        mBinding.tvSkip.visibility = View.VISIBLE
    }

    override fun initData() {
        super.initData()
        // 开始初始化
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_InitHelper_Start_Connected)
        if (!VauApplication.abOptNetParallel) {
            InitHelper.start()
        }
        InitHelper.dataServerTime()
        if (isSwitchAccount) {
            openNextActivity()
            return
        }
        //打开MainActivity
        handler.postDelayed(runnable, delayTime)

    }

    override fun initListener() {
        super.initListener()
        mBinding.tvSkip.setOnClickListener {
            //点击跳转，移除runnable
            handler.removeCallbacks(runnable)
            openNextActivity()
            finish()
        }
    }

    override fun onDestroy() {
        //点击跳转，移除runnable
        handler.removeCallbacks(runnable)
//        if (VauApplication.abOptNetParallel) {
//            LinkStateManager.unregisterCallback(netStateChangeCallback)
//        }
        super.onDestroy()
    }

    private fun openNextActivity() {
        //防止重复执行startActivity
        if (VauApplication.abOptNetParallel) {
            if (isExecuteStartActivity) {
                return
            }
            isExecuteStartActivity = true
        }
//        LogUtil.e("------------>$deepLinkString")
//        deepLinkString = "https://login"
        if (parcelableData != null) {     // Fcm推送
            openActivity(MainActivity::class.java, Bundle().apply {
                putString(Constants.FCM_TYPE, type)
                putParcelable(Constants.FCM_DATA_P, parcelableData ?: PushParam())
            })
            finish()
        } else if (deepLinkString == null || deepLinkString?.contains(
                "https://Home",
                true
            ) == true
        ) {
            startMainActivity()
        } else if (TextUtils.equals(deepLinkString, "https://economic_calendar/")) { // 财经日历
            // todo gold 这里应该是需要传id的 但是现在没有id ， 节后 需要找马东或者 淑漩排查一下这里的id怎么获取
            openActivity(EconomicCalendarActivity::class.java)
        } else if (
            deepLinkString?.contains("http://Trade_", true) == true ||
            deepLinkString?.contains("http://TopTrader", true) == true ||
            deepLinkString?.contains("http://Open_Live", true) == true ||
            deepLinkString?.contains("http://Deposit", true) == true ||
            deepLinkString?.contains("http://Coupon", true) == true ||
            deepLinkString?.contains("https://Trade_", true) == true ||
            deepLinkString?.contains("https://TopTrader", true) == true ||
            deepLinkString?.contains("https://Open_Live", true) == true ||
            deepLinkString?.contains("https://Deposit", true) == true ||
            deepLinkString?.contains("https://Coupon", true) == true ||
            deepLinkString?.contains("https://Livestream", true) == true ||
            deepLinkString?.contains("https://promo", true) == true ||
            deepLinkString?.contains("https://discover", true) == true ||
            deepLinkString?.contains("https://copytrading", true) == true ||
            deepLinkString?.contains("https://login", true) == true ||
            // 354 新增的deeplink
            deepLinkString?.contains("Trade_", true) == true
        ) {
            ActivityManagerUtil.getInstance().finishActivity(MainActivity::class.java)
            val bundle = Bundle()
            bundle.putString("dynamic_links", deepLinkString)
            if (isSwitchAccount) bundle.putString("is_switch_account", "")
            openActivity(MainActivity::class.java, bundle)
            finish()
        } else {
            val pushBean = PushBean()
            pushBean.openType = "url"
            val pushTitle = PushTitle("", "", "", "")
            pushBean.titles = pushTitle
            val pushUrls = PushUrl(
                deepLinkString,
                deepLinkString,
                deepLinkString,
                deepLinkString,
                deepLinkString,
                deepLinkString
            )
            pushBean.urls = pushUrls
            VAUStartUtil.openActivity(this@SplashActivity, pushBean)
        }

    }

    private fun startMainActivity() {
        //因为有可能进入页面之后直接就进入了MainActivity，所以sp文件里没有值，会造成用户删除APP之后重新进入跟单账号之后重启APP 出现闪屏页面，为了防止这个问题加入了isSwitchAccount判断
        if (SpManager.getAppStartFirst(true) && !isSwitchAccount
        ) {
            // 跳转导航页面(暂时没有导航页)
            openActivity(WelcomeActivity::class.java)
            finish()
            return
        }

        for (activity in ActivityManagerUtil.getInstance().activityStack) {
            if (activity is MainActivity) {
                ActivityManagerUtil.getInstance().finishActivity(MainActivity::class.java)
            }
        }

        PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Start_SplashFirst_StartMain, intent)
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Start_StartMain_MainCreate, intent)

        val bundle = Bundle()
        if (PerfTraceUtil.isStartUpFromFcmOrDeepLink(intent)) {
            bundle.putString(PerfTraceUtil.startUpTagFromFcm, PerfTraceUtil.startUpTagFromFcm)
        }
        bundle.putInt("app_link_type", appLinkType)
        openActivity(MainActivity::class.java, bundle)
        finish()
    }

    override fun onResume() {
        super.onResume()
        if (deepLinkString != null) {
            startMainActivity()
        }
    }

    private fun processTrace() {
        PerfTraceUtil.stopTrace(Perf_v6_Start_AppCreate_SplashCreate, intent)
        PerfTraceUtil.startTrace(Perf_v6_Start_SplashCreate_SplashFirst, intent)
    }

}
