package cn.com.vau.page.user.accountManager.adapter

import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class CopyTradingAccountAdapter: BaseQuickAdapter<AccountTradeBean, BaseViewHolder>(R.layout.item_manage_account_copy_trading) {

    private val selectedAccountCd = UserDataUtil.accountCd()

    init {
        addChildClickViewIds(R.id.tvStartCopy, R.id.llExt, R.id.linearLayout, R.id.ivClose, R.id.ivSave, R.id.tvReactivate)
    }

    override fun convert(holder: BaseViewHolder, item: AccountTradeBean) {
        holder.setText(R.id.tvAccountType, "Copy Trading")
        holder.setVisible(R.id.tvAccountType, true)
        holder.setBackground(R.id.tvAccountType, ContextCompat.getDrawable(context, R.drawable.shape_c007fff_r100))
        holder.setGone(R.id.llEditNick, true)
        holder.setVisible(R.id.linearLayout, true)
        holder.setText(R.id.tvAccountName, if (item.nickName.isNullOrEmpty()) context.getString(R.string.set_account_name) else item.nickName)
        holder.setText(R.id.tvAccountId,item.acountCd)
        holder.setText(R.id.tvAccountAmount, when (item.equitySuccess) {
            "1" -> {
                item.stDetailData.equity.mathAdd(item.stDetailData.followEquity)
                    .numCurrencyFormat(item.stDetailData.currencyType.ifNull())
            }
            "2" -> { "-" }
            else -> { "..." }
        })
        holder.setText(R.id.tvAccountAmountUnit, when (item.equitySuccess) {
            "1" -> { item.stDetailData?.currencyType.ifNull() }
            else -> { "" }
        })
        // 跟单账户不涉及Reset
        // 跟单账户不涉及Read Only

        holder.setBackground(
            R.id.clAccountCard,
            if (selectedAccountCd == item.acountCd)
                ContextCompat.getDrawable(context, R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10)
            else
                ContextCompat.getDrawable(context, R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10)
        )

        when (item.secondSuccess) {
            "1" -> { // 成功
                val stDetailData = item.stDetailData
                val currencyType = stDetailData?.currencyType

                // Manual-Trading
                holder.setText(R.id.tvManualTradingNum, if (stDetailData?.equity.isNullOrEmpty() || currencyType.isNullOrEmpty()) "---" else
                    "${(stDetailData.equity?:"").numCurrencyFormat(currencyType)} $currencyType".arabicReverseTextByFlag(" "))
                // Copy-Trading
                holder.setText(R.id.tvCopyTradingNum, if (stDetailData?.followEquity?.numCurrencyFormat(currencyType ?: "").isNullOrEmpty() || currencyType.isNullOrEmpty()) "---" else
                    "${stDetailData.followEquity?.numCurrencyFormat(currencyType ?: "")} $currencyType".arabicReverseTextByFlag(" "))
                // Margin Level
                holder.setText(
                    R.id.tvMarginLevelNum,
                    if (stDetailData?.marginLevel == "0.0" || stDetailData?.marginLevel.isNullOrEmpty())
                        "---"
                    else
                        "${stDetailData?.marginLevel?.addComma("2")}%"
                )
                // Profit
                val totalProfit = stDetailData?.wholeHistoryProfit?.numCurrencyFormat(currencyType?:"")
                holder.setText(R.id.tvProfitNum, if (totalProfit.isNullOrEmpty() || currencyType.isNullOrEmpty()) "---" else "$totalProfit $currencyType".arabicReverseTextByFlag(" "))
                // Last Login
                val lastLoginDate = if(stDetailData.lastLoginDate.isNullOrBlank()) "" else stDetailData.lastLoginDate
//                holder.setGone(R.id.tvLoginTime, TextUtils.isEmpty(lastLoginDate))
                holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, lastLoginDate))
                // Type
                holder.setText(R.id.tvTypeNum, stDetailData.accountTypeName ?: "-")
                // Leverage
                holder.setText(R.id.tvLeverageNum, "${stDetailData.leverage}:1") //杠杆
            }
            "2" -> { // 失败
                holder.setText(R.id.tvManualTradingNum, "-")
                holder.setText(R.id.tvCopyTradingNum, "-")
                holder.setText(R.id.tvMarginLevelNum, "-")
                holder.setText(R.id.tvProfitNum, "-")
                holder.setText(R.id.tvTypeNum, "-")
                holder.setText(R.id.tvLeverageNum, "-")
                holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, ""))
            }
            else -> { // 未加载
                holder.setText(R.id.tvManualTradingNum, "...")
                holder.setText(R.id.tvCopyTradingNum, "...")
                holder.setText(R.id.tvMarginLevelNum, "...")
                holder.setText(R.id.tvProfitNum, "...")
                holder.setText(R.id.tvTypeNum, "...")
                holder.setText(R.id.tvLeverageNum, "...")
                holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, ""))
            }
        }

        // 归档状态
        if (item.isArchive == true) {
            val expiredColorRes = R.attr.color_c731e1e1e_c61ffffff
            holder.setBackground(R.id.clAccountCard, ContextCompat.getDrawable(context, R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10))
            holder.setTextColor(R.id.tvAccountName, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvAccountAmount, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvAccountAmountUnit, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvManualTrading, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvManualTradingNum, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvCopyTrading, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvCopyTradingNum, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvMarginLevel, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvMarginLevelNum, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvProfit, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvProfitNum, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvLoginTime, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvType, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvTypeNum, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvLeverage, AttrResourceUtil.getColor(context, expiredColorRes))
            holder.setTextColor(R.id.tvLeverageNum, AttrResourceUtil.getColor(context, expiredColorRes))

            holder.setText(R.id.tvAccountType, context.resources.getString(R.string.archived))
            holder.setBackground(R.id.tvAccountType, ContextCompat.getDrawable(context, R.drawable.draw_shape_c731e1e1e_c61ffffff_r100))
            holder.setGone(R.id.linearLayout, true)
            holder.setGone(R.id.ivArrow, true)
            holder.setGone(R.id.tvAccountId, true)
            holder.setVisible(R.id.tvReactivate, true)
        } else {
            holder.setVisible(R.id.linearLayout, true)
            holder.setVisible(R.id.ivArrow, true)
            holder.setVisible(R.id.tvAccountId, true)
            holder.setGone(R.id.tvReactivate, true)
        }

        val clAccountInfo = holder.getView<ConstraintLayout>(R.id.clAccountInfo)
        val ivExtent = holder.getView<ImageView>(R.id.ivExtent)
        if (item.showAccountInfo) {
            if (clAccountInfo.visibility == View.GONE) {
                clAccountInfo.visibility = View.VISIBLE
                holder.setImageResource(R.id.ivExtent,
                    if (item.isArchive == true)
                        R.drawable.draw_bitmap2_arrow_top10x10_c731e1e1e_c61ffffff
                    else
                        R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff
                )
            }
        } else {
            if (clAccountInfo.visibility == View.VISIBLE) {
                clAccountInfo.visibility = View.GONE
                holder.setImageResource(R.id.ivExtent,
                    if (item.isArchive == true)
                        R.drawable.draw_bitmap2_arrow_bottom10x10_c731e1e1e_c61ffffff
                    else
                        R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff
                )
            }
        }
    }
}