package cn.com.vau.page.setting.activity

import android.content.*
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.core.hardware.fingerprint.FingerprintManagerCompat
import androidx.core.os.CancellationSignal
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil.setContentView
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.application.VauApplication.Companion.context
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.sudoku.*
import cn.com.vau.databinding.ActivityConfigAndUnlockBinding
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import org.greenrobot.eventbus.EventBus

/**
 * Filename: ConfigAndUnLockActivity.kt
 * Author: GG
 * Date: 2024/1/22
 * Description: 用于 设置解锁功能 和 完成解锁功能 的页面
 */
class ConfigAndUnlockActivity : BaseMvvmBindingActivity<ActivityConfigAndUnlockBinding>() {

    private var type: String = ""

    private var unlockType: String = ""

    private var changeUnlockType: String? = null

    private val messageInfo: String? by lazy { intent.getStringExtra(KEY_MESSAGE_TYPE) }

    //密码错误次数
    private var errorCount = 4

    //设置手势解锁 第一次输入的密码
    private var pwdSetStr = ""

    private var gestureLockView: GestureLockView? = null

    private val fingerprintManager: FingerprintManagerCompat by lazy { FingerprintManagerCompat.from(context) }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        type = intent.getStringExtra(KEY_TYPE) ?: TYPE_CONFIG
        unlockType = intent.getStringExtra(KEY_UNLOCK_TYPE) ?: if (1 == SpManager.getSecurityOpenSetState(1)) UNLOCK_TYPE_PATTERN else UNLOCK_TYPE_FINGER_PRINT
        changeUnlockType = intent.getStringExtra(KEY_CHANGE_UNLOCK_TYPE)
    }

    override fun initView() {
        mBinding.mHeaderBar.isVisible = type == TYPE_CONFIG
        // 在Activity中处理返回手势
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (type == TYPE_UNLOCK && changeUnlockType.isNullOrBlank()) {
                    //messageInfo NoticeConstants.Unlock.UNLOCK_FROM_NEW_ORDER时 退出同时关闭两个页面
                    when (messageInfo) {
                        NoticeConstants.Unlock.UNLOCK_TO_NEW_ORDER -> {
                            finish()
                        }

                        NoticeConstants.Unlock.UNLOCK_TO_ORDER_LIST -> {
                            finish()
                        }

                        else -> {
                            exitApp()
                        }
                    }
                } else {
                    finish()
                }
            }
        })
        when (unlockType) {
            UNLOCK_TYPE_PATTERN -> {
                mBinding.layoutPatternUnlock.root.isVisible = true
                mBinding.layoutFingerprintUnlock.root.isVisible = false
                patternConfig()
            }

            UNLOCK_TYPE_FINGER_PRINT -> {
                mBinding.layoutFingerprintUnlock.root.isVisible = true
                mBinding.layoutPatternUnlock.root.isVisible = false
                fingerPrintConfig()
            }

            UNLOCK_TYPE_NO_LOCK -> {
                EventBus.getDefault().post(DataEvent(tag = NoticeConstants.Unlock.UNLOCK_CONFIG_SUCCESS, data = unlockType))
                finish()
            }
        }

    }

    /**
     * 配置 手势解锁样式
     */
    private fun patternConfig() {
        gestureLockView = GestureLockView(context, object : Drawl.GestureCallBack {
            override fun checkedSuccess(password: String?) {
                checkPwdCode(password ?: "")
            }

            override fun checkedFail() {

            }
        })
        gestureLockView?.setParentView(mBinding.layoutPatternUnlock.mFrameLayout)
        mBinding.layoutPatternUnlock.tvForgotPwd.setOnClickListener {
            showSecurityErrorDialog(ERROR_FORGET)
        }
        if (type == TYPE_UNLOCK || !changeUnlockType.isNullOrBlank()) {
            mBinding.layoutPatternUnlock.tvHint.text = context.getString(R.string.draw_a_pattern_to_unlock)
            mBinding.layoutPatternUnlock.tvForgotPwd.visibility = View.VISIBLE
            mBinding.layoutPatternUnlock.tvEmail.visibility = View.VISIBLE
            mBinding.layoutPatternUnlock.tvEmail.text = UserDataUtil.email()
        }

        if (type == TYPE_CONFIG && changeUnlockType.isNullOrBlank()) {
            mBinding.layoutPatternUnlock.tvHint.text = context.getString(R.string.create_an_unlock_pattern)
            mBinding.layoutPatternUnlock.tvForgotPwd.visibility = View.INVISIBLE
            mBinding.layoutPatternUnlock.tvEmail.visibility = View.INVISIBLE
        }

        if (type == TYPE_CONFIG && pwdSetStr.isNotBlank()) {
            mBinding.layoutPatternUnlock.tvConfirmPwd.isVisible = true
        }

    }

    /**
     * 分发 手势密码结果
     */
    private fun checkPwdCode(password: String) {
        if (password.length < 4 && type == TYPE_CONFIG) {
            mBinding.layoutPatternUnlock.tvErrorHint.visibility = View.VISIBLE
            return
        }

        mBinding.layoutPatternUnlock.tvErrorHint.visibility = View.INVISIBLE

        if (type == TYPE_UNLOCK) {
            checkPwdIsCorrect(password)
            return
        }

        if (type == TYPE_CONFIG && pwdSetStr.isBlank()) {
            pwdSetStr = password
            initView()
            return
        }

        if (type == TYPE_CONFIG && pwdSetStr.isNotBlank()) {
            if (pwdSetStr == password) {
                errorCount = 4
                EventBus.getDefault().post(DataEvent(tag = NoticeConstants.Unlock.UNLOCK_CONFIG_SUCCESS, data = unlockType))
                SpManager.putPatternUnlock(password)
                finish()
            } else {
                Toast.makeText(context, context.getString(R.string.please_draw_the_previous_one), Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 校验 手势解锁结果
     */
    private fun checkPwdIsCorrect(password: String) {
        if (password == SpManager.getPatternUnlock()) {
            configPass()
        } else {
            if (errorCount > 0) {
                Toast.makeText(context, context.getString(R.string.wrong_password_x_login_attempts_remaining, "$errorCount"), Toast.LENGTH_SHORT).show()
                errorCount--
            } else {
                showSecurityErrorDialog(ERROR_PATTERN)
            }
        }
    }

    private val callback = object : FingerprintManagerCompat.AuthenticationCallback() {

        override fun onAuthenticationSucceeded(result: FingerprintManagerCompat.AuthenticationResult) {
            super.onAuthenticationSucceeded(result)
            if (type == TYPE_UNLOCK) {
                configPass()
                return
            }

            if (type == TYPE_CONFIG) {
                SpManager.putSecurityOpenSetState(2)
                EventBus.getDefault().post(DataEvent(tag = NoticeConstants.Unlock.UNLOCK_CONFIG_SUCCESS, data = unlockType))
                finish()
            }

        }

        override fun onAuthenticationError(errMsgId: Int, errString: CharSequence) {
            super.onAuthenticationError(errMsgId, errString)
//            LogUtils.w("onAuthenticationError: errMsgId=$errMsgId, errString=$errString")
            if (errMsgId == 7) {
                if (type == TYPE_UNLOCK) {
                    ToastUtil.showToast(context.getString(R.string.too_many_failed_please_later))
                    EventBus.getDefault().post(DataEvent(tag = NoticeConstants.Unlock.UNLOCK_ERROR, data = unlockType))
                    showSecurityErrorDialog(ERROR_FINGER_PRINT)
                }
            }
            // errMsgId=5  errString=指纹操作已取消。
            if (errMsgId != 5)
                ToastUtil.showToast(errString.toString())
            // 点击取消 errMsgId=10, errString=10
            // 侧滑关闭弹窗 errMsgId=1010, errString=1010
            if (errMsgId == 10 || errMsgId == 1010) {
                mBinding.layoutFingerprintUnlock.tvVerify.visibility = View.VISIBLE
                return
            }
            if (type == TYPE_UNLOCK) {
                EventBus.getDefault().post(DataEvent(tag = NoticeConstants.Unlock.UNLOCK_ERROR, data = unlockType))
                showSecurityErrorDialog(ERROR_FINGER_PRINT)
            }
            if (type == TYPE_CONFIG) {
                finish()
            }
        }

        override fun onAuthenticationFailed() {
            super.onAuthenticationFailed()
//            LogUtils.w("-------Failed")
            ToastUtil.showToast(context.getString(R.string.incorrect_fignerprint))
        }

        override fun onAuthenticationHelp(helpMsgId: Int, helpString: CharSequence) {
            super.onAuthenticationHelp(helpMsgId, helpString)
//            LogUtils.w("onAuthenticationError: helpMsgId=$helpMsgId, helpString=$helpString")
            ToastUtil.showToast(helpString.toString())
        }
    }

    /**
     * 密码校验通过后的配置
     */
    private fun configPass() {
        if (changeUnlockType.isNullOrBlank()) {
            EventBus.getDefault().post(messageInfo ?: NoticeConstants.Unlock.UNLOCK_SUCCESS)
            SpManager.putInternationalizationSwitch(false)
            finish()
        } else {
            type = TYPE_CONFIG
            unlockType = changeUnlockType ?: UNLOCK_TYPE_PATTERN
            changeUnlockType = null
            initView()
        }
    }

    /**
     * 配置 指纹解锁样式
     */
    private fun fingerPrintConfig() {
        fingerprintManager.authenticate(
            null, 0, CancellationSignal(), callback, null
        )

        if (type == TYPE_UNLOCK || !changeUnlockType.isNullOrBlank()) {
            mBinding.layoutFingerprintUnlock.tvHint.text = getString(R.string.click_to_fingerprint_unlock)
            mBinding.layoutFingerprintUnlock.tvEmail.visibility = View.VISIBLE
            mBinding.layoutFingerprintUnlock.tvEmail.text = UserDataUtil.email()
        }

        if (type == TYPE_CONFIG && changeUnlockType.isNullOrBlank()) {
            mBinding.layoutFingerprintUnlock.tvHint.text = getString(R.string.place_your_finger_on_the_fignerprint_sensor)
            mBinding.layoutFingerprintUnlock.tvEmail.visibility = View.INVISIBLE
        }
        mBinding.layoutFingerprintUnlock.tvVerify.setOnClickListener {
            againWakeupSensor()
        }
    }

    private fun againWakeupSensor() {
        fingerprintManager.authenticate(
            null, 0, CancellationSignal(), callback, null
        )
        mBinding.layoutFingerprintUnlock.tvVerify.visibility = View.GONE
    }

    /**
     * 拦截和分发返回事件
     */
    override fun onBackPressed() {
        if (type == TYPE_UNLOCK && changeUnlockType.isNullOrBlank()) {
            //messageInfo NoticeConstants.Unlock.UNLOCK_FROM_NEW_ORDER时 退出同时关闭两个页面
            when (messageInfo) {
                NoticeConstants.Unlock.UNLOCK_TO_NEW_ORDER -> {
                    finish()
                }

                NoticeConstants.Unlock.UNLOCK_TO_ORDER_LIST -> {
                    finish()
                }

                else -> {
                    exitApp()
                }
            }
        } else {
            super.onBackPressed()
        }
    }

    private var lastClickBackMilli: Long = 0

    private fun exitApp() {
        if (System.currentTimeMillis() - lastClickBackMilli < 2000) {
            InitHelper.breakInit()
            ActivityManagerUtil.getInstance().finishAllActivity()
        } else {
            // 再按一次退出
            Toast.makeText(this, getString(R.string.tap_again_to_close), Toast.LENGTH_SHORT).show()
        }
        lastClickBackMilli = System.currentTimeMillis()
    }

    /**
     * 错误弹窗
     */
    private fun showSecurityErrorDialog(errorType: String) {
        CenterActionDialog.Builder(this)
            .setContent( context.getString(
                when (errorType) {
                    ERROR_FORGET -> R.string.forgot_your_please_automatically
                    ERROR_PATTERN -> R.string.invalid_pattern_unlock_please_login_again
                    ERROR_FINGER_PRINT -> R.string.invalid_fingerprint_please_again
                    else -> R.string.invalid_fingerprint_please_again
                }
            )) //设置内容
            .setSingleButton(errorType == ERROR_PATTERN || errorType == ERROR_FINGER_PRINT) //展示一个按钮，默认两个按钮
            //如果展示一个按钮，点击监听使用setOnSingleButtonListener
            .setOnSingleButtonListener { textView ->
                logOut()
            }
            .setOnEndListener { textView ->
                //默认关闭
                logOut()
            }
            .build()
            .showDialog()
    }

    /**
     * 发送退出登录通知
     */
    private fun logOut() {// 退出登陆
        // 通知到MainActivity做退出登录操作
        EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
        finish()
    }

    companion object {

        private const val KEY_TYPE = "type"

        private const val KEY_UNLOCK_TYPE = "unlock_type"

        private const val KEY_CHANGE_UNLOCK_TYPE = "changeType"

        private const val KEY_MESSAGE_TYPE = "message_type"

        const val TYPE_CONFIG = "config"
        const val TYPE_UNLOCK = "unlock"

        const val UNLOCK_TYPE_PATTERN = "pattern"
        const val UNLOCK_TYPE_FINGER_PRINT = "finger_print"
        const val UNLOCK_TYPE_NO_LOCK = "no_lock"

        private const val ERROR_FORGET = "error_forget"
        private const val ERROR_PATTERN = "error_pattern"
        private const val ERROR_FINGER_PRINT = "error_finger_print"

        /**
         * 跳转到设置和解锁页面
         * @param type 页面类型， TYPE_CONFIG 设置解锁页面  TYPE_UNLOCK 解锁页面
         * @param unlockType  解锁类型  UNLOCK_TYPE_PATTERN  手势解锁  UNLOCK_TYPE_FINGER_PRINT 指纹解锁 UNLOCK_TYPE_NO_LOCK 去除解锁
         * @param changeUnlockType  修改后的解锁类型  UNLOCK_TYPE_PATTERN  手势解锁  UNLOCK_TYPE_FINGER_PRINT 指纹解锁 UNLOCK_TYPE_NO_LOCK 去除解锁
         * @param messageInfo 解锁成功后发送的消息，用于解锁成功后的跳转
         */
        fun open(context: Context, type: String, unlockType: String?, changeUnlockType: String? = null, messageInfo: String? = null) {
            context.startActivity(Intent(context, ConfigAndUnlockActivity::class.java).apply {
                putExtra(KEY_TYPE, type)
                putExtra(KEY_UNLOCK_TYPE, unlockType)
                putExtra(KEY_CHANGE_UNLOCK_TYPE, changeUnlockType)
                putExtra(KEY_MESSAGE_TYPE, messageInfo)
            })
        }

        fun clearLockData() {
            SpManager.putSecuritySetState(0)
            SpManager.putSecurityOpenSetState(0)
            SpManager.putPatternUnlock("")
            SpManager.putUnlockTime(10)
        }
    }
}