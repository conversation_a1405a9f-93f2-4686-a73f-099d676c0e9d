package cn.com.vau.page.start

import android.view.View
import androidx.annotation.Keep
import cn.com.vau.R
import cn.com.vau.util.dp2px
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: ViewPager2Adapter
 * Author: 76926
 * Date: 2023/8/3 0003 16:05
 * Description:
 */
class ViewPager2Adapter(data: MutableList<WelcomeBean>) : BaseQuickAdapter<WelcomeBean, BaseViewHolder>(R.layout.item_viewpager, data = data) {
    override fun convert(holder: BaseViewHolder, item: WelcomeBean) {
        holder.setText(R.id.tvTitle, item.title)
            .setText(R.id.tvDetail, item.detail)
        val viewIndicator1 = holder.getView<View>(R.id.viewIndicator1)
        val viewIndicator2 = holder.getView<View>(R.id.viewIndicator2)
        val viewIndicator3 = holder.getView<View>(R.id.viewIndicator3)
        clearBg(viewIndicator1)
        clearBg(viewIndicator2)
        clearBg(viewIndicator3)
        when (holder.bindingAdapterPosition) {
            0 -> selectIndex(viewIndicator1)
            1 -> selectIndex(viewIndicator2)
            2 -> selectIndex(viewIndicator3)
        }
    }

    private fun selectIndex(view: View) {
        view.setBackgroundResource(R.drawable.draw_shape_cebffffff_r2)
        val layoutParams = view.layoutParams
        layoutParams.width = 10.dp2px()
        view.layoutParams = layoutParams
    }

    private fun clearBg(view: View) {
        view.setBackgroundResource(R.drawable.draw_shape_c61ffffff_r2)
        val layoutParams = view.layoutParams
        layoutParams.width = 3.dp2px()
        view.layoutParams = layoutParams
    }
}

@Keep
data class WelcomeBean(val title: String, val detail: String)