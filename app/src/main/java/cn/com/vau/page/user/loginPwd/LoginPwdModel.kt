package cn.com.vau.page.user.loginPwd

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.*
import cn.com.vau.data.profile.TelegramGetBotBean
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class LoginPwdModel : LoginPwdContract.Model {
    override fun pwdLoginApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().loginNewApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun bindUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().thirdpartyBindApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun getAreaCodeApi(baseObserver: BaseObserver<SelectCountryNumberBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().selectCountryNumberClassifyScreeningApi(), baseObserver)
        return baseObserver.disposable
    }

    override fun getBindingTelSMSApi(
        map: HashMap<String, Any?>,
        baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getTelSMSApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun passKeyLoginDataApi(
        map: HashMap<String, String?>,
        baseObserver: BaseObserver<PasskeyLoginData>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().passKeyLoginDataApi(map), baseObserver)
        return baseObserver.disposable
    }

    /**
     * 发送邮箱验证码
     */
    override fun emailSendEmailCodeApi(
        map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().emailSendEmailCodeApi(map), baseObserver)
        return baseObserver.disposable
    }

    /**
     * 三方登录
     */
    override fun thirdPartyLoginApi(
        map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().thirdpartyLoginApi(map), baseObserver)
        return baseObserver.disposable
    }

    /**
     * telegram-获取botId
     */
    override fun telegramGetBotIdApi(baseObserver: BaseObserver<TelegramGetBotBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().telegramGetBotIdApi(), baseObserver)
        return baseObserver.disposable
    }
}
