package cn.com.vau.page.coupon

import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import androidx.core.text.underline
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.widget.CouponProgressView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 02 17:28
 * @updateUser:
 * @updateDate: 2025 4月 02 17:28
 */
class CouponAdapter(private val pageType: Int) : BaseQuickAdapter<DepositCouponDetail, BaseViewHolder>(R.layout.item_recycler_coupon) {

    init {
        addChildClickViewIds(R.id.tvButton, R.id.tvDetails)
    }

    override fun convert(holder: BaseViewHolder, item: DepositCouponDetail) {
        holder.run {
            val tvCouponAmount = getViewOrNull<AppCompatTextView>(R.id.tvCouponAmount)
            val tvCouponType = getViewOrNull<AppCompatTextView>(R.id.tvCouponType)
            val tvCouponContent = getViewOrNull<AppCompatTextView>(R.id.tvCouponContent)
            val tvEndTime = getViewOrNull<AppCompatTextView>(R.id.tvEndTime)
            val cpView = getViewOrNull<CouponProgressView>(R.id.cpView)
            val tvButton = getViewOrNull<AppCompatTextView>(R.id.tvButton)
            val tvDetails = getViewOrNull<AppCompatTextView>(R.id.tvDetails)
            tvCouponAmount?.text = item.amountDes ?: ""
            // 1：满减券，2：折扣券，3：现金券, 4:信用券, 5:抹亏券, 6.满加券, 7.订单卷, 9.抹亏保险券，10.入金返佣券，11.抹手续费券，12.盈利翻倍券
            tvCouponType?.text = context.getString(
                when (item.couponType) {
                    "1", "6" -> R.string.deposit_coupon
                    "3" -> R.string.cash_voucher
                    "4" -> R.string.credit_voucher
                    "5" -> R.string.trade_loss_voucher
                    "7" -> R.string.cfd_voucher
                    "9" -> R.string.loss_protection
                    "10" -> R.string.deposit_rebate
                    "11" -> R.string.commission_fee
                    "12" -> R.string.profit_booster
                    else -> R.string.voucher
                }
            )

            tvCouponContent?.text = item.couponDes ?: ""

            tvEndTime?.setTextColor(if (pageType == CouponFragment.TYPE_ACTIVE) ContextCompat.getColor(context, R.color.ce35728) else AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))

            if (item.userCouponStatus == "0" || pageType == CouponFragment.TYPE_USED || pageType == CouponFragment.TYPE_EXPIRED) {
                setTextViewGrey(tvCouponAmount, tvCouponType, tvCouponContent)
                tvButton?.isVisible = false
                if (item.requiredLots != null && item.requiredLots != 0.0) {
                    setTextViewGrey(tvCouponAmount, tvCouponType, tvCouponContent)
                    cpView?.isVisible = true
                    cpView?.setProgress(item.usedLots, item.requiredLots)
                } else {
                    cpView?.isVisible = false
                }
            } else {
                if (item.requiredLots != null && item.requiredLots != 0.0) {
                    setTextViewGrey(tvCouponAmount, tvCouponType, tvCouponContent)
                    cpView?.isVisible = true
                    tvButton?.isVisible = false
                    cpView?.setProgress(item.usedLots, item.requiredLots)
                } else {
                    cpView?.isVisible = false
                    tvCouponAmount?.setTextColor(ContextCompat.getColor(context, R.color.ce35728))
                    tvCouponType?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
                    tvCouponContent?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
                    tvButton?.isVisible = true
                    if (item.userCouponStatus == "2" && (item.couponType == "1" || item.couponType == "6" || item.couponType == "10")) {
                        tvButton?.text = context.getString(R.string.release)
                        tvButton?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                        tvButton?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                    } else {
                        if (item.userCouponStatus == "4") {
                            tvButton?.text = context.getString(R.string.activated)
                            tvButton?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                            tvButton?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
                        } else {
                            tvButton?.text = context.getString(R.string.use)
                            tvButton?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                            tvButton?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                        }
                    }
                }
            }

            tvDetails?.isInvisible = item.infoUrl.isNullOrEmpty()
            tvDetails?.text = buildSpannedString { underline { append(context.getString(R.string.terms_conditions)) } }

            tvEndTime?.text = when (pageType) {
                CouponFragment.TYPE_USED -> item.useTime

                CouponFragment.TYPE_EXPIRED -> item.etime

                else -> context.getString(R.string.x_days_remaining, item.remainDays ?: " ")

            }
        }
    }

    private fun setTextViewGrey(vararg textViews: AppCompatTextView?) {
        textViews.forEach {
            it?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
        }
    }
}