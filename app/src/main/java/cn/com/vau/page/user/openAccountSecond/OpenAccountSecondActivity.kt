package cn.com.vau.page.user.openAccountSecond

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.*
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.*
import cn.com.vau.data.account.*
import cn.com.vau.databinding.ActivityOpenAcountSecondWhiteBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstSecondActivity
import cn.com.vau.page.user.openAccountFirst.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import org.greenrobot.eventbus.*
import org.json.JSONObject
import kotlin.collections.getOrElse

/**
 * Created by Haipeng.
 */

@SuppressLint("SetTextI18n")
class OpenAccountSecondActivity :
    BaseFrameActivity<OpenAccountSecondPresenter, OpenAccountCacheModel>(),
    OpenAccountCacheContract.View {

    private var isNext: Boolean = false
    private val binding by lazy { ActivityOpenAcountSecondWhiteBinding.inflate(layoutInflater) }

    val mAdapter by lazy { OpenAccountSecondAdapter(mPresenter.employmentSelected) }
    val optionAdapter by lazy { OpenAccountOptionAdapter() }

    private val selectPopupWindow: BottomListDialog by lazy {
        BottomListDialog.Builder(this).build()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        EventBus.getDefault().register(this)
    }

    override fun initParam() {
        super.initParam()
        // 0账户列表 1我的  2新人专享  3注册
        mPresenter.isFrom = intent?.extras?.getInt(Constants.SOUCE_OPEN_ACOUNT) ?: 0
    }

    @SuppressLint("ObsoleteSdkInt", "SetTextI18n")
    override fun initView() {
        super.initView()
        binding.mRecyclerView.adapter = mAdapter

        mPresenter.getListData()
        // 神策自定义埋点(v3500)，页面浏览事件
        sensorsTrack()
    }

    override fun initListener() {
        super.initListener()
        binding.tvNext.setOnClickListener(this)

        mAdapter.setOnItemClickListener { _, _, position ->
            showSelectData(position)
        }

        binding.mHeaderBar.setStartBackIconClickListener {
            goBack()
        }

        binding.mHeaderBar.setEndIconClickListener {
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFirstActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFirstSecondActivity::class.java)
            finish()
        }

        binding.mHeaderBar.setEndIcon1ClickListener {
            openActivity(HelpCenterActivity::class.java)
        }

    }

    private fun initNextView() {
        if (mPresenter.employmentSelected.values.contains(-1)) {
            mPresenter.employmentList.forEach { questionObj ->
                if (mPresenter.employmentSelected.getOrElse(questionObj.questionId.ifNull()) { -1 } == -1) {
                    isNext = false
                    return
                }
            }
//            isNext = false
//            return
        }
        isNext = true

        binding.tvNext.setBackgroundResource(
            if (isNext)
                R.drawable.bitmap_icon2_next_active
            else
                R.drawable.bitmap_icon2_next_inactive
        )

    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvNext -> goNext()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun showRealInfo(data: RealAccountCacheObj?) {
        mAdapter.setList(mPresenter.dataList)
        val isASIC = ("1" == data?.supervisionType)

        if (isASIC) {
            val asicTxt = "Vantage Global Prime Pty Ltd's ${getString(R.string.privacy_policy)}."
            val urlTextAsic = getString(R.string.privacy_policy)
            binding.tvVantageLink.text = asicTxt
            binding.tvVantageLink.set(urlTextAsic, AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff)) {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putInt("tradeType", 15)
                    putString("title", urlTextAsic)
                })
            }
        }

        initNextView()
    }

    override fun goNext() {
        if (!isNext) return
        mPresenter.saveRealInfo()

        // 神策自定义埋点(v3500)，点击事件
        sensorsTrackClick()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            goBack()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun goBack() {
        finish()
        if (ActivityManagerUtil.getInstance().secondActivity !is OpenAccountFirstSecondActivity) {
            val bundle = Bundle()
            bundle.putInt(Constants.SOUCE_OPEN_ACOUNT, mPresenter.isFrom)
            bundle.putBoolean("needFreshStyle", false)
            openActivity(OpenAccountFirstSecondActivity::class.java, bundle)
        }
    }

    override fun refreshOpenGuide() {
        super.refreshOpenGuide()
        EventBus.getDefault().post(NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun showSecondStepDialog(
        resultCode: String?,
        msgInfo: String?,
        url: String?,
        isFrom: Int,
        cacheData: RealAccountCacheObj?
    ) {
        when (resultCode) {
            "V10031" -> {
                CenterActionDialog.Builder(this)
                    .setContent(msgInfo)
                    .setSingleButton(true)
                    .setOnSingleButtonListener {
                            mPresenter.employmentSelected.forEach {
                                mPresenter.employmentSelected.put(it.key, -1)
                            }
                            mAdapter.notifyDataSetChanged()
                    }
                    .build().showDialog()
            }

            "V10032" -> {
                val dialog = CenterActionDialog.Builder(this)
                    .setSingleButton(true)
                    .setSingleButtonText(getString(R.string.ok))
                    .setOnSingleButtonListener {
                        //Update UI
                        mPresenter.isGoToNextStep = 1
                        mPresenter.saveRealInfo()
                    }
                    .build()
                val contentTextView = dialog.getContentViewBinding().tvDetail
                val wholeText = getString(R.string.we_recommend_you_and_demo_account_start)
                val spannableString = SpannableString(wholeText)
                val span = getString(R.string.link_start)
                val index = wholeText.indexOf(span)
                if (index != -1 && (index + span.length) <= wholeText.length) {
                    spannableString.setSpan(object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            startActivity(browserIntent)
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = true
                            ds.color = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
                        }
                    }, index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    contentTextView.movementMethod = LinkMovementMethod.getInstance()
                    contentTextView.highlightColor = ContextCompat.getColor(context, R.color.transparent)
                }
                (dialog as CenterActionDialog).setContent(spannableString)
                dialog.showDialog()
            }
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: DataEvent) {
    }

    /**
     * 展示弹窗
     * tradeType 0雇佣状态 1职业  2收入 3储备金与投资 4收入来源
     */
    override fun showSelectData(mPosition: Int) {

        val questionObj = mPresenter.employmentList.elementAtOrNull(mPosition)
        val dataList = questionObj?.questionOptions ?: ArrayList()
        val questionId = questionObj?.questionId.ifNull()

        optionAdapter.setSelectedId(mPresenter.employmentSelected[questionId] ?: -1)
        optionAdapter.setList(dataList)
        optionAdapter.setOnItemClickListener { _, _, position ->
            val optionId = dataList.elementAtOrNull(position)?.id ?: -1
            mPresenter.employmentSelected.put(questionId, optionId)
            mAdapter.notifyItemChanged(mPosition)
            selectPopupWindow.dismiss()
            initNextView()
        }
        selectPopupWindow.setTitle(questionObj?.desc.ifNull())
        selectPopupWindow.setAdapter(optionAdapter)
        selectPopupWindow.showDialog()


//        selectPopupWindow.setBaseListData(
//            dataList,
//            mPresenter.employmentSelected[questionId] ?: -1,
//            questionObj?.desc ?: ""
//        )
//            .setOnPopClickListener(object : OpenAccountListBottomPopupWindow.OnPopClickListener {
//                @SuppressLint("NotifyDataSetChanged")
//                override fun onItemClick(position: Int) {
//                    val optionId = dataList.elementAtOrNull(position)?.id ?: -1
//                    mPresenter.employmentSelected.put(questionId, optionId)
//                    mAdapter.notifyDataSetChanged()
//                    initNextView()
//                }
//            })
//            .showAtLocation(binding.clParent, Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL, 0, 0)
//        setAlpha(0.2f)

    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    private fun sensorsTrack() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    private fun sensorsTrackClick() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, "ASIC-Next") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }
}