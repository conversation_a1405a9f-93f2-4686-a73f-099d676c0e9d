package cn.com.vau.page.notice.activity

import android.widget.TextView
import androidx.core.app.NotificationManagerCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.msg.MsgInAppTypeData
import cn.com.vau.databinding.ActivityNoticeBinding
import cn.com.vau.databinding.VsLayoutNoticeEnableBinding
import cn.com.vau.page.notice.fragment.NoticeFragment
import cn.com.vau.page.notice.viewmodel.NoticeViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * @description:
 * @author: GG
 * @createDate: 2024 12月 17 13:41
 * @updateUser:
 * @updateDate: 2024 12月 17 13:41
 */
class NoticeActivity : BaseMvvmActivity<ActivityNoticeBinding, NoticeViewModel>() {

    private var vsNoticeEnableBinding: VsLayoutNoticeEnableBinding? = null

    override fun initView() {
        val fragmentList: MutableList<Fragment> = mViewModel.titleList.map { NoticeFragment.newInstance(it.code) }.toMutableList()
        val tabList = mViewModel.titleList.map { it.name.ifNull() }.toMutableList()
        mBinding.mViewPager2.init(fragmentList, tabList, supportFragmentManager, this)
        mBinding.tabLayout.setVp(mBinding.mViewPager2, tabList, TabType.LINE_INDICATOR) { position ->
            SensorsDataUtil.track(SensorsConstant.V3540.MESSAGES_PAGE_TAB_CLICK, JSONObject().apply {
                put(
                    SensorsConstant.Key.TAB_NAME, when (mViewModel.titleList.getOrNull(position)?.code) {
                        NoticeViewModel.TYPE_TRADE -> "Trade"
                        NoticeViewModel.TYPE_COPY_TRADING -> "Copy Trading"
                        NoticeViewModel.TYPE_PRICE_ALERT -> "Price Alerts"
                        NoticeViewModel.TYPE_ACCOUNT -> "Account"
                        NoticeViewModel.TYPE_ANNOUNCEMENT -> "Announcements"
                        else -> "News & Events"
                    }
                )
            })
        }
        SensorsDataUtil.track(SensorsConstant.V3540.MESSAGES_PAGE_VIEW)
    }

    override fun initListener() {
        mBinding.mHeaderBar.setEndIcon1ClickListener {
            if (mViewModel.unReadNoticeCount == 0) {
                ToastUtil.showToast(getString(R.string.no_unread_message))
            } else {
                CenterActionDialog.Builder(this)
                    .setContent(getString(R.string.mark_all_messages_as_read)) //设置内容
                    .setStartText(getString(R.string.no))//设置左侧按钮文本
                    .setEndText(getString(R.string.yes))//设置右侧按钮文本
                    //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                    .setOnEndListener { textView ->
                        //默认关闭
                        mViewModel.msgInAppReadAllApi()
                    }
                    .build()
                    .showDialog()
            }
            SensorsDataUtil.track(SensorsConstant.V3540.MESSAGES_PAGE_ONE_CLICK_READ_CLICK)
        }
        mBinding.vsNoticeEnable.setOnInflateListener { _, view ->
            vsNoticeEnableBinding = VsLayoutNoticeEnableBinding.bind(view)
            vsNoticeEnableBinding?.linkTvNotificationEnableTip?.text = buildString {
                append(getString(R.string.unable_to_receive_app_notifications))
                append(getString(R.string.enable_now))
            }
            vsNoticeEnableBinding?.linkTvNotificationEnableTip?.set(getString(R.string.enable_now), getColor(R.color.ce35728), isShowUnderLine = false) {
                IntentUtil.launchAppDetailsSettings()
                SensorsDataUtil.track(SensorsConstant.V3540.MESSAGES_PAGE_ENABLE_NOW_CLICK)
            }
            vsNoticeEnableBinding?.ivCloseTip?.setOnClickListener {
                SpManager.putNoticeNotificationDialogShow(true)
                mBinding.vsNoticeEnable.isVisible = false
            }
        }
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(NoticeSettingActivity::class.java)
        }
    }

    override fun createObserver() {
        super.createObserver()

        lifecycleScope.launch {
            mViewModel.eventFlow.collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    NoticeViewModel.EVENT_SHOW -> {
                        if (it.data !is MsgInAppTypeData.Obj) return@collectLatest
                        showCount(it.data.code, it.data.count)
                    }

                    NoticeViewModel.EVENT_READ -> {
                        reduceCount(it.data.toString())
                    }

                    NoticeViewModel.EVENT_READ_ALL -> {
                        mViewModel.titleList.forEach {
                            showCount(it.code)
                        }
                    }
                }
            }
        }
    }

    private fun showCount(type: String?, count: String? = null) {
        val index = mViewModel.titleList.indexOfFirst { it.code == type }
        if (index != -1) {
            mBinding.tabLayout.getChildAt(index)?.findViewById<TextView>(R.id.tvCount)?.let {
                it.isGone = count.isNullOrEmpty()
                it.text = if (count.toIntCatching(0) > 99) "99+" else count
            }
        }
    }

    private fun reduceCount(type: String) {
        val index = mViewModel.titleList.indexOfFirst { it.code == type }
        if (index != -1) {
            mBinding.tabLayout.getChildAt(index)?.findViewById<TextView>(R.id.tvCount)?.let {
                // 使用本地记录的数字
                val data = mViewModel.titleList.getOrNull(index)
                val count = data?.count.toIntCatching(0) - 1
                mViewModel.titleList.getOrNull(index)?.count = count.toString()
                it.isGone = count <= 0
                it.text = if (count > 99) "99+" else count.toString()
                // tab上显示的-1之后 将记录的总数也-1
                mViewModel.unReadNoticeCount--
                // 判断如果总数<=0 就发送隐藏小红点的通知
                if (mViewModel.unReadNoticeCount <= 0) {
                    SpManager.putRedPointState(false)
                    EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_HIDE)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        showNotificationTipsOrNot()
    }

    private fun checkNotificationEnable(): Boolean {
        val manager = NotificationManagerCompat.from(this)
        val isOpened = manager.areNotificationsEnabled()
        return isOpened
    }

    private fun showNotificationTipsOrNot() {
        val isOpen = checkNotificationEnable()
        mBinding.vsNoticeEnable.isVisible = isOpen.not() && SpManager.getNoticeNotificationDialogShow(false).not()
    }

}