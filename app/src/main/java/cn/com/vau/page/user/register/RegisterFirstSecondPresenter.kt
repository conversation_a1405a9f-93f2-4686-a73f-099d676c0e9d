package cn.com.vau.page.user.register

import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.VerificationCodeData
import cn.com.vau.page.RegisterRequestBean
import cn.com.vau.util.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 12
 */
class RegisterFirstSecondPresenter : RegistestContract.Presenter() {

    var registerRequestBean: RegisterRequestBean = RegisterRequestBean()

    var isFirstCount = true

    private val sendCodeUtil by lazy { SendCodeUtil() }

    fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener) {
        sendCodeUtil.initData(60, listener)
    }

    fun startSendCodeUtil() {
        if (sendCodeUtil.isAlive() != false) {
            sendCodeUtil.start()
            mRxManager.add(sendCodeUtil.disposable)
        }
    }

    fun stopSendCodeUtil() {
        sendCodeUtil.cancel()
    }

    override fun getCodeApi(validateCode: String, smsSendType: String) {
        val phoneCountryCode = registerRequestBean.countryCode ?: Constants.defaultCountryCode
        val code = registerRequestBean.countryNum ?: Constants.defaultCountryNum
        val mobile = registerRequestBean.mobile
        if (
            TextUtils.isEmpty(registerRequestBean.mobile) ||
            (code == "86" && mobile?.length != 11) || (code != "86" && (mobile?.length ?: 0) > 15)
        ) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_enter_the_number))
            return
        }
        if (registerRequestBean.readingProtocol == false) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_read_and_registration_agreement))
            return
        }
        val map = hashMapOf<String, Any>()
        if (validateCode.isNotEmpty()) {
            map["recaptcha"] = validateCode
            map["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        map["userTel"] = mobile ?: ""
        map["phoneCountryCode"] = phoneCountryCode
        map["code"] = code
        map["smsSendType"] = smsSendType
        mView?.showNetDialog()
        mModel?.getCodeApi(map, object : BaseObserver<VerificationCodeData>() {
            override fun onNext(data: VerificationCodeData) {
                SpManager.putSmsCodeId("")
                mView?.hideNetDialog()
                if (data.resultCode == "V10030") {//联系人手机号存在
                    mView?.jumpToLogin(99, data.msgInfo)
                    return
                }
                if (data.resultCode == "V10060") {//易盾 需要滑动窗口
                    mView?.showCaptcha()
                    SpManager.putSmsCodeId(data.data?.obj?.smsCodeId ?: "")
                    return
                }
                ToastUtil.showToast(data.msgInfo)
                if (data.resultCode != "V00000") return
                startSendCodeUtil()
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    fun checkSmsCodeApi(smsCode: String?) {

        val code = registerRequestBean.countryNum ?: Constants.defaultCountryNum
        val mobile = registerRequestBean.mobile

        if (
            TextUtils.isEmpty(registerRequestBean.mobile) ||
            (code == "86" && registerRequestBean.mobile?.length != 11) ||
            (code != "86" && (registerRequestBean.mobile?.length ?: 0) > 15)
        ) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_enter_the_number))
            return
        }

        if (
            TextUtils.isEmpty(registerRequestBean.mobile) ||
            (registerRequestBean.countryCode == "86" && registerRequestBean.mobile?.length != 11) ||
            (registerRequestBean.countryCode != "86" && (registerRequestBean.mobile?.length
                ?: 0) > 15)
        ) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_enter_the_number))
            return
        }

        if ((smsCode?.length ?: 0) != 6) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_enter_the_code))
            return
        }

        if (registerRequestBean.readingProtocol == false) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_read_and_registration_agreement))
            return
        }

        mView?.showNetDialog()

        val map = hashMapOf<String, Any>()
        map.put("phoneNum", mobile ?: "")
        map.put("validateCode", smsCode ?: "")
        map.put("code", code)

        mModel?.checkSmsCodeApi(map, object : BaseObserver<BaseBean>() {
            override fun onNext(data: BaseBean) {

                mView?.hideNetDialog()

                if (data.resultCode != "V00000") {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                registerRequestBean.smsCode = smsCode
                SpManager.putCountryCode(registerRequestBean.countryCode.ifNull())
                SpManager.putCountryNum(registerRequestBean.countryNum.ifNull())
                SpManager.putCountryName(registerRequestBean.countryName.ifNull())

                mView?.goSecond()

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }
}