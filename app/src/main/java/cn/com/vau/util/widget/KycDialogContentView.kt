package cn.com.vau.util.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.LayoutKycDialogContentViewBinding
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_1
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_2
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_3
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_BANK
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_FACE
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.STATUS_AUDITING
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.STATUS_AUTHENTICATED
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.STATUS_PENDING
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.STATUS_REJECTED
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: KycAuthLimitView
 * Author: GG
 * Date: 2025/3/12
 * Description:
 */
class KycDialogContentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding: LayoutKycDialogContentViewBinding by lazy(LazyThreadSafetyMode.NONE) {
        LayoutKycDialogContentViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    private val mAdapter: Adapter by lazy(LazyThreadSafetyMode.NONE) {
        Adapter()
    }

    init {
        mBinding.root.adapter = mAdapter
        mBinding.root.addItemDecoration(DividerItemDecoration(12.dp2px(), lastDividerSize = 0.dp2px()))
    }

    /**
     * 设置标题
     */
    fun setData(dataList: MutableList<Pair<Int, Int>>?) {
        mAdapter.setList(dataList)
    }

    inner class Adapter : BaseQuickAdapter<Pair<Int, Int>, BaseViewHolder>(R.layout.item_recycler_kyc_dialog_content) {
        override fun convert(holder: BaseViewHolder, item: Pair<Int, Int>) {
            holder.setText(R.id.tvTitle, getLevelTitle(item.first))
            updateVerifyStateView(holder.getViewOrNull(R.id.tvStatus), item.second)
        }

        private fun getLevelTitle(level: Int) = when (level) {
            LEVEL_1 -> context.getString(R.string.personal_details_verification)
            LEVEL_2 -> context.getString(R.string.identity_verification)
            LEVEL_3 -> context.getString(R.string.residency_address_verification)
            LEVEL_BANK -> context.getString(R.string.bank_transfer_verification)
            LEVEL_FACE -> context.getString(R.string.face_verification)
            else -> ""
        }

        private fun updateVerifyStateView(statusView: AppCompatTextView?, showLevelStatus: Int?) {
            if (statusView == null) return
            when (showLevelStatus) {
                STATUS_AUTHENTICATED -> {
                    statusView.text = context.getString(R.string.verified)
                    statusView.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                    statusView.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_ce5f7f3_c283b3d_r100)
                    val drawable = ContextCompat.getDrawable(context, R.drawable.bitmap_img_source_tick11x8_c00c79c)
                    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
                    statusView.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
                }

                STATUS_AUDITING -> {
                    statusView.text = context.getString(R.string.under_review)
                    statusView.setTextColor(ContextCompat.getColor(context, R.color.ce35728))
                    statusView.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_cf9ebe6_c3b2f2f_r100)
                    statusView.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)

                }

                STATUS_PENDING -> {
                    statusView.text = context.getString(R.string.pending)
                    statusView.setTextColor(ContextCompat.getColor(context, R.color.c007fff))
                    statusView.background = ContextCompat.getDrawable(context, AttrResourceUtil.getDrawable(context, R.attr.color_ce0f0ff_c0c2c4d))
                    statusView.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                }

                STATUS_REJECTED -> {
                    statusView.text = context.getString(R.string.rejected)
                    statusView.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                    statusView.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_cfae9e8_c3c2d32_r100)
                    statusView.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)

                }

                else -> {
                    statusView.text = context.getString(R.string.not_submitted)
                    statusView.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
                    statusView.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_ce8e8e8_c414348_r100)
                    statusView.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                }
            }
        }
    }
}