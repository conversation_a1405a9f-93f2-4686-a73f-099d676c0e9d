package cn.com.vau.util.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.withStyledAttributes
import androidx.core.view.isGone
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.databinding.LayoutKycAuthRequirementsViewBinding

/**
 * Filename: KycAuthLimitView
 * Author: GG
 * Date: 2025/3/12
 * Description:
 */
class KycAuthRequirementsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding: LayoutKycAuthRequirementsViewBinding by lazy(LazyThreadSafetyMode.NONE) {
        LayoutKycAuthRequirementsViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    init {
        initXmlAttrs(context, attrs)
    }

    private fun initXmlAttrs(context: Context, attrs: AttributeSet?) {
        context.withStyledAttributes(attrs, R.styleable.KycAuthRequirementsView) {
            val requirements = getString(R.styleable.KycAuthRequirementsView_requirements)
            setRequirements(requirements)
        }
    }

    /**
     * 设置标题
     */
    fun setRequirements(requirements: CharSequence?) {
        mBinding.tvVerifyDesc.text = requirements
        mBinding.tvVerifyDesc.isVisible = !requirements.isNullOrEmpty()
        checkViewVisible()
    }

    private fun checkViewVisible() {
        mBinding.root.isGone = mBinding.tvVerifyDesc.isGone
    }

}