package cn.com.vau.util

import androidx.lifecycle.*
import cn.com.vau.common.mvvm.state.ListUIState
import com.chad.library.adapter.base.BaseQuickAdapter
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlin.reflect.KProperty1

/**
 * @description:
 * @author: GG
 * @createDate: 2024 9月 23 15:28
 * @updateUser:
 * @updateDate: 2024 9月 23 15:28
 */
/**
 *  仅限新mvvm框架的list刷新使用，adapter限制为BRVAH的adapter，refreshLayout可空，默认设置了刷新、加载更多、加载到底了、没有数据空布局以及加载失败几种情况
 */
fun <T> MutableLiveData<ListUIState<List<T>?>>.observeUIState(
    owner: LifecycleOwner,
    adapter: BaseQuickAdapter<T, *>,
    refreshLayout: SmartRefreshLayout? = null,
    before: (() -> Unit)? = null,
    refreshSuccessCallBack: ((List<T>?) -> Unit)? = null,
    loadMoreSuccessCallBack: ((List<T>?) -> Unit)? = null,
    loadEndCallBack: ((List<T>?) -> Unit)? = null,
    emptyCallBack: (() -> Unit)? = null,
    errorCallBack: (() -> Unit)? = null,
    after: (() -> Unit)? = null
) {
    observe(owner) {
        before?.invoke()
        when (it) {
            is ListUIState.RefreshSuccess -> {
                adapter.setList(it.data)
                refreshLayout?.finishRefresh()
                refreshLayout?.resetNoMoreData()
                refreshSuccessCallBack?.invoke(it.data)
            }

            is ListUIState.LoadMoreSuccess -> {
                adapter.addData(it.data.orEmpty())
                refreshLayout?.finishLoadMore()
                loadMoreSuccessCallBack?.invoke(it.data)
            }

            is ListUIState.LoadEnd -> {
                adapter.addData(it.data.orEmpty())
                refreshLayout?.finishLoadMoreWithNoMoreData()
                loadEndCallBack?.invoke(it.data)
            }

            is ListUIState.Empty -> {
                adapter.setList(null)
                refreshLayout?.finishRefreshWithNoMoreData()
                refreshLayout?.finishLoadMoreWithNoMoreData()
                emptyCallBack?.invoke()
            }

            is ListUIState.Error -> {
                refreshLayout?.finishRefresh(false)
                refreshLayout?.finishLoadMore(false)
                errorCallBack?.invoke()
            }
        }
        after?.invoke()
    }
}

fun <T, A> LiveData<T>.observeState(
    lifecycleOwner: LifecycleOwner,
    prop1: KProperty1<T, A>,
    action: (A) -> Unit
) {
    this.map {
        State1(prop1.get(it))
    }.observe(lifecycleOwner) { (a) ->
        action.invoke(a)
    }
}

// 监听一个属性去重
fun <T, A> LiveData<T>.observeStateDistinct(
    lifecycleOwner: LifecycleOwner,
    prop1: KProperty1<T, A>,
    action: (A) -> Unit
) {
    this.map {
        State1(prop1.get(it))
    }.distinctUntilChanged().observe(lifecycleOwner) { (a) ->
        action.invoke(a)
    }
}

//监听两个属性
fun <T, A, B> LiveData<T>.observeState(
    lifecycleOwner: LifecycleOwner,
    prop1: KProperty1<T, A>,
    prop2: KProperty1<T, B>,
    action: (A, B) -> Unit
) {
    this.map {
        State2(prop1.get(it), prop2.get(it))
    }.distinctUntilChanged().observe(lifecycleOwner) { (a, b) ->
        action.invoke(a, b)
    }
}

//监听两个属性去重
fun <T, A, B> LiveData<T>.observeStateDistinct(
    lifecycleOwner: LifecycleOwner,
    prop1: KProperty1<T, A>,
    prop2: KProperty1<T, B>,
    action: (A, B) -> Unit
) {
    this.map {
        State2(prop1.get(it), prop2.get(it))
    }.distinctUntilChanged().observe(lifecycleOwner) { (a, b) ->
        action.invoke(a, b)
    }
}

internal data class State1<A>(val a: A)
internal data class State2<A, B>(val a: A, val b: B)

//更新State
fun <T> MutableLiveData<T>.setState(reducer: T.() -> T) {
    this.value = this.value?.reducer()
}