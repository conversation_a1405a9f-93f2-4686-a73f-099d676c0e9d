package cn.com.vau.util

import cn.com.vau.common.base.rx.BaseObserver
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit

/**
 * Created by THINKPAD on 2018/11/5.
 */

class SendCodeUtil {

    private var second: Int = 60
    private var listener: SendCodeListener? = null
    fun initData(second: Int = 60, listener: SendCodeListener?) {
        this.second = second
        this.listener = listener
    }

    var disposable: Disposable? = null

    fun start() {
        countdown(second)
            .subscribe(object : BaseObserver<Int>() {
                override fun onNext(t: Int) {
                    if (t == 0)
                        onFinish()
                    else
                        onTick(t)
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    <EMAIL> = d
                }

            })
    }

    private fun countdown(t: Int): Observable<Int> {
        var time = t
        if (time < 0) time = 0

        return Observable.interval(0, 1, TimeUnit.SECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .map { aLong -> time - aLong.toInt() }
            .take((time + 1).toLong())
    }

    fun isAlive(): Boolean? = disposable?.isDisposed

    private fun onTick(millisUntilFinished: Int) {
        listener?.onTick(millisUntilFinished)
    }

    private fun onFinish() {
        listener?.onFinish()
    }

    fun cancel() {
        disposable?.dispose()
        // todo listener=null
    }

    interface SendCodeListener {
        fun onFinish()
        fun onTick(millisUntilFinished: Int)
    }
}
