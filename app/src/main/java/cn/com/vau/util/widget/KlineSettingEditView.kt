package cn.com.vau.util.widget

import android.content.Context
import android.text.Editable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.R
import cn.com.vau.common.view.popup.bean.KlineIndicatorItem
import cn.com.vau.common.view.popup.bean.KlineSettingItem
import cn.com.vau.databinding.LayoutKlineSettingEditViewBinding
import cn.com.vau.util.*

/**
 *  没有数据显示的布局 封装
 */
class KlineSettingEditView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy { LayoutKlineSettingEditViewBinding.inflate(LayoutInflater.from(context), this) }

    private val titleList = mutableListOf<AppCompatTextView>()
    private val etList = mutableListOf<AppCompatEditText>()
    private val addList = mutableListOf<AppCompatImageView>()
    private val reduceList = mutableListOf<AppCompatImageView>()

    private val dataList: MutableList<KlineSettingItem> = mutableListOf()

    init {
        titleList.add(mBinding.tvTitle1)
        titleList.add(mBinding.tvTitle2)
        titleList.add(mBinding.tvTitle3)
        etList.add(mBinding.etValue1)
        etList.add(mBinding.etValue2)
        etList.add(mBinding.etValue3)
        addList.add(mBinding.ivHandCountUp1)
        addList.add(mBinding.ivHandCountUp2)
        addList.add(mBinding.ivHandCountUp3)
        reduceList.add(mBinding.ivHandCountDown1)
        reduceList.add(mBinding.ivHandCountDown2)
        reduceList.add(mBinding.ivHandCountDown3)

        etList.forEachIndexed { index, et ->
            et.doAfterTextChanged { editable ->
                dataList.getOrNull(index)?.let { klineSettingItem ->
                    etWatcher(et, klineSettingItem, editable)
                }
            }
        }
    }

    fun setKlineSettingItem(item: KlineIndicatorItem?) {
        dataList.clear()
        dataList.addAll(item?.itemList ?: mutableListOf())
        item?.itemList?.forEachIndexed { index, klineSettingItem ->
            titleList.getOrNull(index)?.text = klineSettingItem.title
            etList.getOrNull(index)?.apply {
                setText("${klineSettingItem.num}")
                setSelection(text?.length.ifNull())
            }
            addList.getOrNull(index)?.setOnClickListener {
                calculate(etList.getOrNull(index), klineSettingItem, true)
            }
            reduceList.getOrNull(index)?.setOnClickListener {
                calculate(etList.getOrNull(index), klineSettingItem, false)
            }
        }
        when (item?.itemList?.size) {
            3 -> {
                mBinding.group1.isVisible = true
                mBinding.group2.isVisible = true
                mBinding.group3.isVisible = true
                mBinding.tvNoConfig.isVisible = false
            }

            2 -> {
                mBinding.group1.isVisible = true
                mBinding.group2.isVisible = true
                mBinding.group3.isInvisible = true
                mBinding.tvNoConfig.isVisible = false
            }

            1 -> {
                mBinding.group1.isVisible = true
                mBinding.group2.isInvisible = true
                mBinding.group3.isInvisible = true
                mBinding.tvNoConfig.isVisible = false
            }

            else -> {
                mBinding.group1.isInvisible = true
                mBinding.group2.isInvisible = true
                mBinding.group3.isInvisible = true
                mBinding.tvNoConfig.isVisible = true
            }
        }
    }

    private fun etWatcher(et: AppCompatEditText?, item: KlineSettingItem, editable: Editable?) {
        if (et != null && editable != null) {
            if (editable.toString().isNotEmpty()) {
                var num = editable.toString().toIntCatching()
                if (num > item.maxNum) {
                    num = item.default
                    et.setText("$num")
                    et.setSelection(et.text?.length.ifNull())
                } else if (num < item.minNum) {
                    num = item.default
                    et.setText("$num")
                    et.setSelection(et.text?.length.ifNull())
                }
                item.num = num
            } else {
                if (item.minNum == 0) {
                    item.num = 0
                } else {
                    item.num = item.default
                    ToastUtil.showToast(context.getString(R.string.the_required_param_empty))
                }
                et.setText("${item.num}")
                et.setSelection(et.text?.length.ifNull())
            }
        }
    }

    /**
     * * from  0:child1  1:child2  2:child3
     */
    private fun calculate(et: AppCompatEditText?, item: KlineSettingItem, isAdd: Boolean) {
        val num = et?.text.toString().toIntCatching()
        val calc = if (isAdd) num + 1 else num - 1
        if (isAdd) {
            if (calc <= item.maxNum) {
                item.num = calc
                et?.setText("$calc")
                et?.setSelection(et.text?.length.ifNull())
            }
        } else {
            if (calc >= item.minNum) {
                item.num = calc
                et?.setText("$calc")
                et?.setSelection(et.text?.length.ifNull())
            }
        }
        et?.requestFocus()
    }
}
