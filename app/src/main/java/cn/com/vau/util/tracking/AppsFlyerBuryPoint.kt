package cn.com.vau.util.tracking

import cn.com.vau.common.base.rx.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.msg.AppsFlyerPointBean
import io.reactivex.disposables.Disposable

/**
 * AppsFlyer后台埋点上报工具类
 */
object AppsFlyerBuryPoint {

    private val mRxManager = RxManager()

    fun requestRequireParam() {
        HttpUtils.loadData(RetrofitHelper.getHttpService().appsFlyerStatisticEventValue(
            UserDataUtil.loginToken()
        ), object : BaseObserver<AppsFlyerPointBean>() {
            override fun onNext(t: AppsFlyerPointBean?) {
                if ("V00000" == t?.resultCode) {
                    LogEventUtil.requireParam = t.data?.Base
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }

}