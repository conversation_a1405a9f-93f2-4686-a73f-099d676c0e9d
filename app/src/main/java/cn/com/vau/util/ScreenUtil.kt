package cn.com.vau.util

import android.Manifest.permission
import android.annotation.SuppressLint
import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Point
import android.provider.Settings
import android.provider.Settings.SettingNotFoundException
import android.view.Surface
import android.view.WindowManager
import androidx.annotation.RequiresPermission
import kotlin.math.min

/**
 * <pre>
 * author: Blankj
 * blog  : http://blankj.com
 * time  : 2016/08/02
 * desc  : UtilApp about screen
</pre> *
 */
class ScreenUtil private constructor() {
    init {
        throw UnsupportedOperationException("u can't instantiate me...")
    }

    companion object {
        val screenWidth: Int
            /**
             * Return the width of screen, in pixel.
             *
             * @return the width of screen, in pixel
             */
            get() {
                val wm = UtilApp.getApp().getSystemService(Context.WINDOW_SERVICE) as? WindowManager ?: return -1
                val point = Point()
                wm.defaultDisplay.getRealSize(point)
                return point.x
            }

        val screenHeight: Int
            /**
             * Return the height of screen, in pixel.
             *
             * @return the height of screen, in pixel
             */
            get() {
                val wm = UtilApp.getApp().getSystemService(Context.WINDOW_SERVICE) as? WindowManager ?: return -1
                val point = Point()
                wm.defaultDisplay.getRealSize(point)
                return point.y
            }

        val appScreenWidth: Int
            /**
             * Return the application's width of screen, in pixel.
             *
             * @return the application's width of screen, in pixel
             */
            get() {
                val wm = UtilApp.getApp().getSystemService(Context.WINDOW_SERVICE) as? WindowManager ?: return -1
                val point = Point()
                wm.defaultDisplay.getSize(point)
                return point.x
            }

        val appScreenHeight: Int
            /**
             * Return the application's height of screen, in pixel.
             *
             * @return the application's height of screen, in pixel
             */
            get() {
                val wm = UtilApp.getApp().getSystemService(Context.WINDOW_SERVICE) as? WindowManager ?: return -1
                val point = Point()
                wm.defaultDisplay.getSize(point)
                return point.y
            }

        val screenDensity: Float
            /**
             * Return the density of screen.
             *
             * @return the density of screen
             */
            get() = Resources.getSystem().displayMetrics.density

        val screenDensityDpi: Int
            /**
             * Return the screen density expressed as dots-per-inch.
             *
             * @return the screen density expressed as dots-per-inch
             */
            get() = Resources.getSystem().displayMetrics.densityDpi

        val screenXDpi: Float
            /**
             * Return the exact physical pixels per inch of the screen in the Y dimension.
             *
             * @return the exact physical pixels per inch of the screen in the Y dimension
             */
            get() = Resources.getSystem().displayMetrics.xdpi

        val screenYDpi: Float
            /**
             * Return the exact physical pixels per inch of the screen in the Y dimension.
             *
             * @return the exact physical pixels per inch of the screen in the Y dimension
             */
            get() = Resources.getSystem().displayMetrics.ydpi

        /**
         * Set full screen.
         *
         * @param activity The activity.
         */
        fun setFullScreen(activity: Activity) {
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        }

        /**
         * Set non full screen.
         *
         * @param activity The activity.
         */
        fun setNonFullScreen(activity: Activity) {
            activity.window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        }

        /**
         * Toggle full screen.
         *
         * @param activity The activity.
         */
        fun toggleFullScreen(activity: Activity) {
            val isFullScreen = isFullScreen(activity)
            val window = activity.window
            if (isFullScreen) {
                window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            } else {
                window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            }
        }

        /**
         * Return whether screen is full.
         *
         * @param activity The activity.
         * @return `true`: yes<br></br>`false`: no
         */
        fun isFullScreen(activity: Activity): Boolean {
            val fullScreenFlag = WindowManager.LayoutParams.FLAG_FULLSCREEN
            return (activity.window.attributes.flags and fullScreenFlag) == fullScreenFlag
        }

        /**
         * Set the screen to landscape.
         *
         * @param activity The activity.
         */
        @SuppressLint("SourceLockedOrientationActivity")
        fun setLandscape(activity: Activity) {
            activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }

        /**
         * Set the screen to portrait.
         *
         * @param activity The activity.
         */
        @SuppressLint("SourceLockedOrientationActivity")
        fun setPortrait(activity: Activity) {
            activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }

        val isLandscape: Boolean
            /**
             * Return whether screen is landscape.
             *
             * @return `true`: yes<br></br>`false`: no
             */
            get() = (UtilApp.getApp().resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE)

        val isPortrait: Boolean
            /**
             * Return whether screen is portrait.
             *
             * @return `true`: yes<br></br>`false`: no
             */
            get() = (UtilApp.getApp().resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT)

        /**
         * Return the rotation of screen.
         *
         * @param activity The activity.
         * @return the rotation of screen
         */
        fun getScreenRotation(activity: Activity): Int {
            return when (activity.windowManager.defaultDisplay.rotation) {
                Surface.ROTATION_0 -> 0
                Surface.ROTATION_90 -> 90
                Surface.ROTATION_180 -> 180
                Surface.ROTATION_270 -> 270
                else -> 0
            }
        }

        val isScreenLock: Boolean
            /**
             * Return whether screen is locked.
             *
             * @return `true`: yes<br></br>`false`: no
             */
            get() {
                val km =
                    UtilApp.getApp().getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager ?: return false
                return km.inKeyguardRestrictedInputMode()
            }

        @set:RequiresPermission(permission.WRITE_SETTINGS)
        var sleepDuration: Int
            /**
             * Return the duration of sleep.
             *
             * @return the duration of sleep.
             */
            get() {
                try {
                    return Settings.System.getInt(
                        UtilApp.getApp().contentResolver,
                        Settings.System.SCREEN_OFF_TIMEOUT
                    )
                } catch (e: SettingNotFoundException) {
                    e.printStackTrace()
                    return -123
                }
            }
            /**
             * Set the duration of sleep.
             *
             * Must hold `<uses-permission android:name="android.permission.WRITE_SETTINGS" />`
             *
             * @param duration The duration.
             */
            set(duration) {
                Settings.System.putInt(
                    UtilApp.getApp().contentResolver,
                    Settings.System.SCREEN_OFF_TIMEOUT,
                    duration
                )
            }

        /**
         * 是否是宽屏
         */
        fun isScreenExpanded(context: Context): Boolean {
            val metrics = context.resources.displayMetrics
            val shortestSide = min(metrics.widthPixels, metrics.heightPixels)
            // 通常折叠屏展开时最短边 > 600dp（参考平板标准）
            return shortestSide > 600.dp2px()
        }

    }
}
