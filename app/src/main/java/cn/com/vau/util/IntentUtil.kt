package cn.com.vau.util

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.core.net.toUri
import java.io.File
import java.util.LinkedList

/**
 * 工具类：Intent 相关操作
 *
 * 提供分享文本、图片、跳转应用设置等功能的 Intent 构建方法
 *
 * <AUTHOR>
 * @blog http://blankj.com
 */
object IntentUtil {

    /**
     * 获取分享文本的 Intent
     *
     * @param content 分享内容
     * @return 分享文本的 Intent（通过 Intent.createChooser 包装）
     */
    fun getShareTextIntent(content: String?): Intent? {
        var intent = Intent(Intent.ACTION_SEND)
        intent.setType("text/plain")
        intent.putExtra(Intent.EXTRA_TEXT, content)
        intent = Intent.createChooser(intent, "")
        return getIntent(intent, true)
    }

    /**
     * 获取分享单张图片的 Intent（通过图片路径）
     *
     * @param imagePath 图片文件路径
     * @return 分享图片的 Intent
     */
    fun getShareImageIntent(imagePath: String?): Intent? {
        return getShareTextImageIntent("", imagePath)
    }

    /**
     * 获取分享单张图片的 Intent（通过 File 对象）
     *
     * @param imageFile 图片文件对象
     * @return 分享图片的 Intent
     */
    fun getShareImageIntent(imageFile: File?): Intent? {
        return getShareTextImageIntent("", imageFile)
    }

    /**
     * 获取分享单张图片的 Intent（通过 Uri 对象）
     *
     * @param imageUri 图片 Uri 对象
     * @return 分享图片的 Intent
     */
    fun getShareImageIntent(imageUri: Uri?): Intent? {
        return getShareTextImageIntent("", imageUri)
    }

    /**
     * 获取带文本和单张图片的分享 Intent（通过路径）
     *
     * @param content   分享文本内容
     * @param imagePath 图片路径
     * @return 分享文本+图片的 Intent
     */
    fun getShareTextImageIntent(content: String?, imagePath: String?): Intent? {
        return getShareTextImageIntent(content, FileUtil.getFileByPath(imagePath.ifNull()))
    }

    /**
     * 获取带文本和单张图片的分享 Intent（通过 File 对象）
     *
     * @param content   分享文本内容
     * @param imageFile 图片文件对象
     * @return 分享文本+图片的 Intent
     */
    fun getShareTextImageIntent(content: String?, imageFile: File?): Intent? {
        return getShareTextImageIntent(content, UriUtil.file2Uri(imageFile))
    }

    /**
     * 获取带文本和单张图片的分享 Intent（通过 Uri 对象）
     *
     * @param content  分享文本内容
     * @param imageUri 图片 Uri 对象
     * @return 分享文本+图片的 Intent
     */
    fun getShareTextImageIntent(content: String?, imageUri: Uri?): Intent? {
        var intent = Intent(Intent.ACTION_SEND)
        intent.putExtra(Intent.EXTRA_TEXT, content)
        intent.putExtra(Intent.EXTRA_STREAM, imageUri)
        intent.setType("image/*")
        intent = Intent.createChooser(intent, "")
        return getIntent(intent, true)
    }

    /**
     * 获取分享多张图片的 Intent（通过路径列表）
     *
     * @param imagePaths 图片路径列表
     * @return 分享多图的 Intent
     */
    fun getShareImageIntent(imagePaths: LinkedList<String?>?): Intent? {
        return getShareTextImageIntent("", imagePaths)
    }

    /**
     * 获取分享多张图片的 Intent（通过 File 列表）
     *
     * @param images 图片文件列表
     * @return 分享多图的 Intent
     */
    fun getShareImageIntent(images: MutableList<File?>?): Intent? {
        return getShareTextImageIntent("", images)
    }

    /**
     * 获取分享多张图片的 Intent（通过 Uri 列表）
     *
     * @param uris 图片 Uri 列表
     * @return 分享多图的 Intent
     */
    fun getShareImageIntent(uris: ArrayList<Uri?>?): Intent? {
        return getShareTextImageIntent("", uris)
    }

    /**
     * 获取带文本和多图的分享 Intent（通过路径列表）
     *
     * @param content    分享文本内容
     * @param imagePaths 图片路径列表
     * @return 分享多图的 Intent
     */
    fun getShareTextImageIntent(
        content: String?,
        imagePaths: LinkedList<String?>?
    ): Intent? {
        val files: MutableList<File?> = ArrayList<File?>()
        if (imagePaths != null) {
            for (imagePath in imagePaths) {
                val file: File? = FileUtil.getFileByPath(imagePath.ifNull())
                if (file != null) {
                    files.add(file)
                }
            }
        }
        return getShareTextImageIntent(content, files)
    }

    /**
     * 获取带文本和多图的分享 Intent（通过 File 列表）
     *
     * @param content 分享文本内容
     * @param images  图片文件列表
     * @return 分享多图的 Intent
     */
    fun getShareTextImageIntent(content: String?, images: MutableList<File?>?): Intent? {
        val uris = ArrayList<Uri?>()
        if (images != null) {
            for (image in images) {
                val uri: Uri? = UriUtil.file2Uri(image)
                if (uri != null) {
                    uris.add(uri)
                }
            }
        }
        return getShareTextImageIntent(content, uris)
    }

    /**
     * 获取带文本和多图的分享 Intent（通过 Uri 列表）
     *
     * @param content 分享文本内容
     * @param uris    图片 Uri 列表
     * @return 分享多图的 Intent
     */
    fun getShareTextImageIntent(content: String?, uris: ArrayList<Uri?>?): Intent? {
        var intent = Intent(Intent.ACTION_SEND_MULTIPLE)
        intent.putExtra(Intent.EXTRA_TEXT, content)
        intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris)
        intent.setType("image/*")
        intent = Intent.createChooser(intent, "")
        return getIntent(intent, true)
    }

    /**
     * 根据是否为新任务设置 Intent 标志
     *
     * @param intent      目标 Intent
     * @param isNewTask   是否以新任务启动
     * @return 配置后的 Intent
     */
    private fun getIntent(intent: Intent, isNewTask: Boolean): Intent {
        return if (isNewTask)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        else
            intent
    }

    /**
     * 跳转到当前应用的详情设置页面
     */
    fun launchAppDetailsSettings() {
        val intent = getLaunchAppDetailsSettingsIntent(UtilApp.getApp().packageName, true)
        if (!isIntentAvailable(intent)) return
        UtilApp.getApp().startActivity(intent)
    }

    /**
     * 获取跳转应用详情设置的 Intent
     *
     * @param pkgName    应用包名
     * @param isNewTask  是否以新任务启动
     * @return 应用详情设置 Intent
     */
    fun getLaunchAppDetailsSettingsIntent(pkgName: String, isNewTask: Boolean): Intent {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.data = ("package:$pkgName").toUri()
        return getIntent(intent, isNewTask)
    }

    /**
     * 检查 Intent 是否可用（是否存在可响应的 Activity）
     *
     * @param intent 检查的 Intent
     * @return true 表示可用
     */
    fun isIntentAvailable(intent: Intent): Boolean {
        return UtilApp.getApp()
            .packageManager
            .queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY).isNotEmpty()
    }
}
