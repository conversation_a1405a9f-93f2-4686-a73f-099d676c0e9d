package cn.com.vau.util.widget.webview.offline.matcher

import cn.com.vau.util.widget.webview.offline.info.BisInfo

interface IMatcher {
    fun matchHashUrl(bisInfo: BisInfo?, url: String?): String? {
        return url
    }

    fun matchHistoryUrl(bisInfo: BisInfo?, url: String?): String? {
        return url
    }

    fun matchHistoryResource(bisInfo: BisInfo?, url: String?): String? {
        return url
    }

    fun matchHashResource(bisInfo: BisInfo?, url: String?): String? {
        return url
    }

    /**
     * 判断URL是否匹配和 BisInfo匹配
     */
    fun isMatch(bisInfo: BisInfo?,url: String?):Boolean{
        return false
    }
}
