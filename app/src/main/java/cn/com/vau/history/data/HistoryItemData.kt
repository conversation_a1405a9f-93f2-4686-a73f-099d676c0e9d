package cn.com.vau.history.data

import android.text.SpannedString
import androidx.annotation.Keep

/**
 * Create data：2025/2/25 16:31
 * @author: Brin
 * Describe:
 */
@Keep
data class HistoryItemData(
    val login: String = "",
    val order: String = "",
    val cmd: Int = 0,                   // 方向 0: buy , 1: sell
    val symbol: String = "",
    val volume: String = "",
    val openTimeStr: String = "",
    val openTime: String = "",
    val openPrice: String = "",
    val closeTimeStr: String = "",
    val closeTime: String = "",
    val closePrice: String = "",
    val commission: String = "",        //手续费
    val swap: String = "",              //隔夜费
    val profit: String = "",            //交易盈亏（不包含手续费和隔夜费）
    val totalProfit: String = "",       //总收益 （盈亏+佣金+税金+库存费） 净盈亏
    val totalVolume: String = "",       //仓位总数量
    val digits: String = "",
    val entry: String = "",
    val positionId: String = "",
    val partClose: Boolean = false,        //true : 部分平仓 ，false : 全部平仓
    val recentlyCloseOrderDeal: String = "" //最近一次平仓时间
)

@Keep
data class HistoryItemUiData(
    val orderType: String = "",
    val orderTypeBackGroundColorInt: Int = 0,//买卖类型: B\S
    val symbol: String = "",
    val orderId: String = "",
    val orderStatus: SpannedString = SpannedString(""),
    val closingPnlTitle: String = "",
    val closingPnl: SpannedString = SpannedString(""),
    val netPnlTitle: String = "",
    val netPnl: SpannedString = SpannedString(""),
    val closedTotalVolumeTitle: String = "",
    val closedTotalVolume: String = "",
    val entryPriceTitle: String = "",
    val entryPrice: String = "",
    val chargesSwapTitle: String = "",
    val chargesSwap: String = "",
    val avgClosePriceTitle: String = "",
    val avgClosePrice: String = "",
    val openedTimeTitle: String = "",
    val openedTime: String = "",
    val closedTimeTitle: String = "",
    val closedTime: String = "",

    val openTimeLong: String = "",
    val closeTimeLong: String = "",
    val cmd: String = "",
    val openPrice: String = "",
    val recentlyCloseOrder: String = "",
)