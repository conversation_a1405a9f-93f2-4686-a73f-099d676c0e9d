package cn.com.vau.history

import cn.com.vau.history.ui.TimeModeType
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * Create data：2025/2/27 11:15
 * @author: Brin
 * Describe:
 *  需求地址：
 *  https://pj4w2l1pwuq.sg.larksuite.com/wiki/FlEAwSv02iJhWJk3BZBlm5oTg0g
 *  https://pj4w2l1pwuq.sg.larksuite.com/sheets/Nym5smA5GhN6dJt2wP7lCbACgub?sheet=OXS7zZ
 *
 */
object HistoryTrack {
    fun trackHistoryEntryIcon() {
        SensorsDataUtil.track(ORDERPAGE_HISTORYICON_CLICK)
    }

    /**
     * Position History / Funding History
     */
    fun trackHistoryTabClick(tabName: String) {
        SensorsDataUtil.track(HISTORYRECORDPAGE_PAGETAB_CLICK, JSONObject().apply { put("tab_name", tabName) })
    }


    fun trackPositionHistoryTimeFilter(@TimeModeType timeType: String) {
        SensorsDataUtil.track(POSITIONHISTORYPAGE_TIME_FILTER, JSONObject().apply { put("time_range", timeType) })
    }

    fun trackFundingHistoryTimeFilter(@TimeModeType timeType: String) {
        SensorsDataUtil.track(FUNDINGHISTORYPAGE_TIME_FILTER, JSONObject().apply { put("time_range", timeType) })
    }

    fun trackPositionHistorySymbolClick() {
        SensorsDataUtil.track(POSITIONHISTORYPAGE_SYMBOL_CLICK)
    }

    /**
     * All / Deposit / Withdraw / Credit Issuance / Credit Deduction
     */
    fun trackFundingHistoryTypeClick(fundType: String) {
        SensorsDataUtil.track(FUNDINGHISTORYPAGE_TYPE_CLICK, JSONObject().apply { put("fund_type", fundType) })
    }


    //<editor-fold desc="历史记录的埋点 Keys 值">
    // 订单页历史纪录icon点击 -> 用户点击历史纪录icon时触发
    const val ORDERPAGE_HISTORYICON_CLICK = "OrderPage_HistoryIcon_Click"

    // 历史纪录页页面标签点击 -> 用户点击历史纪录页页面标签时触发，并需记录是点击哪一个Tab
    const val HISTORYRECORDPAGE_PAGETAB_CLICK = "HistoryRecordPage_PageTab_Click"

    // 持仓历史页时间筛选 -> 用户使用持仓历史页时间筛选点击Confirm时触发，并需记录时间范围，若为自定义则回传Customized
    const val POSITIONHISTORYPAGE_TIME_FILTER = "PositionHistoryPage_Time_Filter"

    // 持仓历史页Symbol点击 -> 用户点击持仓历史页Symbol后有点击任一商品时触发
    const val POSITIONHISTORYPAGE_SYMBOL_CLICK = "PositionHistoryPage_Symbol_Click"

    // 资金历史页时间筛选 -> 用户使用资金历史页时间筛选点击Confirm时触发，并需记录时间范围，若为自定义则回传Customized
    const val FUNDINGHISTORYPAGE_TIME_FILTER = "FundingHistoryPage_Time_Filter"

    // 资金历史页Type点击 -> 用户点击资金历史页Type后有点击任一类型时触发
    const val FUNDINGHISTORYPAGE_TYPE_CLICK = "FundingHistoryPage_Type_Click"
    //</editor-fold>


}


