package cn.com.vau.profile.activity.addOrForgotSecurityPWD

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean

/**
 * Created by zhy on 2018/12/3.
 */
class AddOrForgotSecurityPWDModel : AddOrForgotSecurityPWDContract.Model {

    override fun getBindingTelSMSApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>) {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getTelSMSApi(map), baseObserver)
    }

}
