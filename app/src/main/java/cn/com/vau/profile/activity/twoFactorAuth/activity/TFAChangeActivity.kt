package cn.com.vau.profile.activity.twoFactorAuth.activity

import android.content.*
import android.content.res.ColorStateList
import android.os.Bundle
import androidx.activity.*
import androidx.core.view.*
import androidx.lifecycle.*
import androidx.navigation.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivityTfaChangeBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.*
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.login.activity.VerifySmsCodeActivity.VerifySmsCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAViewModel
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collectLatest
import org.greenrobot.eventbus.EventBus

/**
 * @description: 2fa修改页面 的提示页面
 * @author: GG
 * @createDate: 2024 11月 29 15:19
 * @updateUser:
 * @updateDate: 2024 11月 29 15:19
 */
class TFAChangeActivity : BaseMvvmActivity<ActivityTfaChangeBinding, TFAViewModel>() {

    private val navigator: NavController by lazy { findNavController(R.id.changeNav) }
    private val color_c1e1e1e_cebffffff: ColorStateList by lazy { ColorStateList.valueOf(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff)) }
    private val color_c731e1e1e_c61ffffff: ColorStateList by lazy { ColorStateList.valueOf(AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)) }

    private val img2faPhoneOtpSelect by lazy { AttrResourceUtil.getDrawable(this, R.attr.img2faPhoneOtpSelect) }
    private val img2faPhoneOtpUnselect by lazy { AttrResourceUtil.getDrawable(this, R.attr.img2faPhoneOtpUnselect) }
    private val img2faEmailOtpSelect by lazy { AttrResourceUtil.getDrawable(this, R.attr.img2faEmailOtpSelect) }
    private val img2faEmailOtpUnselect by lazy { AttrResourceUtil.getDrawable(this, R.attr.img2faEmailOtpUnselect) }
    private val img2faUnselect by lazy { AttrResourceUtil.getDrawable(this, R.attr.img2faUnselect) }
    private val img2faSelect by lazy { AttrResourceUtil.getDrawable(this, R.attr.img2faSelect) }

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        mViewModel.pageType = TFAViewModel.TYPE_CHANGE
        mViewModel.tCode = intent.getStringExtra(KEY_TCODE).ifNull()
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        mBinding.mHeaderBar.setStartBackIconClickListener {
            back()
        }
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 如果是kyc的验证页面跳转过来的，则直接跳转到验证页面
        if (mViewModel.signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_KYC_OTP) {
            if (mViewModel.signUpRequestBean?.verifySmsCodeType == VerifySmsCodeType.CHANGE_TOTP ||
                mViewModel.signUpRequestBean?.verifyEmailCodeType == VerifyEmailCodeType.CHANGE_TOTP
            ) {
                navigator.navigate(R.id.action_global_change_verify)
            }
        }
    }

    override fun initListener() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                back()
            }
        })
    }

    /**
     * 根据当前页面设置不同的返回页面
     */
    private fun back() = MainScope().launch {
        noRepeat {
            when (mViewModel.currentPageLiveData.value) {
                TFAViewModel.PAGE_LINK, TFAViewModel.PAGE_RESULT -> {
                    ActivityManagerUtil.getInstance().finishActivity(TFAChangePromptActivity::class.java)
                    ActivityManagerUtil.getInstance().finishActivity(TFAResetActivity::class.java)
                    ActivityManagerUtil.getInstance().finishActivity(TFASettingActivity::class.java)
                    ActivityManagerUtil.getInstance().finishAllSameActivity(TFABindActivity::class.java)
                    ActivityManagerUtil.getInstance().finishAllSameActivity(TFAChangeActivity::class.java)
                    ActivityManagerUtil.getInstance().finishAllSameActivity(TFAVerifyActivity::class.java)
                    ActivityManagerUtil.getInstance().finishAllSameActivity(VerifySmsCodeActivity::class.java)
                    ActivityManagerUtil.getInstance().finishAllSameActivity(VerifyEmailCodeActivity::class.java)
                    EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
                }

                TFAViewModel.PAGE_OPT -> {
                    navigator.navigate(R.id.action_global_change_link)
                }

                TFAViewModel.PAGE_VERIFY -> {
                    if (mViewModel.signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_KYC_OTP) {
                        finish()
                    } else {
                        navigator.navigate(R.id.action_global_otp)
                    }
                }
            }
        }
    }

    override fun initData() {
        super.initData()
        if (mViewModel.signUpRequestBean?.fromPage != FromPageType.FROM_PAGE_KYC_OTP) {
            mViewModel.getUserAccountDataApi()
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.currentPageLiveData.observe(this) {
            mBinding.groupTop.isGone = it == TFAViewModel.PAGE_LINK || it == TFAViewModel.PAGE_RESULT
            mBinding.mHeaderBar.setEndIconVisible(it == TFAViewModel.PAGE_VERIFY || it == TFAViewModel.PAGE_OPT)
            when (it) {
                TFAViewModel.PAGE_LINK -> {
                    mBinding.mHeaderBar.setTitleText(getString(R.string.change_new_authentication))
                }

                TFAViewModel.PAGE_OPT -> {
                    mBinding.mHeaderBar.setTitleText(getString(R.string.change_new_authentication))
                    setTopState(true)
                }

                TFAViewModel.PAGE_VERIFY -> {
                    mBinding.mHeaderBar.setTitleText(getString(R.string.change_new_authentication))
                    setTopState(false)
                }

                TFAViewModel.PAGE_RESULT -> {
                    mBinding.mHeaderBar.isVisible = true
                    if (mViewModel.tfaChangeResult?.isSuccess() == true) {
                        mBinding.mHeaderBar.setTitleText(getString(R.string.two_factor_authentication))
                    } else {
                        mBinding.mHeaderBar.setTitleText(getString(R.string.enable_authenticator))
                    }
                }
            }
            if (SpManager.isV1V2()) { // 黄金开户的功能隐藏上面的布局
                mBinding.groupTop.isVisible = false
            }
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    TFAViewModel.PAGE_LINK.toString() -> {
                        if (mViewModel.verificationType == 1) {
                            mBinding.tvOtp.text = getString(R.string.email_otp)
                        }
                        navigator.navigate(R.id.action_global_otp)
                    }

                    TFAViewModel.PAGE_OPT.toString() -> {
                        navigator.navigate(R.id.action_global_change_verify)
                    }

                    TFAViewModel.PAGE_VERIFY.toString() -> {
                        navigator.navigate(R.id.action_global_change_result)
                    }
                }
            }
        }
        // 发送验证码时触发滑块验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha(it)
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
                VerifyEmailCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
            } else {
                VerifySmsCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
            }
        }
        // 获取安全中心配置信息成功
        mViewModel.getAuthConfigSuccessLiveData.observe(this) {
            val validateType = it?.second?.validateType
            sendCodeViewModel.signUpRequestBean?.validateType = validateType
            if (it.first == "modify-auth-2fa") { // 修改2fa
                when (validateType) {
                    0 -> { // 手机号和邮箱均未验证：验证TOTP
                        navigator.navigate(R.id.action_global_change_verify)
                    }

                    2 -> { // 手机号已验证：先验证手机OTP，然后跳转到输入原密码和两次新密码页面
                        sendCodeViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.CHANGE_TOTP
                        sendPhoneCode("18")
                    }

                    else -> { // 等于1或者其他，邮箱已验证：先验证邮箱OTP，然后跳转到输入原密码和两次新密码页面
                        sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.CHANGE_TOTP
                        sendEmailCode("18")
                    }
                }
            }
        }
    }

    /**
     * 设置顶部状态
     */
    private fun setTopState(isFristTab: Boolean) {
        if (isFristTab) {
            mBinding.ivOtp.setImageResource(getOtpSelectIcon())
            mBinding.tvOtp.setTextColor(color_c1e1e1e_cebffffff)
            mBinding.iv2FA.setImageResource(img2faUnselect)
            mBinding.tv2FA.setTextColor(color_c731e1e1e_c61ffffff)
        } else {
            mBinding.ivOtp.setImageResource(getOtpUnselectIcon())
            mBinding.tvOtp.setTextColor(color_c731e1e1e_c61ffffff)
            mBinding.iv2FA.setImageResource(img2faSelect)
            mBinding.tv2FA.setTextColor(color_c1e1e1e_cebffffff)
        }
    }

    private fun getOtpSelectIcon(): Int {
        return if (mViewModel.verificationType == 1) {
            img2faEmailOtpSelect
        } else {
            img2faPhoneOtpSelect
        }
    }

    private fun getOtpUnselectIcon(): Int {
        return if (mViewModel.verificationType == 1) {
            img2faEmailOtpUnselect
        } else {
            img2faPhoneOtpUnselect
        }
    }

    /**
     * 发送验证码时触发滑块验证
     */
    private fun showCaptcha(type: String) {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) { // 邮箱
                sendCodeViewModel.sendEmailCodeApi(type, it)
            } else {// 手机号
                sendCodeViewModel.sendPhoneCodeApi(type, it)
            }
        }
        mCaptcha?.validate()
    }

    /**
     * 发送手机验证码
     */
    private fun sendPhoneCode(type: String) {
        val second = mViewModel.getAuthConfigSuccessLiveData.value?.second
        sendCodeViewModel.signUpRequestBean?.countryCode = second?.phoneCountryCode
        sendCodeViewModel.signUpRequestBean?.countryNum = second?.phoneCode
        sendCodeViewModel.signUpRequestBean?.mobile = second?.phone
        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
        sendCodeViewModel.sendPhoneCodeApi(type)
    }

    /**
     * 发送邮箱验证码
     */
    private fun sendEmailCode(type: String) {
        val second = mViewModel.getAuthConfigSuccessLiveData.value?.second
        sendCodeViewModel.signUpRequestBean?.email = second?.email
        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
        sendCodeViewModel.sendEmailCodeApi(type)
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        mCaptcha?.destroy()
    }

    companion object {

        private const val KEY_TCODE = "tCode"

        fun open(context: Context, tCode: String?, signUpRequestBean: SignUpRequestBean? = null) {
            context.startActivity(Intent(context, TFAChangeActivity::class.java).apply {
                putExtra(KEY_TCODE, tCode)
                putExtra("signUpRequestBean", signUpRequestBean)
            })
        }

    }
}