package cn.com.vau.profile.activity.kycLink

import android.content.*
import android.os.Bundle
import android.view.MotionEvent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.databinding.ActivityKycBindPhoneBinding
import cn.com.vau.page.common.selectArea.SelectAreaCodeActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.VerifySmsCodeActivity
import cn.com.vau.page.login.activity.VerifySmsCodeActivity.VerifySmsCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.profile.viewmodel.BindPhoneKycViewModel
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha

/**
 * author：lvy
 * date：2025/03/22
 * desc：kyc-绑定手机号
 */
class BindPhoneKycActivity : BaseMvvmActivity<ActivityKycBindPhoneBinding, BindPhoneKycViewModel>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        mViewModel.signUpRequestBean = intent?.getParcelableExtra("signUpRequestBean")
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        // 清空输入框
        mBinding.mobileView.clearText()
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        // 按钮默认不可点击
        mBinding.tvNext.isEnabled = false
        mBinding.llWhatsApp.isEnabled = false
    }

    override fun initData() {
        // 初始化国家区号
        mViewModel.initAreaCode()
    }

    override fun createObserver() {
        // 国家区号设置成功
        mViewModel.areaCodeLiveData.observe(this) {
            mBinding.mobileView.setAreaCodeText(it)
        }
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            mViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.BIND_PHONE_KYC
            VerifySmsCodeActivity.open(this, mViewModel.signUpRequestBean)
        }
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 选择国家区号
        mBinding.mobileView.areaCodeClickListener {
            selectAreaCodeLauncher.launch(Intent().apply {
                setClass(this@BindPhoneKycActivity, SelectAreaCodeActivity::class.java)
                putExtras(bundleOf().apply {
                    putString("selectAreaCode", mViewModel.signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum))
                })
            })
        }
        // 手机号输入框
        mBinding.mobileView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
            sendCode()
        }
        // whatsApp
        mBinding.llWhatsApp.clickNoRepeat {
            mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.WHATSAPP
            sendCode()
        }
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        val isNext = mBinding.mobileView.getInputText().isNotBlank()
        mBinding.tvNext.isEnabled = isNext
        if (isNext) {
            mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
            mBinding.llWhatsApp.isEnabled = true
        } else {
            mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
            mBinding.llWhatsApp.isEnabled = false
        }
    }

    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendCode(it)
        }
        mCaptcha?.validate()
    }

    /**
     * 发送验证码
     */
    private fun sendCode(validate: String? = null) {
        // kyc绑定手机号 type=26
        mViewModel.signUpRequestBean?.mobile = mBinding.mobileView.getInputText()
        sendCodeViewModel.sendPhoneCodeApi(type = "26", validate = validate)
    }

    private val selectAreaCodeLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Constants.SELECT_AREA) {
            val areaData = it?.data?.getParcelableExtra<SelectCountryNumberObjDetail>(Constants.SELECT_AREA_CODE)
            areaData?.let {
                mViewModel.setAreaCodeData(it)
            }
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        mCaptcha?.destroy()
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, BindPhoneKycActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }
}