package cn.com.vau.profile.activity.kycAuth

import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.transition.Fade
import androidx.transition.TransitionManager
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.PrimaryDependOnLevel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import org.json.JSONObject

/**
 * Filename: KycStandardAdapter
 * Author: GG
 * Date: 2025/3/17
 * Description:
 */
class KycStandardAdapter : BaseQuickAdapter<PrimaryDependOnLevel, BaseViewHolder>(R.layout.item_recycler_kyc_auth_standard) {

    var callBack: ((Int?, Int?) -> Unit)? = null

    override fun convert(holder: BaseViewHolder, item: PrimaryDependOnLevel) {
        holder.run {
            val root = getViewOrNull<ConstraintLayout>(R.id.root)
            val tvTitle = getViewOrNull<AppCompatTextView>(R.id.tvTitle)
            val tvRequirementsDesc = getViewOrNull<AppCompatTextView>(R.id.tvRequirementsDesc)
            val tvVerifyState = getViewOrNull<AppCompatTextView>(R.id.tvVerifyState)
            val tvState = getViewOrNull<AppCompatTextView>(R.id.tvState)
            val tvDesc = getViewOrNull<AppCompatTextView>(R.id.tvDesc)
            val ivIcon = getViewOrNull<AppCompatImageView>(R.id.ivIcon)
            val ivArrowDown = getViewOrNull<AppCompatImageView>(R.id.ivArrowDown)
            val karView = getViewOrNull<KycAuthRequirementsView>(R.id.karView)
            val kalView = getViewOrNull<KycAuthLimitView>(R.id.kalView)
            val kafView = getViewOrNull<KycAuthFeaturesView>(R.id.kafView)
            val group = getViewOrNull<Group>(R.id.group)

            when (item.level) {
                LEVEL_1 -> updateLv1(tvTitle, tvRequirementsDesc)
                LEVEL_2 -> updateLv2(tvTitle, tvRequirementsDesc)
                LEVEL_3 -> updateLv3(tvTitle, tvRequirementsDesc)
                LEVEL_BANK -> updateBank(tvTitle, group, ivArrowDown, karView)
                LEVEL_FACE -> updateFace(tvTitle, group, ivArrowDown, karView)
            }
            val iconRes = getIcon(item.level)
            if (iconRes != null) {
                ivIcon?.isVisible = true
                ivIcon?.setImageResource(iconRes)
                karView?.isVisible = false
            } else {
                ivIcon?.isVisible = false
            }

            tvVerifyState?.clickNoRepeat {
                callBack?.invoke(item.level, item.showLevelStatus)
            }

            updateVerifyStateView(tvState, tvVerifyState, item.showLevelStatus, item.isVerify)
            ivArrowDown?.clickNoRepeat {
                setViewHideOrShow(root, group, kafView, ivArrowDown)
                if (group?.isVisible == true) {
                    sensorsTrack(item.level)
                }
            }
            tvDesc?.text = item.i18nLevelDesc
            kafView?.setLiveTradingAccountState(item.functionTradingCodeList)
            kafView?.setCryptoWalletAccountState(item.functionWalletCodeList)
            kalView?.setNumberTitle(" (${getPeriodStr(item.levelFunctionLimit?.getOrNull(0)?.period.ifNull())})")
            item.levelFunctionLimit?.forEach {
                when (it?.limitType) {
                    1 -> {
                        if (it.limitAmount == null) {
                            kalView?.setDepositNumber(context.getString(R.string.unlimited))
                        } else {
                            kalView?.setDepositNumber("$${it.limitAmount.parseScientificNotation()}")
                        }
//                        kalView?.setDepositNumberDesc(context.getString(R.string.fiat_only))
                    }

                    0 -> {
                        if (it.limitAmount == null && it.cryptoAmount == null && it.fiatAmount == null) {
                            kalView?.setWithdrawalNumber(context.getString(R.string.unlimited))
//                            kalView?.setWithdrawalNumberDesc("${context.getString(R.string.fiat)} + ${context.getString(R.string.crypto)}")
                            return
                        }

                        kalView?.setWithdrawalNumber("$${it.limitAmount.parseScientificNotation()}")
//                        if (it.fiatAmount?.mathCompTo(it.cryptoAmount) == 0 && it.fiatAmount.mathCompTo(it.limitAmount) == 0) {
//                            kalView?.setWithdrawalNumberDesc("${context.getString(R.string.fiat)} + ${context.getString(R.string.crypto)}")
//                        } else {
//                            kalView?.setWithdrawalNumberDesc("$${it.fiatAmount.parseScientificNotation()} ${context.getString(R.string.fiat)} + $${it.cryptoAmount.parseScientificNotation()} ${context.getString(R.string.crypto)}")
//                        }
                    }
                }
            }
        }
    }

    private fun getPeriodStr(string: String) = when (string.lowercase()) {
        "total" -> {
            context.getString(R.string.total)
        }

        "day" -> {
            context.getString(R.string.daily_other)
        }

        else -> {
            ""
        }
    }

    private fun updateLv1(tvTitle: AppCompatTextView?, tvRequirementsDesc: AppCompatTextView?) {
        tvTitle?.text = context.getString(R.string.personal_details_verification)
        tvRequirementsDesc?.text = buildString {
            append(context.getString(R.string.verified_email_address))
            append("、")
            append(context.getString(R.string.verified_phone_number))
            append("、")
            append(context.getString(R.string.personal_information))
        }
    }

    private fun updateLv2(tvTitle: AppCompatTextView?, tvRequirementsDesc: AppCompatTextView?) {
        tvTitle?.text = context.getString(R.string.identity_verification)
        tvRequirementsDesc?.text = context.getString(R.string.proof_of_identify_document)
    }

    private fun updateLv3(tvTitle: AppCompatTextView?, tvRequirementsDesc: AppCompatTextView?) {
        tvTitle?.text = context.getString(R.string.residency_address_verification)
        tvRequirementsDesc?.text =
            buildSpannedString {
                color(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)) { append(context.getString(R.string.proof_of_address_document)) }
                append(" ")
                color(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff)) { append(context.getString(R.string.dated_within_last_6_months)) }
            }
    }

    private fun updateBank(tvTitle: AppCompatTextView?, group: Group?, ivArrowDown: AppCompatImageView?, karView: KycAuthRequirementsView?) {
        tvTitle?.text = context.getString(R.string.bank_transfer_verification)
        group?.isVisible = false
        ivArrowDown?.isVisible = false
        karView?.setRequirements(
            buildSpannedString {
                color(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)) { append(context.getString(R.string.financial_and_trading_proof_address_document)) }
                append(" ")
                color(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff)) { append(context.getString(R.string.dated_within_last_3_months)) }
            }
        )
    }

    private fun updateFace(tvTitle: AppCompatTextView?, group: Group?, ivArrowDown: AppCompatImageView?, karView: KycAuthRequirementsView?) {
        tvTitle?.text = context.getString(R.string.face_verification)
        group?.isVisible = false
        ivArrowDown?.isVisible = false
        karView?.setRequirements(context.getString(R.string.face_verification))
    }

    private fun updateVerifyStateView(statusView: AppCompatTextView?, verifyView: AppCompatTextView?, showLevelStatus: Int?, isVerify: Boolean?) {
        when (showLevelStatus) {
            STATUS_AUTHENTICATED -> {
                statusView?.text = context.getString(R.string.verified)
                statusView?.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                statusView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_ce5f7f3_c283b3d_r100)
                val drawable = ContextCompat.getDrawable(context, R.drawable.bitmap_img_source_tick11x8_c00c79c)
                drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
                statusView?.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
                verifyView?.isInvisible = true
            }

            STATUS_AUDITING -> {
                statusView?.text = context.getString(R.string.under_review)
                statusView?.setTextColor(ContextCompat.getColor(context, R.color.ce35728))
                statusView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_cf9ebe6_c3b2f2f_r100)
                statusView?.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                verifyView?.isInvisible = isVerify == false
                verifyView?.text = context.getString(R.string.verify)
                verifyView?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
                verifyView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
            }

            STATUS_REJECTED -> {
                statusView?.text = context.getString(R.string.rejected)
                statusView?.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                statusView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_cfae9e8_c3c2d32_r100)
                statusView?.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                verifyView?.isInvisible = isVerify == false
                verifyView?.text = context.getString(R.string.resubmit)
                verifyView?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                verifyView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            }

            STATUS_UNSUBMITTED -> {
                statusView?.text = context.getString(R.string.not_submitted)
                statusView?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
                statusView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_ce8e8e8_c414348_r100)
                statusView?.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                verifyView?.isInvisible = isVerify == false
                verifyView?.text = context.getString(R.string.verify)
                verifyView?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                verifyView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            }

            STATUS_PENDING -> {
                statusView?.text = context.getString(R.string.pending)
                statusView?.setTextColor(ContextCompat.getColor(context, R.color.c007fff))
                statusView?.background = ContextCompat.getDrawable(context, AttrResourceUtil.getDrawable(context, R.attr.color_ce0f0ff_c0c2c4d))
                statusView?.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                verifyView?.isInvisible = isVerify == false
                verifyView?.text = context.getString(R.string.resubmit)
                verifyView?.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                verifyView?.background = ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            }
        }
    }

    private fun getIcon(level: Int?) = when (level) {
        LEVEL_1 -> AttrResourceUtil.getDrawable(context, R.attr.imgVerifyLv1)
        LEVEL_2 -> AttrResourceUtil.getDrawable(context, R.attr.imgVerifyLv2)
        LEVEL_3 -> AttrResourceUtil.getDrawable(context, R.attr.imgVerifyLv3)
        else -> null
    }

    /**
     * 设置View隐藏或显示
     */
    private fun setViewHideOrShow(root: ConstraintLayout?, group: Group?, kafView: KycAuthFeaturesView?, ivArrowDown: AppCompatImageView?) {
        root?.let {
            TransitionManager.beginDelayedTransition(it, Fade().apply {
                mode = Fade.IN // 或 Fade.OUT
            })
        }
        group?.isVisible = !group.isVisible.ifNull()
        kafView?.isVisible = group?.isVisible == true
        ivArrowDown?.rotation = if (group?.isVisible == true) 0f else 180f
    }

    /**
     * 神策自定义埋点(v3700)
     */
    private fun sensorsTrack(level: Int?) {
        val properties = JSONObject()
        properties.put(
            "detail_level", when (level) {
                LEVEL_3 -> "LV3"
                LEVEL_2 -> "LV2"
                else -> "LV1"
            }
        ) // 按钮名称
        properties.put(
            SensorsConstant.Key.ACCOUNT_LEVEL, when (UserDataUtil.kycLevel().toIntCatching(-1)) {
                LEVEL_3 -> "LV3"
                LEVEL_2 -> "LV2"
                0, -1 -> "LV0"
                else -> "LV1"
            }
        )
        SensorsDataUtil.track(SensorsConstant.V3700.AUTHENTICATIONCENTER_DETAILCLICK, properties)
    }

    /**
     * 设置验证按钮的点击回调
     * @param callback 点击回调
     */
    fun setVerifyButtonCallback(callback: (Int?, Int?) -> Unit) {
        this.callBack = callback
    }

    companion object {

        // region 不同的level 代表不同的验证
        const val LEVEL_1 = 1
        const val LEVEL_2 = 2
        const val LEVEL_3 = 3
        const val LEVEL_BANK = 11
        const val LEVEL_FACE = 12
        // endregion

        // region 验证的状态
        /**
         * 已认证
         */
        const val STATUS_AUTHENTICATED = 0

        /**
         * 审核中
         */
        const val STATUS_AUDITING = 1

        /**
         * 被驳回
         */
        const val STATUS_REJECTED = 2

        /**
         * 未提交
         */
        const val STATUS_UNSUBMITTED = 3

        /**
         * pending
         */
        const val STATUS_PENDING = 4
        // endregion

        // region 验证后的功能
        /**
         * 入金
         */
        const val FEATURES_DEPOSIT = "1"

        /**
         * 出金
         */
        const val FEATURES_WITHDRAWAL = "2"

        /**
         * 交易
         */
        const val FEATURES_LIVE_TRADING = "3"

        /**
         * 开通主交易账号
         */
        const val FEATURES_OPEN_LIVE_TRADING_ACCOUNT = "4"

        /**
         * 开通同名交易账号
         */
        const val FEATURES_OPEN_ADDITIONAL_ACCOUNT = "5"

        /**
         * 钱包闪兑
         */
        const val FEATURES_WALLET_CONVERT = "6"

        /**
         * 钱包入金
         */
        const val FEATURES_WALLET_DEPOSIT = "7"

        /**
         * 钱包出金
         */
        const val FEATURES_WALLET_WITHDRAWAL = "8"

        // endregion

    }
}