package cn.com.vau.profile.activity.updateMobileNumber

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean

/**
 * Created by zhy on 2018/11/29.
 */
interface UpdateMobileNumberContract {
    interface Model : BaseModel {
        fun getBindingTelSMSApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>)
        fun getWithdrawRestrictionMsgApi(
            map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>
        ) //获取出金限制横幅文案
    }

    interface View : BaseView {
        fun refreshSMSCode()
        fun showCaptcha()
        fun showWithdrawRestrictionMsg(msg: String?) //出金限制横幅
    }

    abstract class Presenter : BasePresenter<Model?, View?>() {
        abstract fun getBindingTelSMSApi(userTel: String, userPassword: String, type: String, validateCode: String)
        abstract fun getWithdrawRestrictionMsgApi(type: Int) //获取出金限制横幅文案
    }
}
