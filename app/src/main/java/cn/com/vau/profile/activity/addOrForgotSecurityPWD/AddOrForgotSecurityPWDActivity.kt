package cn.com.vau.profile.activity.addOrForgotSecurityPWD

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.*
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.databinding.ActivityAddOrForgotSecurityPwdBinding
import cn.com.vau.page.WithdrawalBundleBean
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.page.user.loginPwd.LoginVeriParam
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha
import com.netease.nis.captcha.Captcha.CloseType
import com.netease.nis.captcha.CaptchaListener

/**
 * 新增/忘记资金安全密码（0/1）
 * sourceState（设置=setup；出金=withdrawal；转账=transfer）
 * Created by zhy on 2018/12/3.
 */
class AddOrForgotSecurityPWDActivity : BaseFrameActivity<AddOrForgotSecurityPWDPresenter, AddOrForgotSecurityPWDModel>(), AddOrForgotSecurityPWDContract.View {

    private val mBinding: ActivityAddOrForgotSecurityPwdBinding by lazy { ActivityAddOrForgotSecurityPwdBinding.inflate(layoutInflater) }

    //来源（新增0/忘记1）
    private val sourceType: Int by lazy { intent.getIntExtra(KEY_TYPE, 0) }

    //设置=setup；出金=withdrawal；转账=transfer
    private val sourceState: String by lazy { intent.getStringExtra(KEY_STATE) ?: "" }

    //出金必备
    private val withdrawalType: Int by lazy { intent.getIntExtra(KEY_WITHDRAWAL_TYPE, 0) }

    //出金Bean Bundle
    private val withdrawalBundleBean: WithdrawalBundleBean? by lazy { intent.getSerializableExtra(KEY_WITHDRAWAL_DATA) as? WithdrawalBundleBean }

    private var captcha: Captcha? = null

    private var isNext = false

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        mPresenter?.sourceState = sourceState
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        mBinding.mHeaderBar.setTitleText(if (sourceType == 0) getString(R.string.set_funds_password) else getString(R.string.forgot_funds_password))
        mBinding.tvSecurityCodePrompt.text = if (sourceType == 0) getString(R.string.funds_password) else getString(R.string.reset_new_funds_password)
        mBinding.etSecurityCode.setHint(if (sourceType == 0) getString(R.string.set_a_8_to_16_digit_funds_password) else getString(R.string.reset_new_funds_password))
        mBinding.tvConfirmSecurityCodePrompt.text = if (sourceType == 0) getString(R.string.confirm_your_funds_password) else getString(R.string.confirm_new_funds_password)
        mBinding.etConfirmSecurityCode.setHint(if (sourceType == 0) getString(R.string.confirm_your_funds_password) else getString(R.string.confirm_new_funds_password))
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append(" !@#\$%^&*.()")
        }
        mBinding.tvMobile.text = UserDataUtil.userTel()
        mBinding.tvAreaCode.text = "+" + UserDataUtil.areaCode()

        mBinding.etSecurityCode.doAfterTextChanged {
            it?.let {
                checkNewPassword()
            }
        }

        mBinding.etConfirmSecurityCode.doAfterTextChanged {
            it?.let {
                checkNewPassword()
            }
        }
        mBinding.tvSendEms.clickNoRepeat {
            if (!isNext) {
                return@clickNoRepeat
            }
            mPresenter?.smsSendType = VerificationActivity.TYPE_SEND_SMS
            mPresenter?.getBindingTelSMSApi(
                UserDataUtil.userTel(), UserDataUtil.countryCode(), UserDataUtil.areaCode(),
                if (sourceType == 0) "5" else "6", ""
            )
        }
        mBinding.viewWhatsApp.clickNoRepeat {
            if (!isNext) {
                return@clickNoRepeat
            }
            mPresenter?.smsSendType = VerificationActivity.TYPE_SEND_WA
            mPresenter?.getBindingTelSMSApi(
                UserDataUtil.userTel(), UserDataUtil.countryCode(), UserDataUtil.areaCode(),
                if (sourceType == 0) "5" else "6", ""
            )
        }
    }

    /**
     * 判断密码是否符合规范，并且会直接改变对应的状态
     */
    private fun checkNewPassword() {
        val password = mBinding.etSecurityCode.getText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        //正则表达式的意思是 包含字母数字和特殊字符
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected = password != "" && password == mBinding.etConfirmSecurityCode.getText()
        updateButton()
    }

    private fun updateButton() {
        if (mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected
        ) {
            isNext = true
            mBinding.tvSendEms.setTextColor(color_cffffff_c1e1e1e)
            mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            mBinding.viewWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
        } else {
            isNext = false
            mBinding.tvSendEms.setTextColor(color_c731e1e1e_c61ffffff)
            mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
            mBinding.viewWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
        }
    }

    private fun initCaptcha() {
        val loginCaptchaListener: CaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter?.getBindingTelSMSApi(
                        mBinding.tvMobile.getText().toString().trim(), UserDataUtil.countryCode(), UserDataUtil.areaCode(),
                        if (sourceType == 0) "5" else "6", validate
                    )
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
                LogUtil.e("Captcha", "验证出错，错误码:$code 错误信息:$msg")
            }

            override fun onClose(closeType: CloseType) {
                if (closeType == CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post(Runnable { })
                }
            }
        }
        captcha = CaptchaUtil.getCaptcha(this, loginCaptchaListener)
    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    override fun refreshSMSCode() {
        VerificationActivity.openActivity(
            this, VerificationActivity.TYPE_SCEURITY_PWD,
            mPresenter.smsSendType,
            LoginVeriParam(
                userPassword = mBinding.etSecurityCode.getText(),
                mobile = UserDataUtil.userTel(),
                areaCode = UserDataUtil.areaCode(),
                countryCode = UserDataUtil.countryCode(),
                nextType = 1,
                sourceType = sourceType,
                sourceState = sourceState,
                withdrawalType = withdrawalType,
                withdrawalData = withdrawalBundleBean,
            )
        )
    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        super.onDestroy()
        captcha?.destroy()
    }

    companion object {

        private const val KEY_TYPE = "type"

        private const val KEY_STATE = "state"

        private const val KEY_WITHDRAWAL_TYPE = "withdrawal_type"

        private const val KEY_WITHDRAWAL_DATA = "withdraw_data"

        /**
         * 设置资金安全码
         */
        const val TYPE_CONFIG = 0

        /**
         * 修改资金安全码
         */
        const val TYPE_CHANGE = 1

        const val STATE_SETUP = "setup"
        const val STATE_WITHDRAWAL = "withdrawal"
        const val STATE_TRANSFER = "transfer"

        fun openActivity(context: Context, sourceType: Int, sourceState: String, withdrawalType: Int? = null, withdrawalBundleBean: WithdrawalBundleBean? = null) {
            val intent = Intent(context, AddOrForgotSecurityPWDActivity::class.java)
            intent.putExtra(KEY_TYPE, sourceType)
            intent.putExtra(KEY_STATE, sourceState)
            intent.putExtra(KEY_WITHDRAWAL_TYPE, withdrawalType)
            intent.putExtra(KEY_WITHDRAWAL_DATA, withdrawalBundleBean)
            context.startActivity(intent)
        }
    }
}
