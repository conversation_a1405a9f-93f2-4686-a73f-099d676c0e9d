package cn.com.vau.trade.fragment.kchart

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.discover.ProductItemInfoData
import cn.com.vau.databinding.FragmentRecyclerviewBinding
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.signals.adapter.AnalysesRecyclerAdapter
import cn.com.vau.util.*
import cn.com.vau.util.widget.NoDataView
import io.reactivex.disposables.Disposable

/**
 * Created by roy on 2018/7/4.
 * K线底部 --> 分析师观点
 */
class ProductItemNewsFragment : BaseFragment() {

    private var nameEn: String? = null

    private val adapter by lazy {
        AnalysesRecyclerAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_analyses))
            })
        }
    }

    private val mBinding by lazy { FragmentRecyclerviewBinding.inflate(layoutInflater) }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View =
        mBinding.root

    override fun initParam() {
        super.initParam()
        nameEn = arguments?.getString(Constants.PARAM_PRODUCT_NAME) ?: ""
    }

    override fun initView() {
        super.initView()
        mBinding.mRecyclerView.setLayoutManager(WrapContentLinearLayoutManager(context))
        mBinding.mRecyclerView.setAdapter(adapter)
    }

    override fun initData() {
        super.initData()
        val params = HashMap<String, String>()
        params["productCode"] = nameEn ?: ""
        params["productName"] = nameEn ?: ""
        if (UserDataUtil.isLogin()) params["userToken"] = UserDataUtil.loginToken()
        params["timeZone"] = AppUtil.getTimeZoneRawOffsetToHour().toString() + ""
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().productListRelatedItems(params),
            object : BaseObserver<ProductItemInfoData?>() {
                @SuppressLint("NotifyDataSetChanged")
                override fun onNext(dataBean: ProductItemInfoData?) {
                    if ("00000000" != dataBean?.resultCode) return

                    val list = dataBean.data.obj.productNews
                    if (list.isNullOrEmpty()) {
                        adapter.setList(null)
                    } else {
                        adapter.setList(list)
                    }
                }

                override fun onHandleSubscribe(d: Disposable) {
                    rxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    adapter.setList(null)
                }
            })
    }

    override fun initListener() {
        super.initListener()
        adapter.setNbOnItemClickListener { _, _, position ->
            val bean = adapter.data.elementAtOrNull(position) ?: return@setNbOnItemClickListener
            openActivity(HtmlActivity::class.java, Bundle().apply {
                putInt("tradeType", 11)
                putString("id", bean.id ?: "")
            })
        }
    }

}
