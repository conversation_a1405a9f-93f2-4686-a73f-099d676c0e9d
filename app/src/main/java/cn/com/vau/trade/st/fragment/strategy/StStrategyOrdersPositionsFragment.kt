package cn.com.vau.trade.st.fragment.strategy

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.dialog.QuickCloseOrderDialog
import cn.com.vau.data.enums.EnumAdapterPosition
import cn.com.vau.data.enums.EnumInitStep
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.FragmentOpenTradesOrderBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.activity.StStrategyPositionDetailsActivity
import cn.com.vau.trade.adapter.OpenTradesRecyclerAdapter
import cn.com.vau.trade.st.model.StStrategyOrdersViewModel
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import org.greenrobot.eventbus.*
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 策略订单详情页 -- 持仓列表 【跟随持仓列表】
 */
class StStrategyOrdersPositionsFragment : BaseMvvmBindingFragment<FragmentOpenTradesOrderBinding>(), SDKIntervalCallback {

    private var mAdapter: OpenTradesRecyclerAdapter? = null

    var shareOrderList: CopyOnWriteArrayList<ShareOrderData>? = null

//    var currentPosition = -1

    private val mViewModel by activityViewModels<StStrategyOrdersViewModel>()

    override fun onCallback() {
        refreshAdapter(false)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
        shareOrderList = mViewModel.shareStrategyData?.positions
    }

    @SuppressLint("WrongConstant")
    override fun initView() {
        mAdapter = OpenTradesRecyclerAdapter(
            requireContext(),
            shareOrderList ?: CopyOnWriteArrayList<ShareOrderData>(),
            EnumAdapterPosition.STRATEGY
        )
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll)

    }

    override fun initData() {
        super.initData()
        refreshAdapter(true)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.closeConfirmPopupLiveData.observe(this) {
            if (it) {
                // 平仓成功
                CenterActionWithIconDialog.Builder(requireActivity())
                    .setContent(getString(R.string.close_confirmed))
                    .setLottieIcon(R.raw.lottie_dialog_ok)
                    .setSingleButton(true)
                    .setSingleButtonText(getString(R.string.ok))
                    .build()
                    .showDialog()
            }
        }
    }

    override fun initListener() {
        super.initListener()

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            InitHelper.initialize(EnumInitStep.SIGNAL_SOURCE)
        }

        mAdapter?.setOnItemClickListener(object : OpenTradesRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {
                openActivity(StStrategyPositionDetailsActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_ORDER_NUMBER, shareOrderList?.getOrNull(position)?.order
                    )
                    putString(
                        Constants.PARAM_STRATEGY_ID, mViewModel.shareStrategyData?.strategyId
                    )
                    // 标注K线页面跳转过去
                    putString(Constants.IS_FROM, "KLine")
                })
            }

            override fun onStartKLine(position: Int) {
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_PRODUCT_NAME,
                        shareOrderList?.getOrNull(position)?.symbol.ifNull()
                    )
                })
            }

            override fun onShareClick(position: Int) {}

            override fun onCloseClick(position: Int) {
                sensorsTrackClose()
                checkFastCloseOrder(shareOrderList?.getOrNull(position) ?: ShareOrderData())
            }

            override fun onEditClick(position: Int) {}

        })

    }

    private fun checkFastCloseOrder(orderData: ShareOrderData?) {
        val fastCloseState = UserDataUtil.fastCloseState()

        if (fastCloseState == "2") {
            QuickCloseOrderDialog.Builder()
                .setLeftClick {
                    CenterActionDialog.Builder(requireActivity())
                        .setContent(getString(R.string.close_trade))
                        .setStartText(getString(R.string.no))
                        .setEndText(getString(R.string.yes_confirm))
                        .setOnEndListener { _ ->
                            mViewModel.stTradePositionClose(orderData)
                        }
                        .build()
                        .showDialog()
                }
                .setRightClick {
                    mViewModel.userSetItemsetApi(1, Constants.KEY_FAST_CLOSE)
                    mViewModel.stTradePositionClose(orderData)
                }
                .show(requireContext(), 1)
            return
        }

        if (fastCloseState == "0") {
            CenterActionDialog.Builder(requireActivity())
                .setContent(getString(R.string.close_trade))
                .setStartText(getString(R.string.no))
                .setEndText(getString(R.string.yes_confirm))
                .setOnEndListener { _ ->
                    mViewModel.stTradePositionClose(orderData)
                }
                .build()
                .showDialog()
            return
        }
        mViewModel.stTradePositionClose(orderData)
    }

    private fun sensorsTrackClose() {
        SensorsDataUtil.track(SensorsConstant.V3510.POSITION_PAGE_CLOSE_BTN_CLICK)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshAdapter(state: Boolean) {
        if (state) {
            mAdapter?.notifyDataSetChanged()
        } else {
            shareOrderList?.let {
                for ((x, dataBean) in it.withIndex()) {
//                    if (dataBean.isRefresh) {
                    mAdapter?.notifyItemChanged(x, Constants.DEFAULT_PAYLOAD_KEY)
                    dataBean.isRefresh = false
//                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 跟随策略列表数据有更新
            NoticeConstants.Init.DATA_SUCCESS_FOLLOWERS_ORDER_ST -> {

                mBinding.mSmartRefreshLayout.finishRefresh()
                mViewModel.hideLoading()

                shareOrderList = VAUSdkUtil.stShareStrategyList().firstOrNull {
                    mViewModel.baseData?.signalStrategyId == it.strategyId
                }?.positions

                mAdapter?.setData(shareOrderList)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun onPause() {
        super.onPause()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        SDKIntervalUtil.instance.removeCallBack(this)
        EventBus.getDefault().unregister(this)
    }

}