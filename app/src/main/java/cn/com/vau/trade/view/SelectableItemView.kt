package cn.com.vau.trade.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.databinding.ViewSelectableItemBinding

class SelectableItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding by lazy { ViewSelectableItemBinding.inflate(LayoutInflater.from(context), this) }
    private val bgSelect by lazy { AppCompatResources.getDrawable(context, R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r4) }
    private val bgUnSelect by lazy { AppCompatResources.getDrawable(context, R.drawable.draw_shape_stroke_c1f1e1e1e_c1fffffff_r4) }

    init {
        updateSelectionUi(false)
    }

    /**
     * 更新选择状态
     */
    fun updateSelectionUi(isSelected: Boolean) {
        binding.ivSelect.isVisible = isSelected
        binding.tvContent.background = if (isSelected) bgSelect else bgUnSelect
    }

    /**
     * 设置内容
     */
    fun setItemContent(content: String) {
        binding.tvContent.text = content
    }

}
