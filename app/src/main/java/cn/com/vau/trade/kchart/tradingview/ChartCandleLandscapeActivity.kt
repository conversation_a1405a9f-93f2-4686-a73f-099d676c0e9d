package cn.com.vau.trade.kchart.tradingview

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.os.*
import android.view.*
import android.webkit.*
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.ILoading
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.kchart.adapter.PositionOrderAdapter
import cn.com.vau.common.view.popup.bean.*
import cn.com.vau.data.init.*
import cn.com.vau.databinding.ActivityChartCandleLandscapeBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.bean.*
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.model.ChartLandscapeModel
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.github.lzyzsd.jsbridge.BridgeWebViewClient
import org.greenrobot.eventbus.*
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.pow

class ChartCandleLandscapeActivity : BaseActivity(), SDKIntervalCallback, ILoading {

    private val mBinding by lazy { ActivityChartCandleLandscapeBinding.inflate(layoutInflater) }
    private val dataBean by lazy { HKLineChartNetBean() }

    private var symbolData: ShareProductData? = null
    private var tradingViewInterface: TradingViewInterface? = null
    var tradingViewInterval: String = ""
    private val model: ChartLandscapeModel by viewModels()

    private val settingPopup: DrawerTradingViewSettingDialog by lazy {
        DrawerTradingViewSettingDialog.Builder(this)
            .setBridge(tradingViewInterface)
            .build()
    }
    private val drawingPopup: DrawerTradingViewDrawingDialog by lazy {
        DrawerTradingViewDrawingDialog.Builder(this)
            .setBridge(tradingViewInterface)
            .build()
    }
    private val positionAdapter by lazy { PositionOrderAdapter(this, CopyOnWriteArrayList(tradingViewInterface?.shareOrderList ?: emptyList()),
        KLineDataUtils.selectedOrderNo) }
    private val positionDialog by lazy {
        BottomListDialog.Builder(this)
            .setViewMode(true)
            .setMaxHeight(268.dp2px())
            .setTitle(getString(R.string.select_order))
            .setAdapter(positionAdapter)
            .build()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        // 全屏 （是为了更好的控制状态栏颜色和隐藏状态栏文字）
        val window = window
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.SOFT_INPUT_MASK_ADJUST
        )
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val lp = window.attributes
            lp.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            window.attributes = lp
        }
        // 不休眠
        window.setFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
        )
        // 输入法
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE or WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        EventBus.getDefault().register(this)
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
        dataBean.nameEn = intent?.getStringExtra(Constants.PARAM_PRODUCT_NAME)
        dataBean.digits = intent?.getIntExtra("chart_digits", 2) ?: 2
        dataBean.periodPosition = intent.getIntExtra("chart_period_position", 1)
        symbolData = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == dataBean.nameEn
        }
        if (KLineDataUtils.userDataTV == null) {
            KLineDataUtils.userDataTV = TradingViewSettingData.getHistoryData()
        }
    }

    @SuppressLint("JavascriptInterface")
    override fun initView() {
        super.initView()
        showLoadDialog()
        mBinding.ivKNewGuide.clickNoRepeat {

            LogEventUtil.setLogEvent(
                BuryPointConstant.V3474.TRADE_KLINE_USER_GUIDE_BUTTON_CLICK,
                bundleOf(
                    Pair(
                        "Type_of_account", when {
                            !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                            UserDataUtil.isStLogin() -> BuryPointConstant.AccountType.COPY_TRADING
                            UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                            else -> BuryPointConstant.AccountType.LIVE
                        }
                    ), Pair("Mode", "Pro-horizontal")
                )
            )

            val intent = intent.putExtra("showNewGuide", true) //更新成功
            setResult(RESULT_OK, intent)
            finish()
        }

        model.setLoadingHandler(this)
        // 设置状态栏高度
        val height = BarUtil.statusBarHeight
        val lp1 = mBinding.viewStatusbar.layoutParams
        lp1.width = height
        mBinding.viewStatusbar.layoutParams = lp1

        mBinding.tvName.text = dataBean.nameEn
        val webSettings = mBinding.mWebView.settings
        // 自适应屏幕
        webSettings.layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        webSettings.loadWithOverviewMode = true
        // 自适应可不设置，但是华为P10有问题
        webSettings.textZoom = 100
        // 允许与js进行交互
        webSettings.javaScriptEnabled = true
        webSettings.allowFileAccessFromFileURLs = true
        // 开放LocalStorage的功能给H5做持久化存储
        webSettings.domStorageEnabled = true
        webSettings.databaseEnabled = true

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }

        mBinding.mWebView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
            }

            override fun onReceivedTitle(view: WebView?, title: String?) {
                super.onReceivedTitle(view, title)
            }

            override fun onJsPrompt(
                view: WebView?,
                url: String?,
                message: String?,
                defaultValue: String?,
                result: JsPromptResult?
            ): Boolean {
                return super.onJsPrompt(view, url, message, defaultValue, result)
            }

            override fun onJsConfirm(
                view: WebView?,
                url: String?,
                message: String?,
                result: JsResult?
            ): Boolean {
                return super.onJsConfirm(view, url, message, result)
            }
        }
        tradingViewInterface = TradingViewInterface(this, dataBean, mBinding.mWebView, symbolData)
        tradingViewInterface?.let {
            mBinding.mWebView.addJavascriptInterface(it, "vfx_android")
        }

        //----------------------- H5初始数据改为加载原生传递到SessionStorage中的数据 （现在原生请求了3000条，可以适当改为1000）-----------------------
        mBinding.mWebView.webViewClient = object : BridgeWebViewClient(mBinding.mWebView) {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                val jsonStr = GsonUtil.buildGson().toJsonTree(KLineDataUtils.mainList).toString()
                //                val js = "javascript:sessionStorage.setItem('${dataBean.nameEn}', '${jsonStr}')"
                //                LogUtil.d("wj", "执行js: $js")
                view?.loadUrl("javascript:sessionStorage.setItem('${dataBean.nameEn}','${jsonStr}')")   //加载不出来
                super.onPageStarted(view, url, favicon)
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideLoadDialog()
            }
        }

        tradingViewInterval = positionToInterval(dataBean.periodPosition)
        // val tradingViewUrl = "https://au-one.app-alpha.com:18008/web/h5/tradingView/index.html" +
        val tradingViewUrl = "file:///android_asset/tradingView/index.html" +
                "?symbol=${dataBean.nameEn}" +
                "&chartType=Candles" +
                "&timezone=Asia/Shanghai" +
                "&product=vau" +
                "&mode=${if (HttpUrl.official) "prod" else "dev"}" +
                "&currency=${
                    UserDataUtil.currencyType()
                }" +
                "&locale=${LanguageHelper.getHtmlLang()}" +
                "&interval=${positionToInterval(dataBean.periodPosition)}" +
                "&priceScale=${10.0.pow(dataBean.digits.toDouble())}" +
                "&theme=${
                    if (AppUtil.isLightTheme()) "Light" else "Dark"
                }" +
                "&token=${UserDataUtil.loginToken()}" +
                "&xtoken=${UserDataUtil.xToken()}" +
                "&serverId=${getServerId()}" +
                "&login=${UserDataUtil.accountCd()}" +
                "&mobileType=${AppUtil.getSystemModel()?.replace(" ", "")?.ifEmpty { "Android" }}" +
                "&userType=${
                    when {
                        UserDataUtil.isStLogin() -> "mts"
                        UserDataUtil.platform() == "5" -> "mt5"
                        else -> "mt4"
                    }
                }" +
                "&utc=${Constants.season}"

        // LogUtil.d("wj", "mWebView.loadUrl: $tradingViewUrl")
        mBinding.mWebView.loadUrl(tradingViewUrl)

    }

    override fun initData() {
        super.initData()
        initOrderData()
        mBinding.tvOrders.isVisible = tradingViewInterface?.shareOrderList?.size.ifNull() > 1
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivBack.setOnClickListener(this)
        mBinding.ivSetting.setOnClickListener(this)
        mBinding.ivDrawing.setOnClickListener(this)
        mBinding.tvOrders.setOnClickListener(this)
        model.notifyLiveData.observe(this) {
            if (it) {
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
            } else {
                tradingViewInterface?.sendSelectedOrder()
            }
        }
        positionAdapter.setOnItemClickListener { position ->
            KLineDataUtils.selectedOrderNo = tradingViewInterface?.shareOrderList?.elementAtOrNull(position)?.order.ifNull("0")
            setOrderData()
            tradingViewInterface?.sendSelectedOrder()
            positionDialog.dismissDialog()
        }
//        selectOrderPopup.setOnDismissListener { setAlpha(1f) }
//        selectOrderPopup.setOnItemClickListener { position ->
//            KLineDataUtils.selectedOrderNo = tradingViewInterface?.shareOrderList?.elementAtOrNull(position)?.order.ifNull("0")
//            setOrderData()
//            tradingViewInterface?.sendSelectedOrder()
//        }
    }

    private fun initOrderData(orderUpdated: Boolean = false) {
        tradingViewInterface?.shareOrderList?.clear()
        if (!UserDataUtil.isLogin()) return
        if (UserDataUtil.isStLogin()) {
            val filterList = VAUSdkUtil.shareOrderList().filter { it.symbol == symbolData?.symbol }
            for (order in filterList) {
                tradingViewInterface?.shareOrderList?.add(convert(order))
            }
        } else {
            val filterList = VAUSdkUtil.shareOrderList().filter { it.symbol == symbolData?.symbol }
            for (order in filterList) {
                tradingViewInterface?.shareOrderList?.add(order)
            }
        }
        setOrderData()
        if (orderUpdated) {
            tradingViewInterface?.sendSelectedOrder()
        }
    }

    private fun setOrderData() {
        if (tradingViewInterface?.shareOrderList?.isNotEmpty() == true) {
            var selectedOrderIndex = 0
            if (KLineDataUtils.selectedOrderNo != "0") {
                selectedOrderIndex = findIndexForOrderNo()      // 为啥要多此一举取Index，直接根据OrderNo取OrderBean不好么？算了，不想改了，与之前保持一致吧
            } else {
                KLineDataUtils.selectedOrderNo = tradingViewInterface?.shareOrderList?.elementAtOrNull(0)?.order.ifNull("0")
            }
            tradingViewInterface?.selectedOrder = tradingViewInterface?.shareOrderList?.elementAtOrNull(selectedOrderIndex)
        } else {
            tradingViewInterface?.selectedOrder = null
        }
    }

    private fun findIndexForOrderNo(): Int {
        if (tradingViewInterface?.shareOrderList?.isNotEmpty() == true) {
            tradingViewInterface?.shareOrderList?.forEachIndexed { index, shareOrderData ->
                if (KLineDataUtils.selectedOrderNo == shareOrderData.order) {
                    return index
                }
            }
        }
        return 0
    }

    private fun getServerId(): String {
        return if (!UserDataUtil.isLogin()) {
            ""
        } else {
            UserDataUtil.serverId()
        }
    }

    private fun positionToInterval(position: Int): String {
        return when (position) {
            0 -> "0"
            1 -> "1"
            2 -> "5"
            3 -> "15"
            4 -> "30"
            5 -> "60"
            6 -> "4H"
            7 -> "1D"
            8 -> "1W"
            9 -> "1M"
            else -> "30"
        }
    }

    private fun intervalToPosition(interval: String): Int {
        return when (interval) {
            "0" -> 0
            "1" -> 1
            "5" -> 2
            "15" -> 3
            "30" -> 4
            "60" -> 5
            "4H" -> 6
            "1D" -> 7
            "1W" -> 8
            "1M" -> 9
            else -> 4
        }
    }

    fun intervalBuryPoint(interval: String) {
        val point = when (interval) {
            "0" -> "Tick"
            "1" -> "1m"
            "5" -> "5m"
            "15" -> "15m"
            "30" -> "30m"
            "60" -> "1h"
            "4H" -> "4h"
            "1D" -> "1D"
            "1W" -> "1W"
            "1M" -> "1M"
            else -> "1D"
        }
        if (point != "Tick") {
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_TIME_BTN_CLICK, JSONObject().apply {
                put("time_interval", point)
                put(SensorsConstant.Key.SCREEN_ORIENTATION, "Landscape")
            })
        }
    }

    private fun convert(st: ShareOrderData): ShareOrderData {
        return ShareOrderData().apply {
            symbol = st.symbol
            stOrder = st.stOrder      // 接口用订单号
            order = st.order          // 显示用订单号
            takeProfit = st.takeProfit
            stopLoss = st.stopLoss
            volume = st.volume
            ask = st.ask
            bid = st.bid
            digits = st.digits
            askType = st.askType
            bidType = st.bidType
            openPrice = st.openPrice.ifNull()
            openTimeStr = st.openTimeStr.ifNull()
            cmd = st.cmd.ifNull()
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ivBack -> {
                finish()
            }

            R.id.ivDrawing -> {
                drawingPopup.show()
                tradingViewInterface?.closeIndicatorSelection()
            }

            R.id.ivSetting -> {
                settingPopup.stillOpenTabIndex = 0
                settingPopup.show()
                tradingViewInterface?.closeIndicatorSelection()

                LogEventUtil.setLogEvent(
                    BuryPointConstant.V345.TRADE_KLINE_SETTINGS_BUTTON_CLICK, bundleOf(
                        "Account_type" to KLineActivity.getPointAccountType(),
                        "Mode" to "Pro-horizontal"
                    )
                )
            }

            R.id.tvOrders -> {
                if (tradingViewInterface?.shareOrderList?.isNotEmpty() == true) {
                    tradingViewInterface?.shareOrderList?.let {
                        positionAdapter.refreshData(CopyOnWriteArrayList(it), KLineDataUtils.selectedOrderNo)
                        positionDialog.showDialog()
//                        selectOrderPopup.setData(
//                            it,
//                            KLineDataUtils.selectedOrderNo, getString(R.string.select_order)
//                        ).show(this@ChartCandleLandscapeActivity, mBinding.root)
                    }

                }
            }
        }
    }

    fun onPositionMoveOver(bean: DragAction) {
        val order = tradingViewInterface?.shareOrderList?.find { it.order == bean.ordernumber }
        val price = bean.price.toDoubleCatching()
        if (order != null && price > 0) {
            val cmd = if ("0" == order.cmd || "2" == order.cmd || "4" == order.cmd) "Buy" else "Sell"
            val isTP = if (cmd == "Buy") {
                price >= symbolData?.originalBid.ifNull()
            } else {
                price <= symbolData?.originalBid.ifNull()
            }
            val priceStr: String = price.numFormat(dataBean.digits, true)
            CenterActionDialog.Builder(this)
                .setTitle(getString(R.string.use_dragn_drop_loss))
                .setContent(
                    if (isTP) getString(R.string.confirm_to_take_profit) + "\n" + getString(R.string.take_profit_price) + ":" + priceStr
                    else getString(R.string.confirm_to_stop_loss) + "\n" + getString(R.string.stop_loss_price) + ":" + priceStr
                )
                .setStartText(getString(R.string.no))
                .setEndText(getString(R.string.yes_confirm))
                .setOnStartListener {
                    tradingViewInterface?.sendSelectedOrder()
                }
                .setOnEndListener {
                    if (isTP) {
                        model.setTakeProfitOrStopLoss(price, order.stopLoss.toDoubleCatching(), order)
                    } else {
                        model.setTakeProfitOrStopLoss(order.takeProfit.toDoubleCatching(), price, order)
                    }
                }.build().showDialog();
        }
    }

    fun onDeleteTpOrSl(bean: DragAction) {
        val order = tradingViewInterface?.shareOrderList?.find { it.order == bean.ordernumber }
        if (order != null) {
            val isTP = "profit" == bean.type
            CenterActionDialog.Builder(this)
                .setContent(getString(if (isTP) R.string.cancel_take_profit else R.string.cancel_stop_loss))
                .setStartText(getString(R.string.no))
                .setEndText(getString(R.string.yes_confirm))
                .setOnEndListener{
                    if (isTP) {
                        model.setTakeProfitOrStopLoss(0.00, order.stopLoss.toDoubleCatching(), order)
                    } else {
                        model.setTakeProfitOrStopLoss(order.takeProfit.toDoubleCatching(), 0.00, order)
                    }
                }.build().showDialog()
        }
    }

    fun showSettingDialog(stillOpenTabIndex: Int = -1) {
        if (stillOpenTabIndex != -1) {
            settingPopup.stillOpenTabIndex = stillOpenTabIndex
        }
        settingPopup.show()
    }

    fun updateIndicatorTradingView() {
        // LogUtil.d("wj", "209更新------------------------")
        tradingViewInterface?.callSettings()
    }

    override fun showLoadDialog() {
        showNetDialog()
    }

    override fun hideLoadDialog() {
        hideNetDialog()
    }

    @SuppressLint("SetTextI18n")
    override fun onCallback() {
        symbolData?.let {
//            val rose = it.rose
//            val rate = it.originalBid - it.open

            mBinding.tvBoardSellprice.text = "${it.bid}"
            //            tv_board_sellprice.setTextColor(
            //                when (it.bidType) {
            //                    1 -> ContextCompat.getColor(context, R.color.c00c79c)
            //                    2 -> ContextCompat.getColor(context, R.color.ce35728)
            //                    else -> ContextCompat.getColor(context, R.color.c7c858a)
            //                }
            //            )

            val rose = it.rose
            val add = if (rose > 0) "+" else ""
            val roseStr = " ($add${it.roseUI}%)"
            val diff = it.diff
            val add2 = if (diff > 0) "+" else ""
            mBinding.tvBoardDiff.text = add2 + it.diffUI + roseStr
            mBinding.tvBoardDiff.setTextColor(ContextCompat.getColor(context, if (diff < 0) R.color.ce35728 else R.color.c00c79c))

            mBinding.tvBoardTime.text = TimeUtil.getSocketDateToString(it.lasttime)
            // 向H5推送行情数据
            tradingViewInterface?.updateQuotes(TradingViewQuoteData(it.originalAsk, it.originalBid, diff, it.lasttime))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String?) {
        when (tag) {
            NoticeConstants.APP_ON_PAUSE -> finish()
            NoticeConstants.Init.DATA_SUCCESS_ORDER -> initOrderData(true)   // 刷新持仓订单列表
            NoticeConstants.Init.DATA_SUCCESS_GOODS -> symbolData = VAUSdkUtil.symbolList().firstOrNull {
                it.symbol == dataBean.nameEn
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        tradingViewInterface?.saveDrawingData(dataBean.nameEn)
        SDKIntervalUtil.instance.removeCallBack(this)
        val data = KLineDataUtils.userDataTV
        // 因TradingView与原生K线主副图图表类型不一致，所以传空值，竖屏跳过不取
        EventBus.getDefault().post(
            KLineEvent(
                intervalToPosition(tradingViewInterval),
                "",
                "", data
            )
        )
        KLineDataUtils.isFrontPortrait = true
        KLineDataUtils.userDataTV?.save()
        KLineDataUtils.userDataTV = null
        EventBus.getDefault().unregister(this)
    }

}