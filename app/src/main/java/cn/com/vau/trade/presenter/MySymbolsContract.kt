package cn.com.vau.trade.presenter

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * 交易自选
 */
interface MySymbolsContract {

    interface Model : BaseModel {
        fun prodUpdApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun stAccountProductMyUpdApi(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable
    }

    interface View : BaseView {
        fun refreshAdapter()
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun updOptionalProdApi()
    }

}
