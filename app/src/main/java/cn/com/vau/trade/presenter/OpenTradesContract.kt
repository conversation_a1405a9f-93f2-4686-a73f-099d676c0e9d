import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.TradeAccountLoginBean
import cn.com.vau.data.trade.StTradePositionUpdateBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 订单持仓
 */
interface OpenTradesContract {

    interface Model : BaseModel {
        fun tradeOrdersClose(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable
        fun tradeAccountLogin(requestBody: RequestBody, baseObserver: BaseObserver<TradeAccountLoginBean>): Disposable
        fun userSetItemset(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun tradeOrdersBatchCloseV2(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable

        fun stTradePositionClose(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable
        fun tradePositionBatchClose(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable
        fun tradeOrdersUpdate(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable
        fun stTradePositionUpdate(body: RequestBody, baseObserver: BaseObserver<StTradePositionUpdateBean>): Disposable
    }

    interface View : BaseView {
        fun refreshAdapter(state: Boolean)
        fun deletePastOrder()
        fun showCheckDelayDialog(orderBean: ShareOrderData)
        fun showHintDataDialog(hintMsg: String)
        fun submitSuccessDialog()
        fun showTokenErrorDialog(msg: String?)
        fun showDealSuccessDialog(dialogTitle: String, dialogContent: String)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun tradeOrdersClose(orderBean: ShareOrderData, checkDelay: Int)
        abstract fun tradeAccountLogin()
        abstract fun userSetItemset(value: Int)
        abstract fun tradeOrdersBatchCloseV2(orderList: CopyOnWriteArrayList<ShareOrderData>)

        abstract fun stTradePositionClose(orderBean: ShareOrderData)
        abstract fun tradePositionBatchClose(orderList:CopyOnWriteArrayList<ShareOrderData>)
        abstract fun tradeOrdersUpdate(orderBean: ShareOrderData?)
        abstract fun stTradePositionUpdate(orderBean: ShareOrderData?)
    }

}
