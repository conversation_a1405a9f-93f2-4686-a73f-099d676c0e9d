package cn.com.vau.trade.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.trade.presenter.StAddSymbolsContract

/**
 * Created by roy on 2018/11/6.
 */
class StAddSymbolsModel : StAddSymbolsContract.Model {

    override fun prodUpdApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean?>) {
        HttpUtils.loadData(RetrofitHelper.getHttpService().prodUpdApi(map), baseObserver)
    }

}