package cn.com.vau.trade.adapter

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.*
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.util.AttrResourceUtil

/**
 * 输入描述
 * Created by THINKPAD on 2018/12/11.
 */
class ProductListAdapter(val context: Activity, val data: List<ShareProductData>? = null, val selectProductName: String?) : RecyclerView.Adapter<ProductListAdapter.ViewHolder>() {
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val symbol = data?.elementAtOrNull(position)?.symbol ?: ""
        holder.tvProductName.text = symbol
        holder.tvProductName.setTextColor(
                if (TextUtils.equals(selectProductName, symbol)) {
                    ContextCompat.getColor(context, R.color.ce35728)
                } else {
                    AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
                }
        )
        holder.itemView.setOnClickListener {
            val bundle = Bundle()
            bundle.putString(Constants.PARAM_PRODUCT_NAME, symbol)
            context.setResult(Constants.RESULT_CODE, Intent().putExtras(bundle))
            context.finish()
        }
    }

    override fun getItemCount(): Int = data?.size ?: 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
            ProductListAdapter.ViewHolder(LayoutInflater.from(context).inflate(R.layout.item_product_list, parent, false))

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view){
        val tvProductName = view.findViewById<TextView>(R.id.tvProductName)
    }
}