package cn.com.vau.trade.view

import android.content.Context
import android.text.Editable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.databinding.OrderInputViewBinding
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.clickNoRepeat

class OrderInputView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy {
        OrderInputViewBinding.inflate(LayoutInflater.from(context),this)
    }

    var onFocus: ((hasFocus: Boolean) -> Unit)? = null
    var onInitInput:(() -> Unit)? = null

    var onAddClick:((text:String) -> Unit)? = null
    var onSubClick:((text:String) -> Unit)? = null

    var onTextChanged:((edt: Editable) ->Unit)? =null

    init {
        initListener()
    }

    private fun initListener() {
        mBinding.tvInputTitle.clickNoRepeat {
            showInputView()
        }

        mBinding.etInput.addTextChangedListener(object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                onTextChanged?.invoke(edt)
           }
        })

        mBinding.etInput.setOnFocusChangeListener { v, hasFocus ->
            onFocus?.invoke(hasFocus)
            if (hasFocus.not()) {
                resetInputView()
            }
        }

        mBinding.ivAdd.clickNoRepeat {
            if (mBinding.etInput.isVisible.not()){
                initInputView()
            }
            onAddClick?.invoke(mBinding.etInput.text.toString())
        }

        mBinding.ivSub.clickNoRepeat {
            if (mBinding.etInput.isVisible.not()){
                initInputView()
            }
            onSubClick?.invoke(mBinding.etInput.text.toString())
        }

    }

    private fun showInputView() {
        mBinding.etInput.isVisible = true
        mBinding.tvInputTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP,12f)
        KeyboardUtil.showSoftInput(mBinding.etInput)
        onInitInput?.invoke()
    }

    fun initInputView() {
        mBinding.etInput.isVisible = true
        mBinding.tvInputTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP,12f)
        onInitInput?.invoke()
    }
    fun resetInputView() {
        if (mBinding.etInput.text.isNullOrEmpty()){
            mBinding.etInput.isVisible = false
            mBinding.tvInputTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP,14f)
        }
    }

    fun setInputTitleText(titleTx:String) {
        mBinding.tvInputTitle.text = titleTx
    }

    fun setEtInputValue(value:String) {
        mBinding.etInput.setText(value)
        mBinding.etInput.setSelection(mBinding.etInput.text.toString().length)
    }

    fun clearEtFocus() {
        mBinding.etInput.clearFocus()
    }

    fun getInputText():String {
        return mBinding.etInput.text.toString()
    }

    fun etInputIsVisible():Boolean {
        return mBinding.etInput.isVisible
    }

    fun isFocus():Boolean {
        return mBinding.etInput.hasFocus()
    }
}