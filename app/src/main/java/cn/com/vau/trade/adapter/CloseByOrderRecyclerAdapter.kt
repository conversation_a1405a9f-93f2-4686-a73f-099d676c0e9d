package cn.com.vau.trade.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.util.*

/**
 * 互抵平仓订单 -- 互抵订单
 */
class CloseByOrderRecyclerAdapter(
    var mContext: Context,
    var dataList: List<ShareOrderData>?
) : RecyclerView.Adapter<CloseByOrderRecyclerAdapter.ViewHolder>() {

    // 已选订单 订单号
    private var selectOrder = ""

    private val currencyType by lazy { UserDataUtil.currencyType() }
    private val c00c79c by lazy { ContextCompat.getColor(mContext, R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(mContext, R.color.ce35728) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff) }
    private val color_c0a1e1e1e_c0affffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff) }
    private val right_icon_checkbox_agree_selected by lazy { ContextCompat.getDrawable(mContext, R.drawable.icon2_cb_tick_circle_c15b374) }
    private val draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14 by lazy { ContextCompat.getDrawable(mContext, R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14) }
    private val pnl by lazy { mContext.getString(R.string.pnl) }
    private val volume by lazy { mContext.getString(R.string.volume) }
    private val lot by lazy { mContext.getString(R.string.lot) }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(
            LayoutInflater.from(mContext).inflate(R.layout.item_recycler_open_trades_other, parent, false)
        )

        holder.itemView.setOnClickListener {
            val orderId = dataList?.getOrNull(holder.bindingAdapterPosition)?.order ?: return@setOnClickListener
            // 已选中
            if (orderId == selectOrder) return@setOnClickListener
            mOnItemClickListener?.onItemClick(orderId)
        }

        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.ivSelect.visibility = View.VISIBLE
        holder.offView.visibility = View.VISIBLE
        holder.offView.setBackgroundColor(color_c0a1e1e1e_c0affffff)

        val data = dataList?.getOrNull(position) ?: return

        holder.ivSelect.setImageDrawable(
            if (data.order != selectOrder)
                draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
            else
                right_icon_checkbox_agree_selected
        )

        // 产品名称
        holder.tvProdName.setTextDiff(data.symbol.ifNull())

        holder.ivKLine.visibility = View.INVISIBLE

        holder.tvPnlTitle.setTextDiff("${pnl} (${currencyType})")

        // 买卖类型
        if (OrderUtil.isBuyOfOrder(data.cmd)) {
            holder.tvOrderType.setTextDiff("Buy")
            holder.tvOrderType.setTextColorDiff(c00c79c)
            holder.tvOrderType.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c1f00c79c_r100)
        } else {
            holder.tvOrderType.setTextDiff("Sell")
            holder.tvOrderType.setTextColorDiff(ce35728)
            holder.tvOrderType.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c1fe35728_r100)
        }

        holder.tvOrderId.setTextDiff("#${data.order.ifNull()}")

        // 订单盈亏
        holder.tvPnl.setTextDiff(
            if ("-" == data.closePrice) "-" else data.profitUI.ifNull()
        )

        // 手数
        holder.tvVolTitle.setTextDiff("$volume ($lot)")
        holder.tvVolume.setTextDiff(data.volumeUI.ifNull())

        // 开仓价
        holder.tvOpenPrice.setTextDiff(data.openPrice.ifNull())
        // 现价
        holder.tvCurrentPrice.setTextDiff(
            if ("-" == data.closePrice) "-" else data.currentPriceUI.ifNull()
        )

        if ("-" == data.closePrice) {
            holder.tvPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
        } else {
            holder.tvPnl.setTextColorDiff(if (data.profit >= 0) c00c79c else ce35728)
        }

    }

    override fun getItemCount(): Int = dataList?.size ?: 0

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val ivSelect: ImageView = view.findViewById(R.id.ivSelect)
        val tvProdName: TextView = view.findViewById(R.id.tvProdName)
        val ivKLine: ImageView = view.findViewById(R.id.ivKLine)
        val tvPnlTitle: TextView = view.findViewById(R.id.tvPnlTitle)
        val tvOrderType: TextView = view.findViewById(R.id.tvOrderType)
        val tvOrderId: TextView = view.findViewById(R.id.tvOrderId)
        val tvPnl: TextView = view.findViewById(R.id.tvPnl)

        val tvVolTitle: TextView = view.findViewById(R.id.tvVolTitle)
        val tvVolume: TextView = view.findViewById(R.id.tvVolume)

        val tvOpenPriceTitle: TextView = view.findViewById(R.id.tvOpenPriceTitle)
        val tvOpenPrice: TextView = view.findViewById(R.id.tvOpenPrice)

        val tvCurrentPriceTitle: TextView = view.findViewById(R.id.tvCurrentPriceTitle)
        val tvCurrentPrice: TextView = view.findViewById(R.id.tvCurrentPrice)

        val offView: View = view.findViewById(R.id.offView)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(dataList: List<ShareOrderData>?) {
        this.dataList = dataList
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setDataOfCloseByOrderId(selectOrder: String) {
        this.selectOrder = selectOrder
        // 虽然 adapter 在循环刷新，如果设置 selectOrder 后不全局刷新，选中按钮更新会有延迟
        notifyDataSetChanged()
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(orderId: String)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}
