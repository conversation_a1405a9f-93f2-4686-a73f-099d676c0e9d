package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.databinding.DialogBottomSymbolSearchBinding
import cn.com.vau.trade.dialog.BottomSymbolSearchDialog
import cn.com.vau.trade.ext.SSDialogUiState
import cn.com.vau.trade.ext.SymbolSearchConstants.TAG_SS_CATEGORY_FRAGMENT
import cn.com.vau.trade.ext.SymbolSearchConstants.TAG_SS_SEARCH_FRAGMENT
import cn.com.vau.trade.fragment.search.SSCategoryFragment
import cn.com.vau.trade.fragment.search.SSSearchFragment
import cn.com.vau.trade.viewmodel.SymbolSearchDialogViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Created by array on 2025/4/24 17:44
 * Desc: 页面调度器 （统一管理布局切换等）
 */
class SSDispatcherPerformance(
    val activity: FragmentActivity, val dialog: BottomSymbolSearchDialog, val mBinding: DialogBottomSymbolSearchBinding, val mViewModel: SymbolSearchDialogViewModel
) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initView()
    }

    fun initView() {

        /** 弹窗状态 */
        dialog.lifecycleScope.launch {
            mViewModel.dialogUiState.flowWithLifecycle(dialog.lifecycle, Lifecycle.State.STARTED).collectLatest { it ->
                when (it) {
                    is SSDialogUiState.CategoryLayout -> {
                        switchToCategoryLayout()
                    }

                    is SSDialogUiState.SearchLayout -> {
                        switchToSearchLayout()
                    }
                }
            }
        }
    }

    private fun showFragment(fragment: Fragment, tag: String) {
        val fragmentManager = activity.supportFragmentManager
        if (activity.isFinishing || activity.isDestroyed || fragmentManager.isStateSaved) {
            return
        }
        val transaction: FragmentTransaction = fragmentManager.beginTransaction()
        val existingFragment = fragmentManager.findFragmentByTag(tag)
        fragmentManager.fragments.forEach {
            if (it is SSCategoryFragment || it is SSSearchFragment) {
                transaction.hide(it)
            }
        }
        if (existingFragment != null) {
            transaction.show(existingFragment)
        } else {
            transaction.add(R.id.ll_container, fragment, tag)
        }
        transaction.commit()
    }

    /**
     * 切换为分类页
     */
    @SuppressLint("SetTextI18n")
    private fun switchToCategoryLayout() {
        showFragment(SSCategoryFragment.newInstance().apply { this.setViewModel(mViewModel) }, TAG_SS_CATEGORY_FRAGMENT)
    }

    /**
     * 切换为搜索页
     */
    private fun switchToSearchLayout() {
        showFragment(SSSearchFragment.newInstance().apply { this.setViewModel(mViewModel) }, TAG_SS_SEARCH_FRAGMENT)
    }

}