package cn.com.vau.ui.deal.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.trade.FreeOrdersBean
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.numCurrencyFormat

/**
 * 免费订单
 */
class FreeStockOrderRecyclerAdapter(
    var mContext: Context,
    var dataList: ArrayList<FreeOrdersBean.Obj>
) : RecyclerView.Adapter<FreeStockOrderRecyclerAdapter.ViewHolder>() {

    private val c00c79c by lazy { ContextCompat.getColor(mContext, R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(mContext, R.color.ce35728) }
    private val color_c0a1e1e1e_c0affffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff) }
    private val lots by lazy { mContext.getString(R.string.lots) }
    private val pnl by lazy { mContext.getString(R.string.pnl) }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val holder = ViewHolder(
            LayoutInflater.from(mContext).inflate(R.layout.item_recycler_order_free_stock, parent, false)
        )
        holder.tvClose.setOnClickListener {
            mOnItemClickListener?.onCloseOrderClick(holder.bindingAdapterPosition)
        }
        holder.ivKLine.setOnClickListener {
            mOnItemClickListener?.onStartKLine(holder.bindingAdapterPosition)
        }
        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val dataBean = dataList.getOrNull(position)?: return

        holder.tvProdName.text = dataBean.name

        holder.tvClose.isVisible = dataBean.unlockVolume == dataBean.unlockTargetVolume

        holder.tvExpiringSoon.isVisible = "1" == dataBean.isExpireShow
        holder.tvFreeStock.text = "#${dataBean.order}"
        holder.tvVolume.text = "${dataBean.volume} $lots"

        holder.tvPnlTitle.text = "${pnl}(${UserDataUtil.currencyType()})"
        holder.tvPnl.text = "${dataBean.profit?.numCurrencyFormat()}"

        holder.tvDate.text = dataBean.openTime ?: ""

        holder.tvExpiryDate.text = dataBean.expireTime.ifNull("--")

        holder.tvUnlockLot.text = "${dataBean.unlockVolume.ifNull("0.00")}/${dataBean.unlockTargetVolume.ifNull("0.00")} $lots"

        holder.tvPnl.setTextColor(
            if ((dataBean.profit ?: 0.0) >= 0) c00c79c else ce35728
        )

        holder.offView.setBackgroundColor(color_c0a1e1e1e_c0affffff)

    }

    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvProdName: TextView = view.findViewById(R.id.tvProdName)
        val ivKLine: ImageView = view.findViewById(R.id.ivKLine)
        val tvExpiringSoon: TextView = view.findViewById(R.id.tvExpiringSoon)
        val tvFreeStock: TextView = view.findViewById(R.id.tvFreeStock)
        val tvVolume: TextView = view.findViewById(R.id.tvVolume)
        val tvPnlTitle: TextView = view.findViewById(R.id.tvPnlTitle)
        val tvPnl: TextView = view.findViewById(R.id.tvPnl)
        val tvDate: TextView = view.findViewById(R.id.tvDate)
        val tvExpiryDate: TextView = view.findViewById(R.id.tvExpiryDate)
        val tvUnlockLot: TextView = view.findViewById(R.id.tvUnlockLot)
        val tvClose: TextView = view.findViewById(R.id.tvClose)
        val offView: View = view.findViewById(R.id.offView)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onCloseOrderClick(position: Int)
        fun onStartKLine(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}