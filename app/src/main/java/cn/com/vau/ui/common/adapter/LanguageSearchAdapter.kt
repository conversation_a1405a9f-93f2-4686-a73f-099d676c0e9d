package cn.com.vau.ui.common.adapter

import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.profile.LanguageBean
import cn.com.vau.util.matcherSearchText
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * author：lvy
 * date：2024/12/05
 * desc：语言搜索列表
 */
class LanguageSearchAdapter : BaseQuickAdapter<LanguageBean, BaseViewHolder>(R.layout.item_language_list) {

    override fun convert(holder: BaseViewHolder, item: LanguageBean) {
        val languageResultName = item.sourceLanguageName?.matcherSearchText(
            item.searchKey, ContextCompat.getColor(context, R.color.ce35728)
        )
        val translateResultName = item.translatedLanguageName?.matcherSearchText(
            item.searchKey, ContextCompat.getColor(context, R.color.ce35728)
        )

        holder.setText(R.id.tvLanguageName, languageResultName)
            .setText(R.id.tvTranslateName, translateResultName)
            .setImageResource(
                R.id.ivCheck, if (item.isSelected) {
                    R.drawable.icon2_cb_tick_circle_c15b374
                } else {
                    R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
                }
            )
    }
}