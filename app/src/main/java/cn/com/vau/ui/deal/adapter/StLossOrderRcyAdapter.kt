package cn.com.vau.ui.deal.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.trade.TradeLossHistoryBean
import com.google.android.material.checkbox.MaterialCheckBox

/**
 * Created by roy on 2018/10/25.
 */
class StLossOrderRcyAdapter(
        var mContext: Context,
        var dataList: ArrayList<TradeLossHistoryBean.TradeData>
) : RecyclerView.Adapter<StLossOrderRcyAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_rcy_loss_order, parent, false))
        holder.itemView.setOnClickListener {
            mOnItemClickListener?.onItemClick(holder.adapterPosition)
        }
        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindTo(mContext, dataList.elementAtOrNull(position))
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        val tvOrderNum = view.findViewById<TextView>(R.id.tvOrderNum)
        val tvOrderType = view.findViewById<TextView>(R.id.tvOrderType)
        val tvVolume = view.findViewById<TextView>(R.id.tvVolume)
        val tvProdName = view.findViewById<TextView>(R.id.tvProdName)
        val tvOpenPrice = view.findViewById<TextView>(R.id.tvOpenPrice)
        val tvClosePrice = view.findViewById<TextView>(R.id.tvClosePrice)
        val tvPnlTitle = view.findViewById<TextView>(R.id.tvPnlTitle)
        val tvFloatingPnL = view.findViewById<TextView>(R.id.tvFloatingPnL)

        val mcbOrder = view.findViewById<MaterialCheckBox>(R.id.mcbOrder)

        @SuppressLint("UseCompatLoadingForDrawables", "SetTextI18n")
        fun bindTo(mContext: Context, dataBean: TradeLossHistoryBean.TradeData?) {

            dataBean?.run {

                tvOrderNum.text = positionId
                tvOrderType.text = if (dataBean.dealAction == "DealBuy") "Buy" else "Sell"
                tvVolume.text = "${closedVolume ?: (positionVolume ?: "")}${mContext.getString(R.string.lots)}"
                tvProdName.text = symbol ?: ""

                // 开仓平仓
                tvOpenPrice.text = openPrice ?: ""
                tvClosePrice.text = closePrice ?: ""
                // 盈亏
                tvPnlTitle.text = "${mContext.getString(R.string.pnl)} (${UserDataUtil.currencyType()})"
                tvFloatingPnL.text = "${profit ?: ""} ${UserDataUtil.currencyType()}"

                mcbOrder.isChecked = checkState

            }

        }

    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }
}