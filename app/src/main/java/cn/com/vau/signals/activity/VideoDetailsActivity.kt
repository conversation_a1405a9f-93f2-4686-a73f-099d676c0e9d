package cn.com.vau.signals.activity

import android.content.*
import android.graphics.Outline
import android.os.Bundle
import android.view.*
import android.widget.ImageView
import androidx.activity.OnBackPressedCallback
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.discover.WebTVObj
import cn.com.vau.databinding.ActivityVideoDetailsBinding
import cn.com.vau.signals.adapter.WebTvAdapter
import cn.com.vau.signals.viewModel.WebTvViewModel
import cn.com.vau.util.*
import cn.jzvd.*
import com.bumptech.glide.request.RequestOptions

/**
 * 视频详情
 * Created by zhy on 2018/11/15.
 */
class VideoDetailsActivity : BaseMvvmActivity<ActivityVideoDetailsBinding, WebTvViewModel>() {

    private val webTvRecyclerAdapter: WebTvAdapter by lazy { WebTvAdapter() }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.playDataLiveData.value = intent.getParcelableExtra<WebTVObj>(KEY_PLAY_DATA)
        mViewModel.uiListLiveData.value = ListUIState.RefreshSuccess(intent.getParcelableArrayListExtra<WebTVObj>(KEY_WEB_TV_LIST))
        mViewModel.date = intent.getParcelableArrayListExtra<WebTVObj>(KEY_WEB_TV_LIST)?.lastOrNull()?.date.ifNull()
    }

    override fun initView() {
        mBinding.videoPlayer.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(
                    0,
                    0,
                    view.width,
                    view.height,
                    10f.dp2px()
                )
            }
        }
        mBinding.videoPlayer.clipToOutline = true
        mBinding.mRecyclerView.adapter = webTvRecyclerAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(context = this, R.attr.color_c1f1e1e1e_c1fffffff)))
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.playDataLiveData.observe(this) { bean ->
            if (bean == null)
                return@observe
            mBinding.tvVideoTitle.text = bean.videoName
            mBinding.tvVideoContent.text = bean.description
            mBinding.tvVideoTime.text = bean.createTime
            mBinding.tvViews.text = bean.views
            mBinding.videoPlayer.setUp(
                bean.url,
                "",
                Jzvd.SCREEN_WINDOW_NORMAL
            )
            mBinding.videoPlayer.thumbImageView.setScaleType(ImageView.ScaleType.CENTER_CROP)
            mBinding.videoPlayer.fullscreenButton.clickNoRepeat {
                if (mBinding.videoPlayer.currentState == Jzvd.CURRENT_STATE_AUTO_COMPLETE) {
                    Jzvd.setVideoImageDisplayType(Jzvd.VIDEO_IMAGE_DISPLAY_TYPE_FILL_PARENT)
                    return@clickNoRepeat
                }
                if (mBinding.videoPlayer.currentScreen == Jzvd.SCREEN_WINDOW_FULLSCREEN) {
                    //quit fullscreen
                    Jzvd.backPress()
                    Jzvd.setVideoImageDisplayType(Jzvd.VIDEO_IMAGE_DISPLAY_TYPE_FILL_PARENT)
                } else {
                    mBinding.videoPlayer.onEvent(JZUserAction.ON_ENTER_FULLSCREEN)
                    Jzvd.setVideoImageDisplayType(Jzvd.VIDEO_IMAGE_DISPLAY_TYPE_ADAPTER)
                    mBinding.videoPlayer.startWindowFullscreen()
                }
            }
            Jzvd.setVideoImageDisplayType(Jzvd.VIDEO_IMAGE_DISPLAY_TYPE_FILL_PARENT)
            val myOptions = RequestOptions()
                .placeholder(R.drawable.shape_placeholder)
                .error(R.drawable.shape_placeholder)
            ImageLoaderUtil.loadImageWithOption(this@VideoDetailsActivity, bean.cover, mBinding.videoPlayer.thumbImageView, myOptions)
            mBinding.videoPlayer.startVideo()
            mViewModel.addRecord(bean.videoId)
        }
        mViewModel.uiListLiveData.observeUIState(this@VideoDetailsActivity, webTvRecyclerAdapter, mBinding.mRefreshLayout)
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setStartBackIconClickListener {
            prepareData()
            finish()
        }
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                prepareData()
                finish()
            }
        })
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.refresh()
        }
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mViewModel.loadMore()
        }
        webTvRecyclerAdapter.setNbOnItemClickListener { _, _, position ->
            val data = webTvRecyclerAdapter.getItemOrNull(position)
            mViewModel.playDataLiveData.value = data
            webTvRecyclerAdapter.getItemOrNull(position)?.views = data?.views.mathAdd("1")
            webTvRecyclerAdapter.notifyItemChanged(position, Constants.DEFAULT_PAYLOAD_KEY)
        }
    }

    private fun prepareData() {
        setResult(Constants.RESULT_CODE, Intent().apply {
            putParcelableArrayListExtra(KEY_PLAY_DATA, arrayListOf<WebTVObj>().apply {
                addAll(webTvRecyclerAdapter.data)
            })
        })
    }

    override fun onBackPressed() {
        prepareData()
        if (Jzvd.backPress()) {
            return
        }
        super.onBackPressed()
    }

    override fun onPause() {
        super.onPause()
        Jzvd.releaseAllVideos()
    }

    override fun onDestroy() {
        super.onDestroy()
        JZUtils.clearSavedProgress(this, mBinding.videoPlayer.currentUrl)
    }

    companion object {

        const val KEY_WEB_TV_LIST = "web_tv_list"
        private const val KEY_PLAY_DATA = "play_data"

        fun createIntent(context: Context, playData: WebTVObj?, list: ArrayList<WebTVObj>): Intent {
            return Intent(context, VideoDetailsActivity::class.java).apply {
                putExtra(KEY_PLAY_DATA, playData)
                putParcelableArrayListExtra(KEY_WEB_TV_LIST, list)
            }
        }
    }
}
