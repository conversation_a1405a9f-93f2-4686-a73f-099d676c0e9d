package cn.com.vau.signals.stsignal.center.vm

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.strategy.*
import cn.com.vau.util.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * author：lvy
 * date：2024/04/16
 * desc：信号源中心 -> Strategies
 */
class StStrategiesViewModel : BaseViewModel() {

    private val stUserId by lazy { UserDataUtil.stUserId() }

    val strategyListLiveData = MutableLiveData<StProfileStrategiesBean?>() // 策略列表
    val publishStrategySuccessLiveData = MutableLiveData<Boolean>() //上架成功
    val publishStrategyHasSameLiveData = MutableLiveData<Boolean>() // 该策略源账号已经有上架的策略了，需要弹框提示
    val delistStrategyLiveData = MutableLiveData<Any?>() //下架成功

    fun minInvestedValue(strategy:StrategyBean): Int {
        return when (strategy.loginAccountCurrency ?: UserDataUtil.currencyType()) {
            "HKD" -> 400
            "JPY" -> 7000
            "USC", "INR" -> 4000
            else -> 50
        }
    }

    /**
     * 策略列表
     */
    fun stProfileStrategyListApi() {
        requestNet({ stTradingService.strategyListApi(stUserId) }, {
            if (it.isSuccess()) {
                strategyListLiveData.value = it.data
            }
        })
    }

    /**
     * 上架策略（下架策略列表点击上架时请求这个接口）
     */
    fun stPublishStrategyApi(strategyId: String?) {
        requestNet({ stTradingService.strategyPublishApi(stUserId, strategyId) }, {
            if (it.getResponseCode() == "10585") { // 该策略源账号已经有上架的策略了
                publishStrategyHasSameLiveData.value = true
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            publishStrategySuccessLiveData.value = true
        }, isShowDialog = true)
    }

    /**
     * 下架策略（上架策略列表点击下架时请求这个接口）
     */
    fun stDelistStrategyApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = stUserId
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.strategyDelistApi(requestBody) }, {
            if (it.isSuccess()) {
                delistStrategyLiveData.value = it.data
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }

    /**
     * 创建策略（草稿上架时请求这个接口）
     */
    fun createStrategyApi(bean: StrategyBean?) {
        bean?.loginAccountId = UserDataUtil.stAccountId() // ********新增参数
        val jsonObj = GsonUtil.buildGson().toJsonTree(bean).asJsonObject
        requestNet({ stTradingService.strategyNewWithAccountApi(jsonObj) }, {
            if (it.getResponseCode() == "10585") { // 该策略源账号已经有上架的策略了
                publishStrategyHasSameLiveData.value = true
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            publishStrategySuccessLiveData.value = true
            clearCurrentStrategy(bean)
        }, isShowDialog = true)
    }

    /**
     * 创建策略成功，清除本地草稿缓存
     */
    private fun clearCurrentStrategy(bean: StrategyBean?) {
        bean ?: return
        val list: MutableList<StrategyBean> = SpManager.getStrategyListDraft() ?: mutableListOf()
        val index = list.indexOfFirst { it.localCreateTime == bean.localCreateTime }
        if (index != -1 && index < list.size) {
            list.removeAt(index)
        }
        SpManager.putStrategyListDraft(list)
    }
}