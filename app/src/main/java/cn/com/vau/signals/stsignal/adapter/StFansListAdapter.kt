package cn.com.vau.signals.stsignal.adapter

import cn.com.vau.R
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.data.strategy.StFansListBean
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * author：lvy
 * date：2024/04/18
 * desc：粉丝列表
 */
class StFansListAdapter : BaseQuickAdapter<StFansListBean, BaseViewHolder>(R.layout.item_st_fans_list) {

    override fun convert(holder: BaseViewHolder, item: StFansListBean) {
        //头像
        val ivAvatar = holder.getView<ShapeableImageView>(R.id.ivAvatar)
        ImageLoaderUtil.loadImage(context,item.avatar,ivAvatar,R.mipmap.ic_launcher)

        //名称
        holder.setText(R.id.tvNick, item.nickname)
    }
}