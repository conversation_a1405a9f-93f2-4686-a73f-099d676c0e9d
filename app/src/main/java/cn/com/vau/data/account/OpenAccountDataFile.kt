package cn.com.vau.data.account

import android.content.Context
import androidx.annotation.Keep
import cn.com.vau.R
import cn.com.vau.data.BaseBean
import cn.com.vau.data.msg.PushBean
import cn.com.vau.page.common.BaseListBean
import java.io.Serializable

// Type: 开通开同名相关

/**
 * 查询可开通的账户类型(获取申请开通mt4账户号类型)
 */
@Keep
class MT4AccountTypeBean : BaseBean() {
    var data: MT4AccountTypeData? = null
}

@Keep
class MT4AccountTypeData {
    var obj: MT4AccountTypeObj? = null
}

@Keep
class MT4AccountTypeObj {
    var userType: String? = null

    /**
     * 申请类型(
     * 0:不能申请,
     * 1:真实账户开通，
     * 2:同名账户，
     * 3:重新申请，
     * 4:身份证明未通过，
     * 5:只读账户身份证明未通过，
     * 6:账户被拒绝，
     * 7:未上传身份证明，
     * 8:未上传地址证明【ASIC监管不会返回此类型】
     * 9:地址证明审核不通过【ASIC监管不会返回此类型】 )
     */
    var applyTpe = 0
    var stepStr: String? = null
    var regulator: String? = null
    var accountOpenType = 0
    var status = 0
    var listMt4AccountApplyType: ArrayList<Mt4AccountApplyType>? = null
}

@Keep
class Mt4AccountApplyType : Serializable {
    var mt4AccountId: String? = null
    var mt4AccountType = 0
}

/**
 * 获取开户已经填写的信息
 */
@Keep
data class GetProcessData(
    var data: GetProcessDataObj? = null
) : BaseBean()

@Keep
data class GetProcessDataObj(
    var obj: GetProcessObj? = null
)

@Keep
data class GetProcessObj(
    var supervisionType: String? = null,    //监管类型
    var supervisionName: String? = null,    //监管名称
    // step 1
    var email: String? = null,
    var firstName: String? = null,
    var middleName: String? = null,
    var lastName: String? = null,
    var title: String? = null,
    var gender: String? = null,
    var dob: String? = null,
    var nationalityName: String? = null,
    var countryName: String? = null,
    var nationalityId: Int? = -1,
    var countryId: Int? = -1,
    var confirmDialog: Boolean = false,
    // step 4
    var tradingPlatform: String? = null,
    var accountType: String? = null,
    var currency: String? = null,
    var accountCreated: Boolean? = null,
    var accountReadOnly: Boolean? = null,
    var skipNextStep: Boolean? = null,
    var hideAccountType: Boolean? = null,
    var accountStatus: Int? = -1,
    // step 5
    var state: String? = null,
    var suburb: String? = null,
    var streetNumber: String? = null,
    var streetName: String? = null,
    var postcode: String? = null,
    var specialCountry: Int? = -1,   //是否特殊国家   0.非特殊国家   1.法国和英国需要走ID3验证特殊字段  2.马来需要走ID3不验证特殊字段
    var idType: Int? = -1,  //证件类型
    var idNumber: String? = null, //证件号码
    var idDocFilePathList: List<String>? = null, //证件图片集合
    var idFileList: List<UploadFileInfo>? = null, //带格式说明的证件图片集合
    var address: String? = null, //详细地址
    var poaType: Int? = -1,  //POA验证类型（Property bills, Bank statement , Other）
    var poaDocFilePathList: List<String>? = null, //地址证明图片集合
    var poaFileList: List<UploadFileInfo>? = null, //带格式说明的地址证明图片集合
)

@Keep
data class UploadFileInfo(
    val filePath: String? = null,
    val fileName: String? = null,
    val fileType: String? = null,
)

/**
 * 获取同名账户的相关信息
 */
@Keep
data class PlatFormAccountData(
    val `data`: Data?
) : BaseBean() {

    @Keep
    data class Data(
        val obj: List<Obj>?
    )

    @Keep
    data class Obj(
        // 显示名字
        val displayPlatFormName: String?,
        // 图片地址
        val listImage: List<Image>?,
        // 平台账户类型集合
        val listPlatFormAccountType: List<PlatFormAccountType>?,
        // 平台名字 mts/MT4/MT5
        val platFormName: String?,
        // 平台编号 2/4/5
        val platFormNum: String?,

        // 新开户-新增平台说明文案
        var platTypeTitle: PlatformTypeObj? = null,
    ) : BaseListBean {
        override fun getShowItemValue(): String = displayPlatFormName ?: ""

        // 标签的背景
        override fun getLabelBgRes(context: Context): Int? {
            return when {
                "mt5".equals(platFormName, true) -> R.drawable.shape_ce35728_r100
                "mts".equals(platFormName, true) -> R.drawable.shape_c034854_r100
                "vts".equals(platFormName, true) -> R.drawable.shape_c034854_r100
                else -> 0
            }
        }

        // 标签的文字
        override fun getLabelStr(context: Context): String? {
            return when {
                "mt5".equals(platFormName, true) -> context.getString(R.string.recommended)
                "mts".equals(platFormName, true) -> context.getString(R.string.popular)
                "vts".equals(platFormName, true) -> context.getString(R.string.self_developed)
                else -> ""
            }
        }
    }

    @Keep
    data class Image(
        val imgSize: String?,
        val imgType: Int?,
        // 只用到这个，平台 logo
        val imgUrl: String?
    )

    @Keep
    data class PlatFormAccountType(
        // 账户类型名称
        val accountTypeName: String?,
        // 账户类型编号
        val accountTypeNum: Int?,
        // 币种集合
        val listCurrency: List<Currency>?,
        // 图片地址
        val listImage: List<ImageXX>?
    ) : BaseListBean {
        override fun getShowItemValue(): String = accountTypeName ?: ""
    }

    @Keep
    data class OpenAccountData(
        var platform: String? = null,
        var accountType: Int? = null,
        var currency: String? = null
    )

    @Keep
    data class Currency(
        // 币种名称
        val currencyName: String?,
        // 图片地址
        val listImage: List<ImageX>?
    ) : BaseListBean {
        override fun getShowItemValue(): String = currencyName ?: ""
    }

    @Keep
    data class ImageXX(
        val imgSize: String?,
        val imgType: Int?,
        // 图片地址
        val imgUrl: String?
    )

    @Keep
    data class ImageX(
        val imgSize: String?,
        val imgType: Int?,
        val imgUrl: String?
    )

}

/**
 * 是否可以开通/显示钱包
 */
@Keep
class OpenWalletBean : BaseBean() {
    var data: OpenWalletData? = null
}

@Keep
data class OpenWalletData(
    var obj: OpenWalletObj?
)

@Keep
data class OpenWalletObj(
    var showWallet: Boolean? = false    // 是否显示钱包（false: 不显示，true: 显示）
)

/**
 * 检查是否有NDB活动 -- 开同名
 */
@Keep
data class NDBStatusBean(
    val `data`: Data?
) : BaseBean() {
    @Keep
    data class Data(
        val status: Int, //1：正在参加活动 ；0：未参加活动
    )
}

/**
 * 开户缓存相关
 */
@Keep
data class RealAccountCacheBean(
    var data: RealAccountCacheData?
) : BaseBean()

@Keep
data class RealAccountCacheData(
    var isAppraisal: Boolean?,
    var obj: RealAccountCacheObj?,
    val emailEventID: String?, //神策埋点绑定用户使用
)

@Keep
data class RealAccountCacheObj(
    var skipNextStep: Boolean?,
    var isStAccountType: Boolean?,
    var emailStatus: Boolean?,
    var userId: String?,//ebe29fdb6cb440edb82d275e64c61113",
    var supervisionType: String?,
    var email: String?,//<EMAIL>",
    var firstName: String?,//张",
    var middleName: String?,
    var lastName: String?,//单",
    var title: String?,//Mr",
    var idType: Int?,//National ID Card",
    var idNumber: String?,
    var phoneCode: String?,
    var mobile: String?,
    var nationalityId: String?,
    var nationalityName: String?,
    var placeOfName: String?,
    var placeOfBirth: String?,
    var referredBy: String?,
    var dob: String?,
    var updateEmail: Boolean?,

    var address: String?,
    var unitApt: String?,
    var usCitizen: Boolean?,
    var countryId: String?,
    var state: String?,
    var countryName: String?,
    var stateName: String?,
    var suburbName: String?,
    var suburb: String?,

    var employmentFinanceAnswers: MutableList<RealAccountQuestionBean>?,
    var tradingAnswers: MutableList<RealAccountQuestionBean>?,

    var accountType: Int?,
    var currency: String?,
    var tradingPlatform: String?,
    var postcode: String?,
    var type: Int?,

    var readingProtocol: Int?,

    var list: MutableList<OpenImageData>?,
    var verifyMethod: String?,
    var verificationToken: String?,
    var url: String?,
    var isAppraisal: Boolean?,       // 是否做过问卷或测评过
    val emailEventID: String?, //神策埋点绑定用户使用
)

@Keep
class RealAccountQuestionBean {
    var questionId: Int? = null
    var answers: MutableList<Int>? = null
}

@Keep
data class OpenImageData(
    var crmAddress: String?,
    var oosAddress: String?
)

/**
 * 贸易知识和经验（澳洲接口数据）-- 开户
 */
@Keep
data class EmploymentBean(
    var data: EmploymentData? = null
) : BaseBean()

@Keep
class EmploymentData(
    var obj: MutableList<EmploymentQuestionObj>?
)

@Keep
data class EmploymentQuestionObj(
    var questionId: Int?,
    var desc: String?,
    var priority: String?,
    var questionOptions: List<QuestionOption>?
)

@Keep
data class QuestionOption(
    var id: Int?,
    var desc: String?,
    var priority: String?
) : BaseListBean {
    override fun getShowItemValue(): String = desc ?: ""
}

/**
 * 获取下拉列表
 */
@Keep
data class MoreAboutYouBean(
    var data: MoreAboutYouData? = null
) : BaseBean()

@Keep
class MoreAboutYouData(
    var obj: MoreAboutYouObj?
)

@Keep
data class MoreAboutYouObj(
    var asic: AsicData? = null,
    var cima: CimaData? = null
)

@Keep
data class AsicData(
    var listEmploymentStatus: List<MoreAboutYouDetail>? = null,
    var listAnnualIncome: List<MoreAboutYouDetail>? = null,
    var listSavingsAndInvestments: List<MoreAboutYouDetail>? = null,
    var listSourceOfFunds: List<MoreAboutYouDetail>? = null,
    var listIndustry: List<MoreAboutYouDetail>? = null
)

@Keep
data class CimaData(
    var listAnnualIncome: MutableList<MoreAboutYouDetail>? = null,
    var listEmploymentStatus: MutableList<MoreAboutYouDetail>? = null,
    var listSavingsAndInvestments: MutableList<MoreAboutYouDetail>? = null,
    var listSourceOfFunds: MutableList<MoreAboutYouDetail>? = null,
    var listAccountIdType: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestDeposit: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestmentexpAmountTradew: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestmentexpDerivative: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestmentexpLevfx: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestmentexpSecurity: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestmentexpTradew: MutableList<MoreAboutYouDetail>? = null,
    var listAccountInvestmentexpVol: MutableList<MoreAboutYouDetail>? = null,
    var listAccountTitle: MutableList<MoreAboutYouDetail>? = null
)

@Keep
class MoreAboutYouDetail(
    var valueName: String? = null,
    var id: Int? = null,
    var displayName: String? = null

) : BaseListBean {
    override fun getShowItemValue(): String = displayName ?: ""
}

/**
 * 获取国家列表 -- 开户
 * Created by THINKPAD on 2018/10/9.
 */
@Keep
data class SelectNationalityBean(
    var data: SelectNationalityData? = null
) : BaseBean()

@Keep
data class SelectNationalityData(
    var obj: List<SelectNationalityObj>? = null
)

@Keep
data class SelectNationalityObj(
    var lettername: String? = null,
    var list: List<SelectNationalityObjDetail>? = null
) : Serializable

@Keep
data class SelectNationalityObjDetail(
    var id: String? = null, // "AU",
    var nationality: String? = null, //,"澳大利亚"
    var nationalityCn: String? = null, //,"Australia",
    var isShow: String? = null//"61"
) : Serializable

/**
 * 选择地区
 * Created by zhy on 2018/12/20.
 */
@Keep
data class ResidenceBean(
    var data: ResidenceData? = null
) : BaseBean()

@Keep
data class ResidenceData(
    var obj: List<ResidenceObj>? = null
)

@Keep
class ResidenceObj {
    @JvmField
    var lettername: String? = null

    @JvmField
    var list: List<ResidenceObjList>? = null
}

@Keep
class ResidenceObjList {
    //国家
    @JvmField
    var id: Int = 0
    var countryCode: String? = null

    @JvmField
    var countryNameEn: String? = null

    //省份
    @JvmField
    var provinceCode: String? = null

    @JvmField
    var provinceNameEn: String? = null
    var provinceNameCn: String? = null

    //城市
    @JvmField
    var cityCode: String? = null

    @JvmField
    var cityNameEn: String? = null
    var cityNameCn: String? = null
}

/**
 * 获取开户需要的数据，如性别 、 idType等
 */
@Keep
data class AccoSelectData(
    var data: AccoSelectDataObj? = null
) : BaseBean()

@Keep
data class AccoSelectDataObj(
    var obj: AccoSelectObj? = null
)

@Keep
data class AccoSelectObj(
    var accountGenderList: List<AccoSelectItem>? = null,
    var accountTitleList: List<AccoSelectItem>? = null,
    var accountIdTypeList: List<AccoSelectItem>? = null,
    var accountPoaTypeList: List<AccoSelectItem>? = null
)

@Keep
data class AccoSelectItem(
    var id: Int? = -1,
    var valueName: String? = null,
    var displayName: String? = null
)

/**
 * 验证邮箱是否符合要求 -- 开户
 */
@Keep
data class CheckEmailData(
    var data: CheckEmailDataObj? = null
) : BaseBean()

@Keep
data class CheckEmailDataObj(
    var obj: CheckEmailObj? = null
)

@Keep
data class CheckEmailObj(
    var emailStatus: Boolean? = null
)

/**
 * 获取交易平台的数据 如 mt4、mt5等
 */
@Keep
data class PlatformTypeTitleData(
    var data: PlatformTypeTitleDataObj? = null
) : BaseBean()

@Keep
data class PlatformTypeTitleDataObj(
    var obj: PlatformTypeObj? = null
)

@Keep
data class PlatformTypeObj(
    var mt4Title: String? = null,
    var mt4Content: String? = null,
    var mt5Title: String? = null,
    var mt5Content: String? = null,
    var mtsTitle: String? = null,
    var mtsContent: String? = null,
    var vtsTitle: String? = null,
    var vtsContent: String? = null,
)

/**
 * 获取开户的验证状态
 */
@Keep
data class AuditStatusData(
    var `data`: Data?
) : BaseBean() {
    @Keep
    data class Data(
        var obj: Obj?
    )

    @Keep
    data class Obj(
        // LV1审核状态 -1:NotSubmitted;0:Submitted;1:Completed;2:Rejected;3:Pending;4:Re-Audit;9:Rejected
        var accountAuditStatus: String?,
        var basicInfo: Int?,
        var ibtPoaAuditStatus: String?,
        var ibtPoiAuditStatus: String?,
        // POA审核状态 0：Submitted；1：Pending；2：Completed；3：Rejected；4：Re-Audit；
        var poaAuditStatus: String?,
        // Lv2 ID审核状态 0：Submitted；1：Pending；2：Completed；3：Rejected；4：Re-Audit；
        var poiAuditStatus: String?,
        var tradeAccount: Int?
    )
}

@Keep
data class AuthenticationStatusData(
    val obj: Obj? = null
) {
    @Keep
    data class Obj(
        val authenticationstatus: Int? = null,
    )
}

@Keep
data class KycVerifyLevelDataBean(
    val data: KycVerifyLevelData? = null
) : BaseBean()

@Keep
data class KycVerifyLevelData(
    val obj: KycVerifyLevelObj? = null
)

@Keep
data class KycVerifyLevelObj(
    var userId: String? = null,
    var kycConfigId: String? = null,    // 生效kyc 配置id
    var kycConfigCode: String? = null,  // 生效kyc 配置code 用于对接支付/活动/钱包
    var level: Int? = null,             // 用户当前等级，见含义下的枚举1-1
    var levelDesc: String? = null,
    var levelGuidedDocument: String? = null,
    var availableFunctions: List<String>? = null,   //用户拥有功能权限code集合（code含义见枚举1-2）
    var disable: Boolean? = null,       // 是否升级 true包含 false不包含
    var isMaxPrimaryLevel: Boolean? = null,     // 是否升级(审核成功等级) true包含 false不包含
    var i18nLevelDesc: String? = null,
    var i18nLevelGuidedDocument: String? = null,
    var guidance: KycVerifyLevelGuidance? = null    // 下一流程引导信息
)

@Keep
data class KycVerifyLevelGuidance(
    var level: String? = null,      // 下一流程等级level
    var levelDesc: String? = null,
    var levelGuidedDocument: String? = null,
    var i18nLevelDesc: String? = null,      // 下一流程等级描述
    var i18nLevelGuidedDocument: String? = null
)

@Keep
data class KycFunctionPermissionData(
    val obj: KycFunctionPermissionObj? = null
)

@Keep
data class KycFunctionPermissionObj(
    var userId: String? = null,
    var kycConfigId: String? = null,    // 生效kyc 配置id
    var kycConfigCode: String? = null,  // 生效kyc 配置code 用于对接支付/活动/钱包
    var hasPermission: Boolean? = null,  // 是否有该功能权限（判断是否有权限）
    var hasPermissionLevels: List<String>? = null,   // 拥有该功能权限的等级列表（主流程等级，独立验证流程等级）。如果没权限取集合中第一个做引导
    var guidance: Boolean? = null,  //
)

@Keep
data class KycGuidanceLevelData(
    val obj: KycGuidanceLevelObj? = null
)

@Keep
data class KycGuidanceLevelObj(
    val dependOnLevelList: List<GuidanceLevel?>? = null,
    val guidanceLevelList: List<GuidanceLevel?>? = null,
    val isGuidance: Boolean? = null,
    val kycConfigCode: String? = null,
    val kycConfigId: Int? = null,
    val nextLevel: Int? = null,
    val userId: Int? = null
)

@Keep
data class GuidanceLevel(
    val level: Int? = null,
    val levelDesc: String? = null,
    val levelGuidedDocument: String? = null,
    val levelStatus: Int? = null,
    val showLevelStatus: Int? = null,
    val verifyRelationList: List<VerifyRelation?>? = null
)

@Keep
data class KycAuditStatusData(
    val obj: KycAuditStatusObj? = null,
)

@Keep
data class KycAuditStatusObj(
    val independenceDependOnLevels: List<PrimaryDependOnLevel>? = listOf(),
    val kycConfigCode: String? = "",
    val kycConfigId: Int? = 0,
    val primaryDependOnLevels: List<PrimaryDependOnLevel>? = listOf(),
    val userId: Int? = 0
)

@Keep
data class CheckVirtualAccountBean(
    val data: CheckVirtualAccountData? = null
) : BaseBean()

@Keep
data class CheckVirtualAccountData(
    val obj: List<AccountTradeBean>? = null
)

@Keep
data class AccountAuditBean(
    val data: AccountAuditData? = null
) : BaseBean()

@Keep
data class AccountAuditData(
    val obj: AccountAuditObj? = null
)

@Keep
data class AccountAuditObj(
    val accountAuditStatus: String? = null,     // 账户审核状态： 0:处理中  1:审核通过  2:审核未通过  3:Pending  4:Pending后用户重新修改资料  9:重新开户  -1:未提交
    val isAppraisal: Boolean? = null
)

@Keep
data class PrimaryDependOnLevel(
    val functionTradingCodeList: List<Int>? = null,
    val functionWalletCodeList: List<Int>? = null,
    val isVerify: Boolean? = null,
    val level: Int? = null,
    val levelDesc: String? = null,
    val i18nLevelDesc: String? = null,
    val levelFunctionLimit: List<LevelFunctionLimitData?>? = null,
    val levelGuidedDocument: String? = null,
    val levelStatus: Int? = null,
    val showLevelStatus: Int? = null,
    val verifyModuleCodeList: List<Int>? = null,
    val verifyModuleNameList: List<String>? = null,
    val verifyRelationList: List<VerifyRelation>? = null
)

@Keep
data class LevelFunctionLimitData(
    val cryptoAmount: String? = null,
    val fiatAmount: String? = null,
    val kycConfigCode: Any? = null,
    val level: Any? = null,
    val limitAmount: String? = null, // 金额
    val limitType: Int? = null,  // 0 出金 1 入金
    val period: String? = null //限额类型
)

@Keep
data class VerifyRelation(
    val status: Int? = null,
    val userId: Int? = null,
    val verifyModuleCode: Int? = null
)

/**
 * 提交开户信息
 */
@Keep
data class SaveProcessData(
    var data: SaveProcessDataObj? = null
) : BaseBean()

@Keep
data class SaveProcessDataObj(var obj: SaveProcessObj? = null)

@Keep
data class SaveProcessObj(
    var userId: String? = null,
    var step: String? = null,
    var email: String? = null,
    var firstName: String? = null,
    var lastName: String? = null,
    var dob: String? = null,
    var tradingPlatform: String? = null,
    var accountType: String? = null,
    var currency: String? = null,
    var accountCreated: Boolean? = null,
    var accountReadOnly: Boolean? = null,
    var source: String? = null,
    var skipNextStep: Boolean? = null,
    var hideAccountType: Boolean? = null,
    var nationalityId: Int = -1,
    var countryId: Int = -1,

    //只有saveProcessID3接口使用到了这个参数
    var flag: Boolean? = null,

    val emailEventID: String?, //神策埋点绑定用户使用
)

/*
 * 获取活动和新手券 -- 开户成功
 */
@Keep
data class EventsTicketData(
    val `data`: Data?,
) : BaseBean() {

    @Keep
    data class Data(
        val obj: Obj?
    )

    @Keep
    data class Obj(
        val adsenseContents: List<AdsenseContent>?,
        // 优惠券id ( 判断是否有优惠券 )
        val couponId: String?,
        // 图片地址
        val rookieTicketAddress: String?,
        // 用户优惠券id
        var userCouponId: Int = -1,
        // 优惠文案
        var couponDes: String?,
        // 优惠券目标用户（1：代表所有， 2：代表新注册）
        var aimUser: Int?,
        // 优惠券类型（1：满减券，2：折扣券，3：现金券，4：信用券，5：抹亏券）
        var couponType: String?,
        // 剩余天数
        var remainDays: Int = 0,
        // TC页
        var infoUrl: String?,
        // 优惠金额描述，单一显示amount 币种符号，多币种，按美元显示$ amountUSD
        var amountDes: String?
    )

    @Keep
    data class AdsenseContent(
        val appJumpDefModel: PushBean?,
        val endTime: String?,
        val eventsStatus: Int?,
        val imgUrl: String?,
        val startTime: String?
    )

    @Keep
    data class Titles(
        val en: String?,
        val zh: String?
    )

}

/**
 * 文件上传
 * Created by THINKPAD on 2018/12/1.
 */
@Keep
data class UploadImageBean(
    var data: UploadFileData? = null
) : BaseBean()

@Keep
class UploadFileData(
    var obj: UploadFileObj? = null
)

@Keep
data class UploadFileObj(
    var imgFile: String? = null,
    var imgFileoos: String? = null
)

/**
 * ASIC问卷限制次数
 */
@Keep
data class AsicQuestionData(
    var obj: Obj? = null
) {
    @Keep
    data class Obj(
        val isFreeze: Int? = null,
        /**
         * 是否永久冻结: 1冻结，0未冻结
         */
        val isPermanentlyFrozen: Int? = null,
        val questionnaireNumber: Int? = null,
        /**
         * 能否继续答题状态
         *
         * true：可以
         *
         * false：不可以
         */
        val answer: Boolean? = false,
        /**
         * 限制答题提示语
         */
        val incorrectAnswerPrompt: String? = null,
        /**
         * 跳转链接
         */
        val jumpLink: String? = null
    )
}