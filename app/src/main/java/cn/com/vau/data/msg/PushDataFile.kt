package cn.com.vau.data.msg

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 * 公共跳转
 * Created by roy on 2018/3/15 0015.
 */
@Keep
@Parcelize
data class PushBean(
    var regulator: String? = null,
    var openType: String? = null,
    var viewType: String? = null,
    var urls: PushUrl? = null,
    var titles: PushTitle? = null,
    var title: String? = null,
    var param: PushParam? = null,
    var status: String = "-1",
    var accountStep: String? = null,
    var eventId: String? = null,
    var product: String? = null,
) : Parcelable

@Keep
@Parcelize
data class PushParam(
    var type: String? = null,
    var userid: String? = null,
    var auditStatus: String? = null,
    var targetUserId: String? = null,
    var newsId: String? = null,
    var talk: String? = null,
    var dataId: Int? = null,
    var parentid: String? = null,
    var trendId: String? = null,     // 心情观点 0
    var start: String? = null,
    var pagesize: String? = null,
    var replyid: String? = null,
    var token: String? = null,
    var zone: String? = null,
    var state: String? = null,
    var userId: String? = null,
    var txnSta: String? = null,
    var mt4: String? = null,
    var inviteStatus: String? = null,
    var ibStatus: String? = null,

    var id: String? = null,
    var url: String? = null,
    var productCode: String? = null,   //产品详情 "ABCDEF"
    var product: String? = null,   //产品详情 "ABCDEF"
    var asicNonPro: Int = 0,
    var recordId: String? = null,
    var ruleId: String? = null,
    var pushId: String? = null,

    var playbackUrl: String? = null,
    var arnUrl: String? = null,
    var messageNode: String? = null,
    val accountId: String? = null,
    val orderCode: String? = null,
    // 跳转对应策略的 分润页面 需要的 参数
    val strategyId: String? = null,
    // 跳转对应日期的分润页面需要的参数
    val settlementDate: String? = null,
    var isAllowClosePage: Boolean = true
) : Parcelable

// TODO
@Keep
@Parcelize
data class PushTitle(
    var zh: String? = null,
    var en: String? = null,
    var ja: String? = null,
    var th: String? = null
) : Parcelable

// TODO
@Keep
@Parcelize
data class PushUrl(
    var def: String?,
    var zh: String? = null,
    var en: String? = null,
    var th: String? = null,
    var ja: String? = null,
    var fr: String? = null
) : Parcelable