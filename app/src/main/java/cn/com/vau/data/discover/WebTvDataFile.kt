package cn.com.vau.data.discover

import android.os.Parcelable
import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
class WebTVData(
    var obj: List<WebTVObj>?
) : Parcelable

@Keep
@Parcelize
class WebTVObj(
    // 视频id
    var videoId: String? = "",
    // 封面
    var cover: String? = "",
    // 视频名称
    var videoName: String? = "",
    // 描述
    var description: String? = "",
    // 播放量
    var views: String = "0",
    // 播放地址
    var url: String? = "",
    // 创建日期（日/月/年）
    var createTime: String? = "",
    // 时间戳，翻页使用
    var date: String = "0",
    //添加参数 播放状态
    var isPlaying: Boolean = false
) : Parcelable