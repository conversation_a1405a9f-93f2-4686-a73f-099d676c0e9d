package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewStub
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.AccountOpeningGuideObj
import cn.com.vau.data.enums.EnumLoginStatus
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.util.AttrResourceUtil

class TradeGuideView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mViewStubNotLogin: ViewStub? = null
    private var mViewStubGuide: ViewStub? = null
    private var mViewStubNotDeposit: ViewStub? = null
    private var mViewStubLoggedIn: ViewStub? = null
    private var mViewStubVirtualAccount: ViewStub? = null

    private var onRegisterCallback: (() -> Unit)? = null
    private var onLoginCallback: (() -> Unit)? = null
    private var onGoLiveCallback: (() -> Unit)? = null
    private var onDepositCallback: (() -> Unit)? = null
    private var onMarginLevelClick: (() -> Unit)? = null
    private var onSetupCallback: (() -> Unit)? = null

    private var mAccountNotLoginView: AccountNotLoginView? = null
    private var mAccountGuideView: AccountGuideView? = null
    private var mAccountNotDepositView: AccountNotDepositView? = null
    private var mAccountLoggedInView: AccountLoggedInView? = null
    private var mAccountVirtualMT5View: AccountVirtualMT5View? = null

    private val ce35728 by lazy { ContextCompat.getColor(context, R.color.ce35728) }
    private val c00c79c by lazy { ContextCompat.getColor(context, R.color.c00c79c) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }

    init {
        initView()
    }

    private fun initView() {
        inflate(context, R.layout.layout_trade_guide, this)
        mViewStubNotLogin = findViewById<ViewStub>(R.id.viewStubNotLogin)
        mViewStubGuide = findViewById<ViewStub>(R.id.viewStubAccountGuide)
        mViewStubNotDeposit = findViewById<ViewStub>(R.id.viewStubNotDeposit)
        mViewStubLoggedIn = findViewById<ViewStub>(R.id.viewStubLoggedInAccount)
        mViewStubVirtualAccount = findViewById<ViewStub>(R.id.viewStubVirtual)

        mViewStubNotLogin?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountNotLoginView = inflated as? AccountNotLoginView
                mAccountNotLoginView?.setOnRegisterCallback(onRegisterCallback)
                mAccountNotLoginView?.setOnLoginCallback(onLoginCallback)
            }
        })

        mViewStubGuide?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountGuideView = inflated as? AccountGuideView
                mAccountGuideView?.setOnGoLiveCallback(onGoLiveCallback)
            }
        })

        mViewStubNotDeposit?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountNotDepositView = inflated as? AccountNotDepositView
                mAccountNotDepositView?.setOnDepositCallback(onDepositCallback)
            }
        })

        mViewStubLoggedIn?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountLoggedInView = inflated as? AccountLoggedInView
                mAccountLoggedInView?.setOnMarginLevelClick(onMarginLevelClick)
            }
        })

        mViewStubVirtualAccount?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountVirtualMT5View = inflated as? AccountVirtualMT5View
                mAccountVirtualMT5View?.setOnVirtualSetupCallback(onSetupCallback)
            }
        })
    }

    fun setAccountState(
        state: EnumLoginStatus?,
        guideObj: AccountOpeningGuideObj?,
        isDeposited: Boolean,
        shareAccountBean: ShareAccountInfoData,
        accountAuditStatus: String,
    ): TradeGuideView {
        val isKYCProcess = SpManager.isV1V2()
        when (state) {
            // 已登录Live虚拟MT5账户
            EnumLoginStatus.LOGIN_VIRTUAL_ACCOUNT -> {
                mViewStubNotLogin?.isVisible = false   // 未登录隐藏
                mViewStubGuide?.isVisible = false    // 开户引导隐藏
                mViewStubNotDeposit?.isVisible = false  // 未入金隐藏
                mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                mViewStubVirtualAccount?.isVisible = true    // 虚拟MT5账户显示
            }

            // 已登录模拟账户
            EnumLoginStatus.LOGIN_DEMO -> {
                mViewStubNotLogin?.isVisible = false   // 未登录隐藏
                mViewStubVirtualAccount?.isVisible = false    // 虚拟MT5账户隐藏
                mViewStubNotDeposit?.isVisible = false  // 未入金隐藏（Demo不显示）
                // KYC流程
                if (isKYCProcess) {
                    if (accountAuditStatus == "4"   // 【Pending后用户重新修改资料】
                        || accountAuditStatus == "9"   // 【重新开户】
                        || accountAuditStatus == "-1"   // 【未提交】
                    ) {
                        // 展示GoLive按钮
                        mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                        mViewStubGuide?.isVisible = true    // 开户引导显示
                        mAccountGuideView?.setKycDemoStatus()
                        // 开户引导布局-账户信息
                        refreshOpenGuideBoard(getEnableColor(shareAccountBean), shareAccountBean)
                    } else {
                        // 其余状态显示已登录 展示资产卡片
//                        accountAuditStatus == "0"   // 【审核中】
//                        accountAuditStatus == "1"   // 【已通过】
//                        accountAuditStatus == "2"   // 【未通过/被拒】
//                        accountAuditStatus == "3"   // 【Pending】
                        mViewStubLoggedIn?.isVisible = true    // 已登录显示
                        mViewStubGuide?.isVisible = false    // 开户引导隐藏
                        // 已登录布局
                        refreshLoggedInBoard(getEnableColor(shareAccountBean), shareAccountBean)
                    }
                } else {
                    // 开户引导
                    when {
                        // 未完全开户         || 開戶資料有誤          || 身份证明有误          || ???
                        1 == guideObj?.type || 3 == guideObj?.type || 4 == guideObj?.type || (2 == guideObj?.type) && (guideObj.step.equals("0")) -> {
                            mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                            mViewStubGuide?.isVisible = true    // 开户引导显示
                            mAccountGuideView?.setNormalDemoStatus(guideObj)
                            // 开户引导布局-账户信息
                            refreshOpenGuideBoard(getEnableColor(shareAccountBean), shareAccountBean)
                        }
                        // 没有开户问题，直接显示已登录布局
                        else -> {
                            // 已登录
                            mViewStubLoggedIn?.isVisible = true    // 已登录显示
                            mViewStubGuide?.isVisible = false    // 开户引导隐藏
                            // 已登录布局
                            refreshLoggedInBoard(getEnableColor(shareAccountBean), shareAccountBean)
                        }
                    }
                }
            }

            // 已登录真实账户
            EnumLoginStatus.LOGIN_LIVE -> {
                mViewStubNotLogin?.isVisible = false   // 未登录隐藏
                mViewStubVirtualAccount?.isVisible = false    // 虚拟MT5账户隐藏
                // KYC流程
                if (isKYCProcess) {
                    // 未入金
                    if (isDeposited.not()) {
                        mViewStubNotDeposit?.isVisible = true    // 未入金显示
                        mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                        mViewStubGuide?.isVisible = false    // 开户引导隐藏
                        // 未入金布局
                        refreshNotDepositEquity(shareAccountBean)
                    } else {
                        // 已登录
                        mViewStubLoggedIn?.isVisible = true    // 已登录显示
                        mViewStubNotDeposit?.isVisible = false    // 未入金隐藏
                        mViewStubGuide?.isVisible = false    // 开户引导隐藏
                        // 已登录布局
                        refreshLoggedInBoard(getEnableColor(shareAccountBean), shareAccountBean)
                    }
                } else {
                    // 开户引导
                    when {
                        // 未完全开户         || 開戶資料有誤          || 身份证明有误          || ???
                        1 == guideObj?.type || 3 == guideObj?.type || 4 == guideObj?.type || (2 == guideObj?.type) && (guideObj.step.equals("0")) -> {
                            mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                            mViewStubNotDeposit?.isVisible = false    // 未入金隐藏
                            mViewStubGuide?.isVisible = true    // 开户引导显示
                            mAccountGuideView?.setLiveStatus(guideObj)
                            // 开户引导布局-账户信息
                            refreshOpenGuideBoard(getEnableColor(shareAccountBean), shareAccountBean)
                        }
                        // 未入金
                        isDeposited.not() -> {
                            mViewStubNotDeposit?.isVisible = true    // 未入金显示
                            mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                            mViewStubGuide?.isVisible = false    // 开户引导隐藏
                            // 未入金布局
                            refreshNotDepositEquity(shareAccountBean)
                        }
                        // 已登录
                        else -> {
                            mViewStubLoggedIn?.isVisible = true    // 已登录显示
                            mViewStubNotDeposit?.isVisible = false    // 未入金隐藏
                            mViewStubGuide?.isVisible = false    // 开户引导隐藏
                            // 已登录布局
                            refreshLoggedInBoard(getEnableColor(shareAccountBean), shareAccountBean)
                        }
                    }
                }
            }

            else -> {
                // 未登录:
                // 从来没有登录过 / 之前登录过
                mViewStubNotLogin?.isVisible = true   // 未登录显示
                mViewStubGuide?.isVisible = false    // 开户引导隐藏
                mViewStubNotDeposit?.isVisible = false  // 未入金隐藏
                mViewStubLoggedIn?.isVisible = false    // 已登录隐藏
                mViewStubVirtualAccount?.isVisible = false    // 虚拟MT5账户隐藏
                mAccountNotLoginView?.setStatus(state)
            }
        }
        return this
    }

    // 刷新未入金布局
    fun refreshNotDepositEquity(shareAccountBean: ShareAccountInfoData) {
        // 显示才去刷新
        mAccountNotDepositView?.let {
            if (it.isVisible) {
                it.refreshNotDepositEquity(shareAccountBean)
            }
        }
    }

    // 刷新已登录布局
    fun refreshLoggedInBoard(enableColorRes: Int, shareAccountBean: ShareAccountInfoData) {
        // 显示才去刷新
        mAccountLoggedInView?.let {
            if (it.isVisible) {
                it.refreshLoggedInBoard(enableColorRes, shareAccountBean)
            }
        }
    }

    // 刷新引导开户布局
    fun refreshOpenGuideBoard(enableColorRes: Int, shareAccountBean: ShareAccountInfoData) {
        // 显示才去刷新
        mAccountGuideView?.let {
            if (it.isVisible) {
                it.refreshOpenGuideBoard(enableColorRes, shareAccountBean)
            }
        }
    }

    fun showBannerEffectLoggedInLayout(bannerVisible: Boolean) {
        // 显示才去刷新
        mAccountLoggedInView?.let {
            if (it.isVisible) {
                it.showBannerEffect(bannerVisible)
            }
        }
    }

    fun refreshVirtualBoard(shareAccountBean: ShareAccountInfoData) {
        // 显示才去刷新
        mAccountVirtualMT5View?.let {
            if (it.isVisible) {
                it.refreshVirtualEquity(shareAccountBean)
            }
        }
    }

    // App切后台超过1分钟的显示
    fun appInBackgroundMoreThan1m() {
        // 已登录布局
        if (mAccountLoggedInView?.isVisible == true) {
            mAccountLoggedInView?.appInBackgroundMoreThan1m()
        }
        // 未入金布局
        if (mAccountNotDepositView?.isVisible == true) {
            mAccountNotDepositView?.appInBackgroundMoreThan1m()
        }
        // 开户引导布局
        if (mAccountGuideView?.isVisible == true) {
            mAccountGuideView?.appInBackgroundMoreThan1m()
        }
        // 虚拟MT5布局
        if (mAccountVirtualMT5View?.isVisible == true) {
            mAccountVirtualMT5View?.appInBackgroundMoreThan1m()
        }
    }

    // 注册回调
    fun setOnRegisterCallback(callback: (() -> Unit)?): TradeGuideView {
        onRegisterCallback = callback
        return this
    }

    // 登录回调
    fun setOnLoginCallback(callback: (() -> Unit)?): TradeGuideView {
        onLoginCallback = callback
        return this
    }

    // 开通真实账户回调
    fun setOnGoLiveCallback(callback: (() -> Unit)?): TradeGuideView {
        onGoLiveCallback = callback
        return this
    }

    // 入金回调
    fun setOnDepositCallback(callback: (() -> Unit)?): TradeGuideView {
        onDepositCallback = callback
        return this
    }

    // 点击预付款比
    fun setOnMarginLevelClick(callback: (() -> Unit)?): TradeGuideView {
        onMarginLevelClick = callback
        return this
    }

    // 点击补全虚拟MT5账户
    fun setOnSetupCallback(callback: (() -> Unit)?): TradeGuideView {
        onSetupCallback = callback
        return this
    }

    private fun getEnableColor(shareAccountBean: ShareAccountInfoData): Int {
        return if (shareAccountBean.profit < 0) ce35728
        else if (shareAccountBean.profit > 0) c00c79c
        else color_c1e1e1e_cebffffff
    }

}