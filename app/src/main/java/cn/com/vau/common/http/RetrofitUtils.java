package cn.com.vau.common.http;

import cn.com.vau.util.GsonUtil;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * <AUTHOR>
 */
public class RetrofitUtils {

    /**
     * 获取Retrofit.Builder单例对象
     *
     * @return
     */
    public static Retrofit.Builder getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private static class SingletonHolder {
        /**
         * Retrofit.Builder单例对象
         */
        private static final Retrofit.Builder INSTANCE = new Retrofit.Builder()
                .client(OkHttpUtils.getClient())
                .addConverterFactory(GsonConverterFactory.create(GsonUtil.INSTANCE.buildGson()))
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create());
    }
}
