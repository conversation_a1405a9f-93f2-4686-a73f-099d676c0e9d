package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.ui.common.DateEntity
import cn.com.vau.util.AttrResourceUtil

/**
 * Created by roy on 2018/10/25.
 * 历史订单 -- 日期选择
 */
class PopHistoryOrderCalendarItemRcyAdapter(
    var mContext: Context,
    var dataList: ArrayList<DateEntity>?
) : RecyclerView.Adapter<PopHistoryOrderCalendarItemRcyAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(
            LayoutInflater.from(mContext)
                .inflate(R.layout.item_recycler_calendar_item, parent, false)
        )
        holder.itemView.setOnClickListener {
            val data = dataList?.elementAtOrNull(holder.layoutPosition)
            if (data?.type == -2) { // 这里的-2对应的是DateEntity第一个构造器的默认值，如修改默认值这里也需要修改
                return@setOnClickListener
            }
            mOnItemClickListener?.onItemClick(holder.adapterPosition)
        }
        return holder
    }

    @RequiresApi(Build.VERSION_CODES.M)
    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindTo(mContext, dataList?.elementAtOrNull(position))
    }

    override fun getItemCount(): Int {
        return dataList?.size ?: 0
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        @RequiresApi(Build.VERSION_CODES.M)
        @SuppressLint("UseCompatLoadingForDrawables")
        fun bindTo(mContext: Context, dataBean: DateEntity?) {

            dataBean?.run {

                val tvDate = itemView.findViewById<TextView>(R.id.tvDate)
                tvDate.visibility =
                    if (dataBean.type == -1) View.INVISIBLE else View.VISIBLE

                tvDate.setTextAppearance(
                    if (dataBean.type == -2 || dataBean.type == 2) R.style.bold_semi_font else R.style.regular_font
                )

                tvDate.setTextColor(
                    if (dataBean.type == 2)
                        ContextCompat.getColor(mContext, R.color.cffffff)
                    else
                        AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff)
                )

                tvDate.setBackgroundResource(
                    when (dataBean.type) {
                        2 -> R.drawable.shape_ce35728_r100
                        3 -> R.drawable.shape_c1fe35728_r100
                        else -> R.color.transparent
                    }
                )
                tvDate.text = dataBean.dateContent
            }

        }

    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}