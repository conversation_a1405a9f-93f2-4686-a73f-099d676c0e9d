package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.R
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.CommunityFilterGridDivider
import cn.com.vau.common.view.popup.adapter.StCommunityFilterAdapter
import cn.com.vau.common.view.popup.adapter.StCommunityFilterBean
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.DialogBottomCommunityFilterBinding
import cn.com.vau.util.ScreenUtil
import cn.com.vau.util.arabicText
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.BottomInfoListDialog
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import org.json.JSONObject

/**
 * Filename: StCommunityFilterPopup.kt
 * Author: GG
 * Date: 2024/3/23
 * Description:
 */
@SuppressLint("ViewConstructor")
class BottomCommunityFilterDialog private constructor(activity: Activity) : BottomDialog<DialogBottomCommunityFilterBinding>(activity, DialogBottomCommunityFilterBinding::inflate) {

    private val filterAdapter: StCommunityFilterAdapter by lazy {
        StCommunityFilterAdapter().apply {
            setNewInstance(
                arrayListOf(
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_TITLE, data = activity.getString(R.string.time_period)),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.last_x_months, "1"), requestData = "1", tag = "1"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.last_x_months, "3"), requestData = "3", tag = "1", isSelected = true),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.last_x_months, "6"), requestData = "6", tag = "1"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.last_x_months, "12"), requestData = "12", tag = "1"),

                    StCommunityFilterBean(StCommunityFilterBean.TYPE_TITLE, data = activity.getString(R.string.rating), isHasMorePopup = true),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">95".arabicText().ifNull(), requestData = "1", tag = "2"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">85".arabicText().ifNull(), requestData = "2", tag = "2"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">60".arabicText().ifNull(), requestData = "3", tag = "2"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.all), requestData = "0", tag = "2", isSelected = true),

                    StCommunityFilterBean(StCommunityFilterBean.TYPE_TITLE, data = activity.getString(R.string.signal_filter_detail_return)),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">0%".arabicText().ifNull(), requestData = "0", tag = "3"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">50%".arabicText().ifNull(), requestData = "0.5", tag = "3"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">100%".arabicText().ifNull(), requestData = "1", tag = "3"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">500%".arabicText().ifNull(), requestData = "5", tag = "3"),

                    StCommunityFilterBean(StCommunityFilterBean.TYPE_TITLE, data = activity.getString(R.string.risk_band), isHasMorePopup = true),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = "<=2".arabicText().ifNull(), requestData = "2", tag = "4"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = "<=6".arabicText().ifNull(), requestData = "6", tag = "4"),

                    StCommunityFilterBean(StCommunityFilterBean.TYPE_TITLE, data = activity.getString(R.string.win_rate)),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">70%".arabicText().ifNull(), requestData = "0.7", tag = "5"),
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = ">50%".arabicText().ifNull(), requestData = "0.5", tag = "5"),

                    //数据动态添加
                    StCommunityFilterBean(StCommunityFilterBean.TYPE_TITLE, data = activity.getString(R.string.trading_category)),
                )
            )
        }
    }

    private val ratingPopup by lazy {
        BottomInfoListDialog.Builder(activity)
            .setDataList(arrayListOf(HintLocalData(title = activity.getString(R.string.rating), content = activity.getString(R.string.the_risk_is_the_win_return_number_risk_and_a_the_strategy))))
            .build()
    }

    private val riskBandPopup by lazy {
        BottomInfoListDialog.Builder(activity)
            .setDataList(arrayListOf(HintLocalData(title = activity.getString(R.string.risk_band), content = activity.getString(R.string.the_risk_band_the_the_here_date_status))))
            .build()
    }

    private var dataTimeBean: StCommunityFilterBean? = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.last_x_months, "3"), requestData = "3")
    private var dataRatingBean: StCommunityFilterBean? = StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = activity.getString(R.string.all), requestData = "0", tag = "2")
    private var dataReturnBean: StCommunityFilterBean? = null
    private var dataRiskBandBean: StCommunityFilterBean? = null
    private var dataWinRateBean: StCommunityFilterBean? = null
    private var dataTradingBean: StCommunityFilterBean? = null

    private var confirmClick: ((StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?) -> Unit)? = null

    override fun setContentView() {
        mContentBinding.apply {

            tvRightButton.setOnClickListener {
                clearSelect()
            }

            tvButton.setOnClickListener {
                confirmClick?.invoke(dataTimeBean, dataRatingBean, dataReturnBean, dataRiskBandBean, dataWinRateBean, dataTradingBean)
                sensorsTrack()
                dismiss()
            }

            val gridLayoutManager = GridLayoutManager(activity, 2)
            gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (filterAdapter.getItemViewType(position)) {
                        StCommunityFilterBean.TYPE_TITLE -> gridLayoutManager.spanCount
                        else -> 1
                    }
                }
            }
            val layoutParams = rvList.layoutParams as MarginLayoutParams
            layoutParams.height = (ScreenUtil.screenHeight * 3.5 / 5).toInt()
            rvList.layoutParams = layoutParams
            rvList.layoutManager = gridLayoutManager
            rvList.adapter = filterAdapter
            rvList.itemAnimator = null
            rvList.addItemDecoration(CommunityFilterGridDivider(12.dp2px()))

            filterAdapter.setOnItemClickListener { _, _, position ->
                when (filterAdapter.getItemViewType(position)) {
                    StCommunityFilterBean.TYPE_TITLE -> {
                        when (filterAdapter.data.getOrNull(position)?.data) {
                            activity.getString(R.string.rating) -> {
                                ratingPopup.show()
                            }

                            activity.getString(R.string.risk_band) -> {
                                riskBandPopup.show()
                            }
                        }
                    }

                    StCommunityFilterBean.TYPE_DETAIL -> {
                        filterAdapter.data.getOrNull(position)?.let {
                            when (position) {
                                in START_INDEX_TIME..START_INDEX_TIME + COUNT_TIME - 1 -> {
                                    updateTime(it)
                                }

                                in START_INDEX_RATING..START_INDEX_RATING + COUNT_RATING - 1 -> {
                                    updateRating(it)
                                }

                                in START_INDEX_RETURN..START_INDEX_RETURN + COUNT_RETURN - 1 -> {
                                    updateReturn(if (it.data == dataReturnBean?.data) null else it)
                                }

                                in START_INDEX_RISKBAND..START_INDEX_RISKBAND + COUNT_RISKBAND - 1 -> {
                                    updateRiskBand(if (it.data == dataRiskBandBean?.data) null else it)
                                }

                                in START_INDEX_WINRATE..START_INDEX_WINRATE + COUNT_WINRATE - 1 -> {
                                    updateWinRate(if (it.data == dataWinRateBean?.data) null else it)
                                }

                                in START_INDEX_TRADING..<filterAdapter.data.size -> {
                                    updateTrading(if (it.data == dataTradingBean?.data) null else it)
                                }
                            }
                        }
                    }
                }
            }

        }
    }

    /**
     * 更新时间选项样式
     */
    private fun updateTime(item: StCommunityFilterBean?) {
        if (item?.data != dataTimeBean?.data) {
            dataTimeBean = item
            filterAdapter.data.subList(START_INDEX_TIME, START_INDEX_TIME + COUNT_TIME).forEach {
                it.isSelected = it.data == item?.data
            }
            filterAdapter.notifyItemRangeChanged(START_INDEX_TIME, COUNT_TIME)
        }
    }

    /**
     * 更新评分筛选项样式
     */
    private fun updateRating(item: StCommunityFilterBean?) {
        if (item?.data != dataRatingBean?.data) {
            dataRatingBean = item
            filterAdapter.data.subList(START_INDEX_RATING, START_INDEX_RATING + COUNT_RATING).forEach {
                it.isSelected = it.data == item?.data
            }
            filterAdapter.notifyItemRangeChanged(START_INDEX_RATING, COUNT_RATING)
        }
    }

    /**
     * 更新回报率筛选项样式
     */
    private fun updateReturn(item: StCommunityFilterBean?) {
        if (item?.data != dataReturnBean?.data) {
            dataReturnBean = item
            filterAdapter.data.subList(START_INDEX_RETURN, START_INDEX_RETURN + COUNT_RETURN).forEach {
                it.isSelected = it.data == item?.data
            }
            filterAdapter.notifyItemRangeChanged(START_INDEX_RETURN, COUNT_RETURN)
        }
    }

    /**
     * 更新RiskBand筛选项样式
     */
    private fun updateRiskBand(item: StCommunityFilterBean?) {
        if (item?.data != dataRiskBandBean?.data) {
            dataRiskBandBean = item
            filterAdapter.data.subList(START_INDEX_RISKBAND, START_INDEX_RISKBAND + COUNT_RISKBAND).forEach {
                it.isSelected = it.data == item?.data
            }
            filterAdapter.notifyItemRangeChanged(START_INDEX_RISKBAND, COUNT_RISKBAND)
        }
    }

    /**
     * 更新WinRate筛选项样式
     */
    private fun updateWinRate(item: StCommunityFilterBean?) {
        if (item?.data != dataWinRateBean?.data) {
            dataWinRateBean = item
            filterAdapter.data.subList(START_INDEX_WINRATE, START_INDEX_WINRATE + COUNT_WINRATE).forEach {
                it.isSelected = it.data == item?.data
            }
            filterAdapter.notifyItemRangeChanged(START_INDEX_WINRATE, COUNT_WINRATE)
        }
    }

    /**
     * 更新产品类型样式
     */
    private fun updateTrading(item: StCommunityFilterBean?) {
        if (item?.data != dataTradingBean?.data) {
            dataTradingBean = item
            filterAdapter.data.subList(START_INDEX_TRADING, filterAdapter.data.size).forEach {
                it.isSelected = it.data == item?.data
            }
            filterAdapter.notifyItemRangeChanged(START_INDEX_TRADING, filterAdapter.data.size - START_INDEX_TRADING)
        }
    }

    /**
     * 清除选中的选项，恢复默认
     */
    private fun clearSelect() {
        filterAdapter.data.forEach {
            it.isSelected = false
        }
        filterAdapter.data.getOrNull(2)?.isSelected = true
        filterAdapter.data.getOrNull(9)?.isSelected = true
        this.dataTimeBean = filterAdapter.data.getOrNull(2)
        this.dataRatingBean = filterAdapter.data.getOrNull(9)
        this.dataReturnBean = null
        this.dataRiskBandBean = null
        this.dataWinRateBean = null
        this.dataTradingBean = null
        filterAdapter.notifyItemRangeChanged(0, filterAdapter.itemCount)
    }

    /**
     * 外部选项传入
     */
    fun setData(
        dataTimeBean: StCommunityFilterBean?,
        dataRatingBean: StCommunityFilterBean? = null,
        dataReturnBean: StCommunityFilterBean? = null,
        dataRiskBandBean: StCommunityFilterBean? = null,
        dataWinRateBean: StCommunityFilterBean? = null,
        dataTradingBean: StCommunityFilterBean? = null
    ) {
        updateTime(dataTimeBean)
        updateRating(dataRatingBean)
        updateReturn(dataReturnBean)
        updateRiskBand(dataRiskBandBean)
        updateWinRate(dataWinRateBean)
        updateTrading(dataTradingBean)
    }

    fun confirmCallback(confirmClick: ((StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?, StCommunityFilterBean?) -> Unit)? = null) {
        this.confirmClick = confirmClick
    }

    /**
     * 根据用户能看到的产品组 进行筛选项的填充
     */
    fun updateTradingInfo(groupNameList: List<String?>) {
        //如果元素不相同就返回false 并重新填充
        val lastData = filterAdapter.data.takeLast(filterAdapter.data.size - START_INDEX_TRADING)
        if (groupNameList.size != lastData.size || !groupNameList.zip(lastData).all { (element1, element2) -> element1 == element2.requestData }) {
            val data = filterAdapter.data.take(START_INDEX_TRADING).toMutableList()
            data.addAll(groupNameList.map { StCommunityFilterBean(StCommunityFilterBean.TYPE_DETAIL, data = VAUSdkUtil.getGroupNameLanguage(activity, it.ifNull()), requestData = it.ifNull(), tag = "6") })
            filterAdapter.setNewInstance(data)
        }
    }

    /**
     * 神策自定义埋点(v3710)
     * App_发现页面点击 -> 多维筛选项
     */
    private fun sensorsTrack() {
        val properties = JSONObject()
        dataTimeBean?.let {
            properties.put(
                "time_period_filter", when (it.requestData) {
                    "1" -> "Last 1 Month"
                    "3" -> "Last 3 Months"
                    "6" -> "Last 6 Months"
                    "12" -> "Last 12 Months"
                    else -> ""
                }
            )
        }

        dataRatingBean?.let {
            properties.put(
                "rating_filter", when (it.requestData) {
                    "1" -> ">95"
                    "2" -> ">85"
                    "3" -> ">60"
                    "0" -> "All"
                    else -> ""
                }
            )
        }

        dataReturnBean?.let {
            properties.put(
                "return_filter", when (it.requestData) {
                    "0" -> ">0%"
                    "0.5" -> ">50%"
                    "1" -> ">100%"
                    "5" -> ">500%"
                    else -> ""
                }
            )
        }

        dataRiskBandBean?.let {
            properties.put(
                "risk_band_filter", when (it.requestData) {
                    "2" -> "<=2"
                    "6" -> "<=6"
                    else -> ""
                }
            )
        }
        dataWinRateBean?.let {
            properties.put(
                "win_rate_filter", when (it.requestData) {
                    "0.7" -> ">70%"
                    "0.5" -> ">50%"
                    else -> ""
                }
            )
        }
        dataTradingBean?.let {
            properties.put("trading_category_filter", it.requestData)
        }

        SensorsDataUtil.track(SensorsConstant.V3710.COPY_TRADING_DISCOVER_FILTER_CONFIRM_CLICK, properties)
    }

    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomCommunityFilterBinding, Builder>(activity) {

        override fun build(): BottomCommunityFilterDialog {
            return super.build() as BottomCommunityFilterDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomCommunityFilterBinding> {
            return BottomCommunityFilterDialog(activity)
        }
    }

    companion object {
        private const val START_INDEX_TIME = 1
        private const val START_INDEX_RATING = 6
        private const val START_INDEX_RETURN = 11
        private const val START_INDEX_RISKBAND = 16
        private const val START_INDEX_WINRATE = 19
        private const val START_INDEX_TRADING = 22

        private const val COUNT_TIME = 4
        private const val COUNT_RATING = 4
        private const val COUNT_RETURN = 4
        private const val COUNT_RISKBAND = 2
        private const val COUNT_WINRATE = 2
    }
}