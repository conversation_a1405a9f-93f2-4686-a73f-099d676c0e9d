package cn.com.vau.common.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.util.widget.FirebaseManager;

/**
 * Created by roy on 2018/7/9.
 */

public class WrapContentLinearLayoutManager extends LinearLayoutManager {

    public void setFrom(String from) {
        this.from = from;
    }

    private String from = "";
    public WrapContentLinearLayoutManager(Context context) {
        super(context);
    }

    public WrapContentLinearLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public WrapContentLinearLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            super.onLayoutChildren(recycler, state);
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
            FirebaseManager.INSTANCE.recordException(new IndexOutOfBoundsException("WrapContentLinearLayoutManager#onLayoutChildren"+from));
        }
    }

}
