package cn.com.vau.common.adapter;

import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

public class DataBoundViewHolder<T extends ViewDataBinding>
        extends RecyclerView.ViewHolder {
    private T databinding;

    public DataBoundViewHolder(T vdb) {
        super(vdb.getRoot());
        databinding = vdb;
    }

    public T getDatabinding() {
        return databinding;
    }
}
