package cn.com.vau.common.view.kchart.toolbar

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.util.AttrResourceUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.myapplication.R
import com.example.myapplication.databinding.KlineLinePickerViewBinding

/**
 * 线样式选择器
 */
class LinePickerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding by lazy { KlineLinePickerViewBinding.inflate(LayoutInflater.from(context), this, true) }

    private var onItemListener: ((item: LineStyleBean) -> Unit)? = null
    var mAdapter = LineStyleAdapter()

    init {
        setupRecyclerView()
    }

    private fun setupRecyclerView() {

        binding.rvList.layoutManager = object : LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false) {
            override fun canScrollVertically(): Boolean = false // 禁用垂直滚动
            override fun canScrollHorizontally(): Boolean = false // 禁用水平滚动
        }
        binding.rvList.isNestedScrollingEnabled = false
        binding.rvList.setHasFixedSize(true)
        binding.rvList.itemAnimator = null // 禁用默认动画
        binding.rvList.layoutAnimation = null // 禁用默认动画
        binding.rvList.adapter = mAdapter

        mAdapter.setOnItemClickListener { adapter, view, position ->
            val item = mAdapter.getItem(position) ?: return@setOnItemClickListener
            onItemListener?.invoke(item)
        }
    }

    fun setOnItemListener(listener: (item: LineStyleBean) -> Unit) {
        onItemListener = listener
    }

    // 批量添加工具项
    fun setLineStyleBeans(items: List<LineStyleBean>) {
        mAdapter.setList(items)
    }
}

class LineStyleAdapter : BaseQuickAdapter<LineStyleBean, BaseViewHolder>(R.layout.kline_item_line_picker) {
    override fun convert(holder: BaseViewHolder, item: LineStyleBean) {
        holder.setImageResource(R.id.ivLine, AttrResourceUtil.getDrawable(context, item.icon))
    }

}

