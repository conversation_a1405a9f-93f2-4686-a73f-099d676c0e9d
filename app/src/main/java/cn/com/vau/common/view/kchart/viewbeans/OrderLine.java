package cn.com.vau.common.view.kchart.viewbeans;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.TextUtils;

import androidx.core.content.res.ResourcesCompat;

import cn.com.vau.R;
import cn.com.vau.common.greendao.dbUtils.UserDataUtil;
import cn.com.vau.common.utils.OrderUtil;
import cn.com.vau.common.view.kchart.interfaces.AboveCoordinatesView;
import cn.com.vau.common.view.kchart.interfaces.UnabelFocusedsView;
import cn.com.vau.data.init.ShareOrderData;
import cn.com.vau.trade.kchart.ChartUIParamUtil;
import cn.com.vau.trade.kchart.KLineDataUtils;
import cn.com.vau.util.AppUtil;
import cn.com.vau.util.DistKt;
import cn.com.vau.util.ExpandKt;
import cn.com.vau.util.ScreenUtil;
import cn.com.vau.util.language.MultiLanguages;

public class OrderLine extends ZoomMoveViewContainer implements UnabelFocusedsView, AboveCoordinatesView {

    private Paint mLinePaint = null;
    private Paint mTextBgPaint = null;
    private Paint mScaleTextBgPaint = null;
    private Paint mTextPaint = null;
    private Paint mScaleTextPaint = null;
    private Paint closeBitmapPaint = null;
    private Paint mStrokePaint = null;

    private float mTextPaddingDP = 4f;
    private float mTextPadding = 0;
    private float mIconPadding = 50f;
    private float mIconOffsetRight = 15f;
    private float iconWidth = 16f;
    private float iconHeight = 16f;
    private float mTouchOffset = 10f;
    private float mHScreenPaddingLeft = 0f;

    // 边距
    private Context ctx;
    private ShareOrderData mShareOrderData;
    private Type type;
    private Typeface textTypeface;
    private Typeface normalTypeface;
    private float mPaddingHorizontal = 0;
    private float mPaddingVertical = 0;
    //圆角半径
    private float mCornerRoundRadius = DistKt.dp2px(ChartUIParamUtil.INSTANCE.getOrderLineRadiusDp());
    private RectF orderRF = new RectF();
    private RectF iconRF = new RectF();
    private RectF iconHotAreaRF = new RectF();
    private RectF scaleRF = new RectF();
    private float circleR = 0;
    // 激活状态
    private boolean isActive = false;
    private boolean isShowScale = true;

    private boolean isPositionLine;
    private int closeIconId = R.drawable.icon2_close_circle_24x24;

    public OrderLine(Context context, Type type) {
        super(context);
        this.ctx = context;
        this.isShow = false;
        this.type = type;
        isPositionLine = type == Type.POSITION_LINE;
        MultiLanguages.updateAppLanguage(context);
        MultiLanguages.updateAppLanguage(mContext);
        init();
    }

    private void init() {
        textTypeface = ResourcesCompat.getFont(ctx, R.font.gilroy_medium );
        normalTypeface = ResourcesCompat.getFont(ctx, R.font.gilroy_regular);
        circleR = getPixelDp(3f);
        mLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mLinePaint.setStyle(Paint.Style.STROKE);
        mLinePaint.setAntiAlias(true);
        mLinePaint.setStrokeWidth(getPixelDp(0.5f));

        mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.setStyle(Paint.Style.FILL);
        mTextPaint.setTypeface(textTypeface);
        mTextPaint.setTextSize(getPixelDp(9f));
        mTextPaint.setTextAlign(Paint.Align.LEFT);

        mTextBgPaint = new Paint();
        mTextBgPaint.setStyle(Paint.Style.FILL);
        mTextBgPaint.setAntiAlias(true);

        mScaleTextBgPaint = new Paint();
        mScaleTextBgPaint.setStyle(Paint.Style.FILL);
        mScaleTextBgPaint.setAntiAlias(true);

        mStrokePaint = new Paint();
        mStrokePaint.setStyle(Paint.Style.STROKE);
        mStrokePaint.setStrokeWidth(getPixelDp(0.5f));
        mStrokePaint.setAntiAlias(true);

        mScaleTextPaint = new Paint();
        mScaleTextPaint.setStyle(Paint.Style.FILL);
        mScaleTextPaint.setAntiAlias(true);
        mScaleTextPaint.setTypeface(normalTypeface);
        mScaleTextPaint.setTextSize(getPixelSp(9));
        mScaleTextPaint.setTextAlign(Paint.Align.CENTER);

        closeBitmapPaint = new Paint();
//        PorterDuffColorFilter pdcf = new PorterDuffColorFilter(ContextCompat.getColor(ctx, AppUtil.isLightTheme() ? R.color.c731e1e1e : R.color.c61ffffff), PorterDuff.Mode.SRC_IN);
//        closeBitmapPaint.setColorFilter(pdcf);

        mTextPadding = getPixelDp(mTextPaddingDP);

        iconWidth = getPixelDp(8f);
        iconHeight = getPixelDp(8f);

        mPaddingHorizontal = getPixelDp(ChartUIParamUtil.INSTANCE.getOrderLineTextPaddingHorizontalDp());
        mPaddingVertical = getPixelDp(ChartUIParamUtil.INSTANCE.getOrderLineTextPaddingVerticalDp());
        int screenWidth = KLineDataUtils.isFrontPortrait ? ScreenUtil.Companion.getAppScreenWidth() : ScreenUtil.Companion.getAppScreenHeight();
        mHScreenPaddingLeft = (screenWidth - DistKt.dp2px(ChartUIParamUtil.INSTANCE.getOrderLineMarginStartDp())) * ChartUIParamUtil.INSTANCE.getOrderLineTextMarginStartPercent();
        if (!AppUtil.isLightTheme()){
            closeIconId = R.drawable.icon2_close_circle_24x24_d;
        }
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        try {
            if (isShow) {

                checkParameter();

                if (mShareOrderData == null) return;
                if (type == Type.TAKE_PROFIT) {
                    // 止盈线
                    String takeProfit = mShareOrderData.getTakeProfit();
                    if (parseFloat(takeProfit) != 0 && parseFloat(takeProfit) < mYMax && parseFloat(takeProfit) >= parseFloat(ExpandKt.numFormat(mYMin, mShareOrderData.getDigits(), true))) {
                        mLinePaint.setColor(ChartUIParamUtil.INSTANCE.getOrderLineTPColor());
                        mTextBgPaint.setColor(isActive ? ChartUIParamUtil.INSTANCE.getOrderLineTPColor() : Color.WHITE);
                        mScaleTextBgPaint.setColor(ChartUIParamUtil.INSTANCE.getOrderLineTPColor());
                        mStrokePaint.setColor(ChartUIParamUtil.INSTANCE.getOrderLineTPColor());
                        mTextPaint.setColor(isActive ? Color.WHITE : mContext.getResources().getColor(R.color.c1e1e1e));
                        mScaleTextPaint.setColor(Color.WHITE);
//                        String tpTextStr = "#" + mShareOrderData.getOrder() + "  " + mShareOrderData.getVolume() + " " + ctx.getString(R.string.lots) + "  " + ExpandKt.numFormat(takeProfit, mShareOrderData.getDigits(), true) + "  TP";
//                        float textRectRight = drawTextStuff(canvas, parseFloat(takeProfit), tpTextStr, true);
                        float textRectRight = drawTpSl(canvas, parseFloat(takeProfit), "TP");
                        String takeProfitStr =  ExpandKt.numFormat(takeProfit, mShareOrderData.getDigits(), true);
                        // 虚线结束的x坐标
                        float latitudeXEnd = mCoordinateWidth - mPaddingHorizontal * 2 - mScaleTextPaint.measureText(takeProfitStr);
                        if (isActive) {
                            latitudeXEnd = latitudeXEnd - iconWidth - getPixelDp(4f);
                            drawIcon(canvas,parseFloat(takeProfit),latitudeXEnd);
                            drawOrderLine(canvas, parseFloat(takeProfit), textRectRight, latitudeXEnd);
                            if (isShowScale) {
                                drawScaleText(canvas, parseFloat(takeProfit), latitudeXEnd+iconWidth+getPixelDp(4));
                            }
                        }else {
                            drawOrderLine(canvas, parseFloat(takeProfit), textRectRight, latitudeXEnd);
                            if (isShowScale) {
                                drawScaleText(canvas, parseFloat(takeProfit), latitudeXEnd);
                            }
                            getChartView().setTpCancelRectF(null);
                        }

                    } else {
                        getChartView().setTpCancelRectF(null);
                    }
                }

                if (type == Type.STOP_LOSS) {
                    // 止损线
                    String stopLoss = mShareOrderData.getStopLoss();
                    if (parseFloat(stopLoss) != 0 && parseFloat(stopLoss) < mYMax && parseFloat(stopLoss) >= parseFloat(ExpandKt.numFormat(mYMin, mShareOrderData.getDigits(), true))) {
                        mLinePaint.setColor(ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
                        mTextBgPaint.setColor(isActive ? ChartUIParamUtil.INSTANCE.getSellIndicatorColor() : Color.WHITE);
                        mScaleTextBgPaint.setColor(ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
                        mStrokePaint.setColor(ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
                        mTextPaint.setColor(isActive ? Color.WHITE : mContext.getResources().getColor(R.color.c1e1e1e));
                        mScaleTextPaint.setColor(Color.WHITE);
//                        String slTextStr = "#" + mShareOrderData.getOrder() + "  " + mShareOrderData.getVolume() + " " + ctx.getString(R.string.lots) + "  " + ExpandKt.numFormat(stopLoss, mShareOrderData.getDigits(), true) + "  SL";
//                        float textRectRight = drawTextStuff(canvas, parseFloat(stopLoss), slTextStr, true);
                        float textRectRight = drawTpSl(canvas, parseFloat(stopLoss), "SL");
                        String stopLossStr =  ExpandKt.numFormat(stopLoss, mShareOrderData.getDigits(), true);
                        // 虚线结束的x坐标
                        float latitudeXEnd = mCoordinateWidth - mPaddingHorizontal * 2 - mScaleTextPaint.measureText(stopLossStr);
                        if (isActive) {
                            latitudeXEnd = latitudeXEnd - iconWidth - getPixelDp(4f);
                            drawIcon(canvas,parseFloat(stopLoss),latitudeXEnd);
                            drawOrderLine(canvas, parseFloat(stopLoss), textRectRight, latitudeXEnd);
                            if (isShowScale) {
                                drawScaleText(canvas, parseFloat(stopLoss), latitudeXEnd+iconWidth+getPixelDp(4));
                            }
                        }else {
                            drawOrderLine(canvas, parseFloat(stopLoss), textRectRight, latitudeXEnd);
                            if (isShowScale) {
                                drawScaleText(canvas, parseFloat(stopLoss), latitudeXEnd);
                            }
                            getChartView().setSlCancelRectF(null);
                        }

                    } else {
                        getChartView().setSlCancelRectF(null);
                    }
                }

                if (type == Type.POSITION_LINE) {
                    // 持仓线
                    float openPrice = parseFloat(mShareOrderData.getOpenPrice());
                    if (openPrice < mYMax && openPrice >= parseFloat(ExpandKt.numFormat(mYMin, mShareOrderData.getDigits(), true))) {
                        String cmd = mShareOrderData.getCmd();
                        int orderColor = OrderUtil.INSTANCE.isBuyOfOrder(cmd) ?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor();
                        mLinePaint.setColor(orderColor);
                        mScaleTextBgPaint.setColor(orderColor);
                        mScaleTextPaint.setColor(Color.WHITE);
                        String orderType;
                        if ("0".equals(cmd) || "2".equals(cmd) || "4".equals(cmd))
                            orderType = "BUY";
                        else
                            orderType = "SELL";

                        float textRectRight = drawOrderType(canvas, openPrice, orderType);
                        // 虚线结束的x坐标
                        String openStr =  ExpandKt.numFormat(openPrice, mShareOrderData.getDigits(), true);
                        float latitudeXEnd = mCoordinateWidth - mPaddingHorizontal * 2 - mScaleTextPaint.measureText(openStr);
                        if (isActive){
                            latitudeXEnd = latitudeXEnd - (2* circleR) - getPixelDp(0.5f);
                            drawOrderCircle(canvas,openPrice,latitudeXEnd+ circleR);
                            drawOrderLine(canvas, openPrice, textRectRight, latitudeXEnd);
                            if (isShowScale) {
                                drawScaleText(canvas, openPrice, latitudeXEnd+(2*circleR) + getPixelDp(0.5f));
                            }
                        } else {
                            drawOrderLine(canvas, openPrice, textRectRight, latitudeXEnd);
                            if (isShowScale) {
                                drawScaleText(canvas, openPrice, latitudeXEnd);
                            }
                        }

                    }
                }
            }
        } catch (Exception ignored) {
           ignored.printStackTrace();
        }
    }

    private float parseFloat(String floatStr) {
        int result = 0;
        if (!TextUtils.isEmpty(floatStr)) {
            try {
                return Float.parseFloat(floatStr);
            } catch (Exception e) {
                result = 0;
            }
        }
        return result;
    }

    private float drawOrderType(Canvas canvas, float price ,String orderType) {
        boolean isBuy = "BUY".equals(orderType);
        float y = caclY(price);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        mTextPaint.getFontMetrics(fm);
        float textHeight = Math.abs(fm.ascent) + mPaddingVertical;
        float orderTypeW = mTextPaint.measureText(orderType);
        String volumeStr = mShareOrderData.getVolume() + " " + ctx.getString(R.string.lots);
        float volumeStrW = mTextPaint.measureText(volumeStr);
        String tpSpStr = mShareOrderData.getProfitUI();
        if (tpSpStr == null) {
            tpSpStr = ExpandKt.numCurrencyFormat(mShareOrderData.getProfit(), UserDataUtil.currencyType(), true);
        }
        float tpSpStrW = mTextPaint.measureText(tpSpStr);

        orderRF.left = mCoordinateMarginLeft + mHScreenPaddingLeft;
        orderRF.top = y;
        orderRF.right = orderRF.left + orderTypeW + volumeStrW + tpSpStrW + mTextPadding * 6;
        orderRF.bottom = orderRF.top + textHeight + mTextPadding;
        // 保证矩形在线中间
        float recHeight = orderRF.height();
        orderRF.top -= recHeight / 2;
        orderRF.bottom -= recHeight / 2;

        RectF leftRect = new RectF(orderRF.left, orderRF.top, orderRF.left + orderTypeW + mTextPadding * 2, orderRF.bottom);
        float lineX = leftRect.right+volumeStrW+mTextPadding * 2;
        Paint linePaint = new Paint();
        if (isActive) {
            // 画背景
            mTextBgPaint.setColor(isBuy?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
            // 画边框
            mStrokePaint.setColor(isBuy?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mStrokePaint);
            linePaint.setColor(ctx.getColor(R.color.c61ffffff));
            linePaint.setStrokeWidth(getPixelDp(1f));
            canvas.drawLine(lineX,leftRect.top,lineX,leftRect.bottom,linePaint);
            linePaint.setStrokeWidth(getPixelDp(1f));
            canvas.drawLine(leftRect.right,leftRect.top,leftRect.right,leftRect.bottom,linePaint);
        } else {
            // 画背景
            mTextBgPaint.setColor(Color.WHITE);
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);

            mTextBgPaint.setColor(isBuy?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(leftRect,mCornerRoundRadius,mCornerRoundRadius,mTextBgPaint);
            // 画边框
            mStrokePaint.setColor(isBuy?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mStrokePaint);


            linePaint.setColor(isBuy?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            linePaint.setStrokeWidth(getPixelDp(0.5f));
            canvas.drawLine(lineX,leftRect.top,lineX,leftRect.bottom,linePaint);
            linePaint.setStrokeWidth(getPixelDp(2f));
            canvas.drawLine(leftRect.right,leftRect.top,leftRect.right,leftRect.bottom,linePaint);
        }
        mTextPaint.setColor(Color.WHITE);
        canvas.drawText(orderType, orderRF.left + mTextPadding, orderRF.top + (mTextPadding / 2) + textHeight- (mPaddingVertical/2f), mTextPaint);

        mTextPaint.setColor(isActive?Color.WHITE:ctx.getColor(R.color.c1e1e1e));
        canvas.drawText(volumeStr,leftRect.right+mTextPadding,orderRF.top + (mTextPadding / 2) + textHeight - (mPaddingVertical/2f),mTextPaint);
        float volumeX = lineX + mTextPadding ;
        canvas.drawText(tpSpStr,volumeX,orderRF.top + (mTextPadding / 2) + textHeight -(mPaddingVertical/2f),mTextPaint);

        if (type == Type.POSITION_LINE) {
            getChartView().setPositionRectF(orderRF);
        }
        return orderRF.right;
    }

    private float drawTpSl(Canvas canvas, float price ,String tpSlType){
        boolean isTp = "TP".equals(tpSlType);
        float y = caclY(price);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        mTextPaint.getFontMetrics(fm);
        float textHeight = Math.abs(fm.ascent) + mPaddingVertical;

//        String tpSlPrice = ExpandKt.numFormat(String.valueOf(price), mShareOrderData.getDigits(), true);
//        float tpSlPriceW = mTextPaint.measureText(tpSlPrice);
        String volumeStr = mShareOrderData.getVolume() + " " + ctx.getString(R.string.lots);
        float volumeStrW = mTextPaint.measureText(volumeStr);
        float orderTypeW = mTextPaint.measureText(tpSlType);

        orderRF.left = mCoordinateMarginLeft + mHScreenPaddingLeft;
        orderRF.top = y;
        orderRF.right = orderRF.left + orderTypeW + volumeStrW + mTextPadding * 4;
        orderRF.bottom = orderRF.top + textHeight + mTextPadding;
        // 保证矩形在线中间
        float recHeight = orderRF.height();
        orderRF.top -= recHeight / 2;
        orderRF.bottom -= recHeight / 2;

        RectF liftRect = new RectF(orderRF.left , orderRF.top, orderRF.left + 2* mTextPadding + orderTypeW, orderRF.bottom);
        float lineX = liftRect.right;
        Paint linePaint = new Paint();

        if (isActive) {
            // 画背景
            mTextBgPaint.setColor(isTp?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
            // 画边框
            mStrokePaint.setColor(isTp?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mStrokePaint);
            linePaint.setColor(ctx.getColor(R.color.c61ffffff));
            linePaint.setStrokeWidth(getPixelDp(1f));
            canvas.drawLine(lineX,liftRect.top,lineX,liftRect.bottom,linePaint);
//            linePaint.setStrokeWidth(getPixelDp(1f));
//            canvas.drawLine(liftRect.left,liftRect.top,liftRect.left,liftRect.bottom,linePaint);
        } else {
            mTextBgPaint.setColor(Color.WHITE);
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);

            mTextBgPaint.setColor(isTp?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(liftRect,mCornerRoundRadius,mCornerRoundRadius,mTextBgPaint);
            // 画边框
            mStrokePaint.setColor(isTp?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mStrokePaint);
            linePaint.setColor(isTp?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
            linePaint.setStrokeWidth(getPixelDp(2f));
            canvas.drawLine(lineX,liftRect.top,lineX,liftRect.bottom,linePaint);
//            linePaint.setStrokeWidth(getPixelDp(2f));
//            canvas.drawLine(liftRect.left,liftRect.top,liftRect.left,liftRect.bottom,linePaint);
        }

        mTextPaint.setColor(Color.WHITE);
        canvas.drawText(tpSlType, liftRect.left + mTextPadding, liftRect.top + (mTextPadding / 2) + textHeight - (mPaddingVertical/2f), mTextPaint);

        mTextPaint.setColor(isActive?Color.WHITE:ctx.getColor(R.color.c1e1e1e));
        canvas.drawText(volumeStr,liftRect.right+mTextPadding,orderRF.top + (mTextPadding / 2) + textHeight- (mPaddingVertical/2f),mTextPaint);
//        float priceX = lineX + mTextPadding ;
//        canvas.drawText(tpSlPrice,priceX,orderRF.top + (mTextPadding / 2) + textHeight,mTextPaint);
        if (isTp){
            getChartView().setTpRectF(orderRF);
        }else {
            getChartView().setSlRectF(orderRF);
        }


        return orderRF.right;
    }

    private void drawIcon(Canvas canvas, float price, float latitudeXEnd){
        float y = caclY(price);
        try {
            Bitmap bitmap = BitmapFactory.decodeResource(this.ctx.getResources(), closeIconId);
//                LogUtil.i("wj", "bitmap: "+bitmap);
            iconRF.top = orderRF.top + (orderRF.bottom - orderRF.top) / 2 - iconHeight / 2;
            iconRF.left = latitudeXEnd;
            iconRF.right = latitudeXEnd + iconWidth;
            iconRF.bottom = iconRF.top + iconHeight;

            canvas.drawBitmap(bitmap, null, iconRF, closeBitmapPaint);
            if (type == Type.TAKE_PROFIT) {
                getChartView().setTpCancelRectF(getIconHotAreaRectF(iconRF));
            }
            if (type == Type.STOP_LOSS) {
                getChartView().setSlCancelRectF(getIconHotAreaRectF(iconRF));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private float drawTextStuff(Canvas canvas, float price, String showTextStr, boolean drawIcon) {
        float y = caclY(price);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        mTextPaint.getFontMetrics(fm);

        float textHeight = Math.abs(fm.ascent);
        float iconPadding = isPositionLine ? 0 : mIconPadding;
        orderRF.left = mCoordinateMarginLeft + mHScreenPaddingLeft;
        orderRF.top = y;
        orderRF.right = orderRF.left + mTextPaint.measureText(showTextStr) + mTextPadding * 2 + iconPadding;
        orderRF.bottom = orderRF.top + textHeight + mTextPadding * 2;

        // 保证矩形在线中间
        float recHeight = orderRF.height();
        orderRF.top -= recHeight / 2;
        orderRF.bottom -= recHeight / 2;

        if (type == Type.POSITION_LINE) {
            getChartView().setPositionRectF(orderRF);
        } else if (type == Type.TAKE_PROFIT) {
            getChartView().setTpRectF(orderRF);
        } else if (type == Type.STOP_LOSS) {
            getChartView().setSlRectF(orderRF);
        }

        // 画背景
        canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
        if (!isActive) {
            // 画边框
            canvas.drawRoundRect(orderRF, mCornerRoundRadius, mCornerRoundRadius, mStrokePaint);
        }
        //                                                                     此数值 6 为offset 为了让应用了app字体的文字更居中
        canvas.drawText(showTextStr, orderRF.left + mTextPadding, orderRF.top + 6 + mTextPadding / 2 + textHeight, mTextPaint);

        // 画止盈/止损的 "X" 号
        if (drawIcon) {
            try {
                Bitmap bitmap = BitmapFactory.decodeResource(this.ctx.getResources(), R.drawable.icon_source2_close_16x16);
//                LogUtil.i("wj", "bitmap: "+bitmap);
                iconRF.top = orderRF.top + (orderRF.bottom - orderRF.top) / 2 - iconHeight / 2;
                iconRF.left = orderRF.right - mIconOffsetRight - iconWidth;
                iconRF.right = orderRF.right - mIconOffsetRight;
                iconRF.bottom = iconRF.top + iconHeight;

                canvas.drawBitmap(bitmap, null, iconRF, closeBitmapPaint);
                if (type == Type.TAKE_PROFIT) {
                    getChartView().setTpCancelRectF(getIconHotAreaRectF(orderRF));
                }
                if (type == Type.STOP_LOSS) {
                    getChartView().setSlCancelRectF(getIconHotAreaRectF(orderRF));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return orderRF.right;   // 返回矩形的右边界
    }

    // 按传入的文字矩形右边界作为起点画线
    private void drawOrderLine(Canvas canvas, float price, float startPoint, float latitudeXEnd) {
        float y = caclY(price);
        Path path = new Path();
        path.moveTo(startPoint, y);
        path.lineTo(latitudeXEnd, y);
        canvas.drawPath(path, mLinePaint);
    }

    private void drawOrderCircle(Canvas canvas, float price, float latitudeXEnd){
        float y = caclY(price);
        Paint paint = new Paint();
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(latitudeXEnd,y,circleR,paint);
        paint.setColor(ctx.getColor(R.color.c007fff));
        paint.setStrokeWidth(getPixelDp(1));
        paint.setStyle(Paint.Style.STROKE);
        canvas.drawCircle(latitudeXEnd,y,circleR,paint);
    }

    private void drawScaleText(Canvas canvas, float scaleText, float latitudeXEnd) {
        String openStr =  ExpandKt.numFormat(scaleText, mShareOrderData.getDigits(), true);
        Paint.FontMetricsInt fm = mScaleTextPaint.getFontMetricsInt();
        float textHeight = Math.abs(fm.ascent) + mPaddingVertical;
        float y = caclY(scaleText);
        scaleRF.left = latitudeXEnd;
        scaleRF.top = y;
        scaleRF.right = mCoordinateWidth;
        scaleRF.bottom = scaleRF.top + textHeight + mPaddingVertical * 2;
        //保证线在这个矩形中间
        float recHeight = scaleRF.height();
        scaleRF.top -= recHeight / 2;
        scaleRF.bottom -= recHeight / 2;

        float textWidth = mScaleTextPaint.measureText(openStr);
        // 画背景
        canvas.drawRoundRect(scaleRF, mCornerRoundRadius, mCornerRoundRadius, mScaleTextBgPaint);
        // 画文字
//        float strX = mCoordinateWidth - textWidth / 2f - mPaddingHorizontal;
        float strX = scaleRF.left+ mPaddingHorizontal + (textWidth/2f);
        float strY = scaleRF.top + textHeight + mPaddingVertical/2 ;
        canvas.drawText(openStr, strX , strY, mScaleTextPaint);
    }

    public float caclY(float price) {
        float max = mYMax;
        float min = Math.min(price, mYMin);
        return (1f - (price - min) / (max - min)) * mCoordinateHeight;
    }

    private void checkParameter() {
        if (this.mShownPointNums < 0) {
            throw new IllegalArgumentException("maxPointNum must be larger than 0");
        }
        if (this.mCoordinateHeight <= 0) {
            throw new IllegalArgumentException("mCoordinateHeight can't be zero or smaller than zero");
        }
        if (this.mCoordinateWidth <= 0) {
            throw new IllegalArgumentException("mCoordinateWidth can't be zero or smaller than zero");
        }
    }

    /**
     * 文字高度
     */
    private float getTextHeight(Paint textPaint) {
        Paint.FontMetrics fm = new Paint.FontMetrics();
        textPaint.getFontMetrics(fm);
        return Math.abs(fm.ascent);
    }

    private RectF getIconHotAreaRectF(RectF textBgRect) {
        float hotSize = getPixelDp(60f);
        iconHotAreaRF.top = textBgRect.top - hotSize;
        iconHotAreaRF.bottom = textBgRect.bottom+hotSize;
        iconHotAreaRF.right = textBgRect.right+hotSize;
        iconHotAreaRF.left = textBgRect.left - hotSize;
        return iconHotAreaRF;
    }

    public void setLineDashPath(PathEffect pathEffect) {
        mLinePaint.setPathEffect(pathEffect);
    }

    public void setTextColor(int textColor) {
        mTextPaint.setColor(textColor);
    }

    public void setTextSize(float sp) {
        mTextPaint.setTextSize(getPixelSp(sp));
    }

    public void setLineWidth(float dp) {
        mLinePaint.setStrokeWidth(getPixelDp(dp));
    }

    public ShareOrderData getOrderData() {
        return mShareOrderData;
    }

    public void setOrderData(ShareOrderData shareOrderData) {
        mShareOrderData = shareOrderData;
    }

    public void actived() {
        this.isActive = true;
    }

    public void disactived() {
        this.isActive = false;
    }

    public boolean isActive() {
        return this.isActive;
    }

    public enum Type {
        /**
         * 持仓线
         */
        POSITION_LINE,

        /**
         * 止盈线
         */
        TAKE_PROFIT,

        /**
         * 止损线
         */
        STOP_LOSS,
    }

    public enum Direction {
        /**
         * Buy
         */
        BUY,
        /**
         * Sell
         */
        SELL,
    }
}
