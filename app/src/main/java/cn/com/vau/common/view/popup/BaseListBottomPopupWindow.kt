package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.PopupBaseListBottomBinding
import cn.com.vau.page.common.BaseListBean

/**
 * Created by roy on 2018/12/5.
 * 底部
 */
class BaseListBottomPopupWindow(val context: Context) : PopupWindow() {

    private val popView by lazy { PopupBaseListBottomBinding.inflate(LayoutInflater.from(context)) }
    private var adapter: PopListBottomRcyAdapter? = null

    private var dataList: ArrayList<String> = arrayListOf()
    private var popTitle: String = ""
    private var currentSelectIndex: Int = 0
    private var isShowDoneBtn: Boolean = true

    init {
        initParam()
        initView()
        initData()
        initListener()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        adapter = PopListBottomRcyAdapter(context, dataList)
        popView.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        popView.mRecyclerView.adapter = adapter
    }

    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    fun initData() {
        popView.tvTitle.text = popTitle

        popView.mRecyclerView.post {
            if (dataList.size > 6) {
                val itemView = popView?.mRecyclerView?.getChildAt(0)
                itemView?.let {
                    val height = it.height * 6
                    val layoutParams = popView?.mRecyclerView?.layoutParams
                    layoutParams?.height = height
                    popView?.mRecyclerView?.layoutParams = layoutParams
                }
            }
        }

    }

    private fun initParam() {
        this.contentView = popView.root
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.WRAP_CONTENT
        // 设置PopupWindow弹出窗体可点击
        this.isFocusable = true
        // 设置SelectPicPopupWindow弹出窗体动画效果
        this.animationStyle = R.style.popupAnimStyleBottom
    }

    private fun initListener() {
        adapter?.setOnItemClickListener(object : PopListBottomRcyAdapter.OnItemClickListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClick(position: Int, itemType: Int) {
                if (itemType == 1) {
                    mOnPopClickListener?.onItemClick(position)
                    dismiss()
                } else {
                    currentSelectIndex = position
                    mOnPopClickListener?.onItemClick(position)
                    dismiss()
                }
            }
        })


        popView?.tvDone?.setOnClickListener {
            dismiss()
        }

    }

    private var mOnPopClickListener: OnPopClickListener? = null

    interface OnPopClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnPopClickListener(mOnPopClickListener: OnPopClickListener): BaseListBottomPopupWindow {
        this.mOnPopClickListener = mOnPopClickListener
        return this
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setBaseListData(
        dataList: List<BaseListBean>,
        selectIndex: Int,
        popTitle: String
    ): BaseListBottomPopupWindow {
        return setBaseListData(dataList, selectIndex, popTitle, 0)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setBaseListData(
        dataList: List<BaseListBean>,
        selectIndex: Int,
        popTitle: String,
        itemType: Int
    ): BaseListBottomPopupWindow {
        this.dataList.clear()

        for (dataBean in dataList) {
            this.dataList.add(dataBean.getShowItemValue())
        }

        currentSelectIndex = selectIndex
        adapter?.setItemType(itemType)
        adapter?.setSelectIndex(selectIndex)
        adapter?.notifyDataSetChanged()
        isShowDoneBtn = itemType == 0

        this.popTitle = popTitle

        initData()

        return this

    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(
        dataList: List<String>,
        selectIndex: Int,
        popTitle: String
    ): BaseListBottomPopupWindow {
        this.dataList.clear()
        this.dataList.addAll(dataList)
        adapter?.setSelectIndex(selectIndex)
        adapter?.notifyDataSetChanged()
        this.popTitle = popTitle
        initData()
        return this
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(
        dataList: List<String>,
        popTitle: String,
        itemType: Int
    ): BaseListBottomPopupWindow {
        this.dataList.clear()
        this.dataList.addAll(dataList)
        adapter?.setSelectIndex(-1)
        adapter?.setItemType(itemType)
        adapter?.notifyDataSetChanged()
        isShowDoneBtn = itemType == 0
        this.popTitle = popTitle
        initData()
        return this
    }

}