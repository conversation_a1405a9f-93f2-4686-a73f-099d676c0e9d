package com.kit.extra

import android.app.Activity
import android.content.Intent
import cn.com.vau.kit.biz.IExtraBean
import com.kit.LiangActivity
import com.kit.ZLActivity


class LiangExtraEntry : IExtraBean {
    override fun getName(): String {
        return "Liang"
    }

    override fun onClick(context: Activity) {
        val intent = Intent(context, LiangActivity::class.java)
        context.startActivity(intent)
//        ReverseOrderDialog.Builder(context).setMainOrderId("123").build().show()
    }


}