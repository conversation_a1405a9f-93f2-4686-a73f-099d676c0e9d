package cn.com.vau.kit.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import cn.com.vau.kit.R;
import cn.com.vau.kit.model.fragment.BaseKitFragment;
import cn.com.vau.kit.itf.LoadTargetFragmentItf;

import java.util.List;

import static cn.com.vau.kit.constant.Constants.BUNDLE_KEY;

public class TransferCenterKitActivity extends BaseKitActivity implements LoadTargetFragmentItf {

    private static final String TARGET_FRAGMENT = "TARGET_FRAGMENT";

    private static final String TAG_FRAGMENT = "TAG_FRAGMENT";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.kit_transfer_center_actrivity);
        initFragment();
    }

    private void initFragment() {
        Class targetFragment = (Class) getIntent().getSerializableExtra(TARGET_FRAGMENT);
        try {
            Bundle extras = getIntent().getExtras();
            Bundle bundle = extras.getBundle(BUNDLE_KEY);

            Fragment fragment = (Fragment) targetFragment.newInstance();
            fragment.setArguments(bundle);

            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.mContainer, fragment, TAG_FRAGMENT)
                    .commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void jump(Activity activity, Class target) {
        Intent intent = new Intent(activity, TransferCenterKitActivity.class);
        intent.putExtra(TARGET_FRAGMENT, target);
        activity.startActivity(intent);
    }


    public static void jump(Activity activity, Class target, Bundle bundle) {
        Intent intent = new Intent(activity, TransferCenterKitActivity.class);
        intent.putExtra(TARGET_FRAGMENT, target);
        intent.putExtra(BUNDLE_KEY, bundle);
        activity.startActivity(intent);
    }


    @Override
    public void loadFragment(Class<?> className, Bundle bundle) {
        try {
            Fragment fragment = (Fragment) className.newInstance();
            fragment.setArguments(bundle);
            getSupportFragmentManager()
                    .beginTransaction()
                    .addToBackStack(null)
                    .replace(R.id.mContainer, fragment, TAG_FRAGMENT)
                    .commit();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        Fragment fragment = getSupportFragmentManager().findFragmentByTag(TAG_FRAGMENT);
        if (fragment != null) {
            if (fragment instanceof BaseKitFragment) {
                BaseKitFragment baseKitFragment = (BaseKitFragment) fragment;
                if (!baseKitFragment.onBackPressListener()) {
                    return;
                }
            }
        }
        super.onBackPressed();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (Fragment fragment : fragments) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (Fragment fragment : fragments) {
            fragment.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }
}
