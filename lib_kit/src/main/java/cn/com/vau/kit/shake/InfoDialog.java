package cn.com.vau.kit.shake;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.SimpleAdapter;

import cn.com.vau.kit.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class InfoDialog extends Dialog {

    public InfoDialog(Context context) {
        super(context);
    }
    public InfoDialog(Context context, int theme) {
        super(context, theme);
    }

    public static class Builder {
        private List<String> messages;

        private View contentView;

        private String positiveButtonText;
        private String negativeButtonText;
        private String singleButtonText;

        private View.OnClickListener positiveButtonClickListener;
        private View.OnClickListener negativeButtonClickListener;
        private View.OnClickListener singleButtonClickListener;

        private View layout;
        private InfoDialog dialog;

        private Context mContext;

        public Builder(Context context) {
            if (context == null) return;

            mContext = context;

            // 这里传入自定义的style，直接影响此Dialog的显示效果。style具体实现见style.xml
            dialog = new InfoDialog(mContext, R.style.Dialog);

            LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            layout = inflater.inflate(R.layout.kit_dialog_layout, null);

            dialog.addContentView(layout, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT));
        }

        public InfoDialog getDialog() {
            return dialog;
        }

        public Builder setMessages(List<String> messages) {
            this.messages = messages;
            return this;
        }

        public Builder setContentView(View v) {
            this.contentView = v;
            return this;
        }

        public Builder setPositiveButton(String positiveButtonText, View.OnClickListener listener) {
            this.positiveButtonText = positiveButtonText;
            this.positiveButtonClickListener = listener;
            return this;
        }

        public Builder setNegativeButton(String negativeButtonText, View.OnClickListener listener) {
            this.negativeButtonText = negativeButtonText;
            this.negativeButtonClickListener = listener;
            return this;
        }

        public Builder setSingleButton(String singleButtonText, View.OnClickListener listener) {
            this.singleButtonText = singleButtonText;
            this.singleButtonClickListener = listener;
            return this;
        }

        /**
         * 创建单按钮对话框
         * @return
         */
        public InfoDialog createSingleButtonDialog() {
            showSingleButton();
            layout.findViewById(R.id.singleButton).setOnClickListener(singleButtonClickListener);

            // 如果传入的按钮文字为空，则使用默认的“返回”
            if (singleButtonText != null) {
                ((Button) layout.findViewById(R.id.singleButton)).setText(singleButtonText);
            } else {
                ((Button) layout.findViewById(R.id.singleButton)).setText("返回");
            }

            create();
            return dialog;
        }

        /**
         * 创建双按钮对话框
         * @return
         */
        public InfoDialog createTwoButtonDialog() {
            showTwoButton();

            layout.findViewById(R.id.positiveButton).setOnClickListener(positiveButtonClickListener);
            layout.findViewById(R.id.negativeButton).setOnClickListener(negativeButtonClickListener);

            // 如果传入的按钮文字为空，则使用默认的“是”和“否”
            if (positiveButtonText != null) {
                ((Button) layout.findViewById(R.id.positiveButton)).setText(positiveButtonText);
            } else {
                ((Button) layout.findViewById(R.id.positiveButton)).setText("是");
            }

            if (negativeButtonText != null) {
                ((Button) layout.findViewById(R.id.negativeButton)).setText(negativeButtonText);
            } else {
                ((Button) layout.findViewById(R.id.negativeButton)).setText("否");
            }

            create();
            return dialog;
        }

        /**
         * 单按钮对话框和双按钮对话框的公共部分在这里设置
         */
        private void create() {
            if (messages != null) {// 设置提示内容
                List<Map<String, String>> mapList = new ArrayList<>();

                for (String msg : messages) {
                    Map<String, String> map = new HashMap<>();
                    map.put("message", msg);

                    mapList.add(map);
                }

                ListView messageLV = (ListView) layout.findViewById(R.id.message_lv);
                final SimpleAdapter adapter = new SimpleAdapter(mContext, mapList,
                        R.layout.kit_item_info_lv, new String[] { "message" },// 与下面数组元素要一一对应
                        new int[] { R.id.item_content });

                messageLV.setAdapter(adapter);
            } else if (contentView != null) {// 如果使用Builder的setContentview()方法传入了布局，则使用传入的布局
                ((LinearLayout) layout.findViewById(R.id.content)).removeAllViews();
                ((LinearLayout) layout.findViewById(R.id.content))
                        .addView(contentView, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT));
            }

            dialog.setContentView(layout);
            dialog.setCancelable(true);// 用户可以点击手机Back键取消对话框显示
            dialog.setCanceledOnTouchOutside(true);// 用户能通过点击对话框之外的地方取消对话框显示
        }

        /**
         * 显示双按钮布局，隐藏单按钮
         */
        private void showTwoButton() {
            layout.findViewById(R.id.singleButtonLayout).setVisibility(View.GONE);
            layout.findViewById(R.id.twoButtonLayout).setVisibility(View.VISIBLE);
        }

        /**
         * 显示单按钮布局，隐藏双按钮
         */
        private void showSingleButton() {
            layout.findViewById(R.id.singleButtonLayout).setVisibility(View.VISIBLE);
            layout.findViewById(R.id.twoButtonLayout).setVisibility(View.GONE);
        }
    }
}
