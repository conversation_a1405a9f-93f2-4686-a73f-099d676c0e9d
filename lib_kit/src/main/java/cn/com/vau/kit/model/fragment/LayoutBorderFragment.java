package cn.com.vau.kit.model.fragment;

import cn.com.vau.kit.model.layoutborder.LayoutBorderConfig;
import cn.com.vau.kit.model.layoutborder.LayoutBorderManager;
import cn.com.vau.kit.model.layoutborder.LayoutLevelFloatPage;
import cn.com.vau.kit.model.pickcolor.FloatPageManager;
import cn.com.vau.kit.model.pickcolor.PageIntent;


public class LayoutBorderFragment  {


   public static void initLayoutBorder(){
       LayoutBorderManager.getInstance().start();
       LayoutBorderConfig.setLayoutBorderOpen(true);

       PageIntent intent = new PageIntent(LayoutLevelFloatPage.class);
       intent.mode = PageIntent.MODE_SINGLE_INSTANCE;
       FloatPageManager.getInstance().add(intent);
       LayoutBorderConfig.setLayoutLevelOpen(true);
   }
}
