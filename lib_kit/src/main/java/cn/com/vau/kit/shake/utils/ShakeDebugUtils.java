package cn.com.vau.kit.shake.utils;

import android.app.Activity;
import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;

import cn.com.vau.kit.shake.InfoDialog;
import cn.com.vau.kit.shake.callback.ActivityCallbacks;
import cn.com.vau.kit.shake.info.DisplayInfo;
import cn.com.vau.kit.shake.info.MemInfo;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static android.util.Half.EPSILON;



public class ShakeDebugUtils implements SensorEventListener, ActivityCallbacks.OnActCallbacks {
    private WeakReference<Activity> activityWeakReference;

    private SensorManager mSensorManager;
    private OnShakeListener mOnShakeListener = null;

    private InfoDialog mDialog;

    private DisplayInfo displayInfo;
    private MemInfo memInfo;

    private String curActName = "";// 当前Activity名称
    private int allActRefCount = 0;// 所有activity的引用个数

    private Set<String> fragmentSet;// Activity包含的fragment集合

    public ShakeDebugUtils(Activity activity, ActivityCallbacks activityCallbacks) {
        activityWeakReference = new WeakReference<>(activity);

        mSensorManager = (SensorManager) activityWeakReference.get().getApplicationContext()
                .getSystemService(Context.SENSOR_SERVICE);
        activityCallbacks.setActCallbacks(this);

        displayInfo = new DisplayInfo(activityWeakReference.get());
        memInfo = new MemInfo(activityWeakReference.get());
    }

    public void setOnShakeListener(OnShakeListener onShakeListener) {
        mOnShakeListener = onShakeListener;
    }

    public void onResume() {
        mSensorManager.registerListener(this,
                mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER),
                SensorManager.SENSOR_DELAY_NORMAL);
    }

    public void onPause() {
        mSensorManager.unregisterListener(this);
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {

    }

    private static final float NS2S = 1.0f / 1000000000.0f;
    private final float[] deltaRotationVector = new float[4];
    private float timestamp;

    //速度阀值
    private int mSpeed = 7000;
    //时间间隔
    private int mInterval = 50;
    //上一次摇晃的时间
    private long LastTime;
    //上一次的x、y、z坐标
    private float LastX;
    private float LastY;
    private float LastZ;

    @Override
    public void onSensorChanged(SensorEvent event) {

//        int SENSOR_VALUE = 40;
//
//        // This time step's delta rotation to be multiplied by the current rotation
//        // after computing it from the gyro sample data.
//        if (timestamp != 0) {
//            final float dT = (event.timestamp - timestamp) * NS2S;
//            // Axis of the rotation sample, not normalized yet.
//            float axisX = event.values[0];
//            float axisY = event.values[1];
//            float axisZ = event.values[2];
//
//            // Calculate the angular speed of the sample
//            double omegaMagnitude = Math.sqrt((double) (axisX * axisX + axisY * axisY + axisZ * axisZ));
//
//
//            // Normalize the rotation vector if it's big enough to get the axis
//            if (omegaMagnitude > EPSILON) {
//                axisX /= omegaMagnitude;
//                axisY /= omegaMagnitude;
//                axisZ /= omegaMagnitude;
//            }
//
//            // Integrate around this axis with the angular speed by the time step
//            // in order to get a delta rotation from this sample over the time step
//            // We will convert this axis-angle representation of the delta rotation
//            // into a quaternion before turning it into the rotation matrix.
//            double thetaOverTwo = omegaMagnitude * dT / 2.0f;
//            float sinThetaOverTwo = (float) Math.sin(thetaOverTwo);
//            float cosThetaOverTwo = (float) Math.cos(thetaOverTwo);
//            deltaRotationVector[0] = sinThetaOverTwo * axisX;
//            deltaRotationVector[1] = sinThetaOverTwo * axisY;
//            deltaRotationVector[2] = sinThetaOverTwo * axisZ;
//            deltaRotationVector[3] = cosThetaOverTwo;
//
//            if (Math.abs(deltaRotationVector[0]) > SENSOR_VALUE ||
//                    Math.abs(deltaRotationVector[1]) > SENSOR_VALUE ||
//                    Math.abs(deltaRotationVector[2]) > SENSOR_VALUE ||
//                    Math.abs(deltaRotationVector[3]) > SENSOR_VALUE
//            ) {
//                Log.d(TAG, "onSensorChanged: ");
//                onShakeListener();
//            }
//        }
//        timestamp = event.timestamp;
//        float[] deltaRotationMatrix = new float[9];
//        SensorManager.getRotationMatrixFromVector(deltaRotationMatrix, deltaRotationVector);

        long NowTime = System.currentTimeMillis();
        if ((NowTime - LastTime) < mInterval)
            return;
        //将NowTime赋给LastTime
        LastTime = NowTime;
        //获取x,y,z
        float NowX = event.values[0];
        float NowY = event.values[1];
        float NowZ = event.values[2];
        //计算x,y,z变化量
        float DeltaX = NowX - LastX;
        float DeltaY = NowY - LastY;
        float DeltaZ = NowZ - LastZ;
        //赋值
        LastX = NowX;
        LastY = NowY;
        LastZ = NowZ;
        //计算
        double NowSpeed = Math.sqrt(DeltaX * DeltaX + DeltaY * DeltaY + DeltaZ * DeltaZ) / mInterval * 10000;
        //判断
        if (NowSpeed >= mSpeed) {
            // 开启debug界面
//            AppDebugActivity.openIt(mContext);
            onShakeListener();
        }
    }

    private void onShakeListener() {
        if (null != mOnShakeListener) {
            mOnShakeListener.onShake();
        } else {
            final int screenHeight = displayInfo.getScreenHeight();
            final int screenWidth = displayInfo.getScreenWidth();
            final float density = displayInfo.getDensity();
            final int densityDPI = displayInfo.getDensityDPI();

            final long totalMemory = memInfo.getTotalMemory();

            final String[] threadNames = ThreadUtils.getThreadNames();
            final StringBuffer namesSB = new StringBuffer();
            for (String threadName : threadNames) {
                namesSB.append(threadName + " ");
            }

            Handler mHandler = new Handler(Looper.getMainLooper());
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (mDialog != null && mDialog.isShowing()) {
                        return;
                    }

                    Activity activity = activityWeakReference.get();
                    if (activity == null) return;

                    if (activity.isFinishing()) return;

                    final InfoDialog.Builder builder = new InfoDialog.Builder(activity);

                    List<String> messages = new ArrayList<>();
                    messages.add("当前类名: " + curActName);
                    messages.add("所有类的引用个数：" + allActRefCount + "个");
                    messages.add("共有" + threadNames.length + "个线程在运行，它们是：" + namesSB.toString());
                    messages.add("屏幕尺寸：" + screenWidth + " x " + screenHeight);
                    messages.add("屏幕密度：" + densityDPI + "像素/英寸，像素比例为" + density);
                    messages.add("当前分配的总内存：" + totalMemory + " bytes");

                    if (fragmentSet != null && fragmentSet.size() > 0) {
                        messages.add("共有" + fragmentSet.size() + "个Fragment，它们是：");
                        for (String nameAndId : fragmentSet) {
                            messages.add(nameAndId);
                        }
                    }

                    mDialog = builder.setMessages(messages)
                            .setSingleButton("OK", new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    if (builder.getDialog() != null) {
                                        builder.getDialog().dismiss();
                                    }
                                }
                            })
                            .createSingleButtonDialog();

                    mDialog.show();
                }
            });
        }
    }

    private static final String TAG = "ShakeDebugUtils";

    @Override
    public void onActResume(String actName, int refCount) {
        curActName = actName;
    }

    @Override
    public void onAllActRef(int refCount) {
        allActRefCount = refCount;
    }

    @Override
    public void onFragmentResume(Set<String> fragmentSet) {
        this.fragmentSet = fragmentSet;
    }

    public interface OnShakeListener {
        void onShake();
    }
}
