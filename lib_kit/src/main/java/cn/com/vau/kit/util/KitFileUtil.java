package cn.com.vau.kit.util;

import android.app.Application;

import cn.com.vau.kit.Kit;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;



public class KitFileUtil {

    public static final String TXT = "txt";
    public static final String JPG = "jpg";
    public static final String DB = "db";
    public static final String XML = ".xml";
    private static final String SHARED_PREFS = "shared_prefs";

    public static List<File> listFiles(File file) {
        if (file != null && file.isDirectory()) {
            File[] files = file.listFiles();
            return Arrays.asList(files);
        }
        return null;
    }

    public static String getSuffix(File file) {
        if (file == null || !file.exists()) {
            return "";
        }
        return file.getName()
                .substring(file.getName().lastIndexOf(".") + 1)
                .toLowerCase(Locale.getDefault());
    }

    public static boolean isImageFile(File file) {
        if (file == null) return false;
        String suffix = getSuffix(file);
        return "jpg".equals(suffix) || "jpeg".equals(suffix) || "png".equals(suffix) || "bmp".equals(suffix);
    }

    public static boolean isSpFile(File file) {
        File parentFile = file.getParentFile();
        if (parentFile != null && parentFile.getName().equals(SHARED_PREFS) && file.getName().contains(XML)) {
            return true;
        }
        return false;
    }

    public static boolean isDbFile(File file) {
        if (file == null) {
            return false;
        }
        String suffix = getSuffix(file);
        return "db".equals(suffix);
    }

    public static boolean isVideoFile(File file) {
        if (file == null) {
            return false;
        }
        String suffix = getSuffix(file);
        return "3gp".equals(suffix) || "mp4".equals(suffix) || "mkv".equals(suffix) || "webm".equals(suffix);
    }


    public static void deleteFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
            else if (file.isDirectory())
                deleteFile(file); // 递规的方式删除文件夹
        }
        dir.delete();// 删除目录本身
    }

    //获得Crash文件夹路径
    public static File getCrashRootFile() {
        Application application = Kit.getInstance().getApplication();
        File cacheDir = application.getCacheDir();
        String path = cacheDir + "/" + "crash";
        File file = new File(path);
        if (!file.exists() && file.mkdirs()) {
            return file;
        }
        return file;
    }

    //写内容到文件里面
    public static void writeFile(String fileName, String content) {
        OutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(fileName);
            outputStream.write(content.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    //读取文件内容
    public static String readFile(File path) {
        if (path == null || !path.exists()) {
            return "";
        }
        FileInputStream inputStream = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            inputStream = new FileInputStream(path);
            int length;
            byte[] buffer = new byte[1024 * 60];
            while ((length = inputStream.read(buffer)) != -1) {
                stringBuilder.append(new String(buffer, 0, length));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuilder.toString();
    }

}
