package cn.com.vau.kit.item;


import androidx.annotation.IntDef;

import cn.com.vau.kit.bean.Element;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;


public class EditTextItem extends ElementItem {

    private @Type
    int type;
    private String detail;

    public EditTextItem(String name, Element element, @Type int type, String detail) {
        super(name, element);
        this.type = type;
        this.detail = detail;
    }

    public String getDetail() {
        return detail;
    }

    public int getType() {
        return type;
    }

    @IntDef({
            Type.TYPE_TEXT,
            Type.TYPE_TEXT_SIZE,
            Type.TYPE_TEXT_COLOR,
            Type.TYPE_WIDTH,
            Type.TYPE_HEIGHT,
            Type.TYPE_PADDING_LEFT,
            Type.TYPE_PADDING_RIGHT,
            Type.TYPE_PADDING_TOP,
            Type.TYPE_PADDING_BOTTOM,
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface Type {
        int TYPE_TEXT = 1;
        int TYPE_TEXT_SIZE = 2;
        int TYPE_TEXT_COLOR = 3;
        int TYPE_WIDTH = 4;
        int TYPE_HEIGHT = 5;
        int TYPE_PADDING_LEFT = 6;
        int TYPE_PADDING_RIGHT = 7;
        int TYPE_PADDING_TOP = 8;
        int TYPE_PADDING_BOTTOM = 9;
    }
}
