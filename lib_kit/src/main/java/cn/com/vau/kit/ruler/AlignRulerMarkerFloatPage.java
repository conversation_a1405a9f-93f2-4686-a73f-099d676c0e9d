package cn.com.vau.kit.ruler;

import android.content.Context;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import cn.com.vau.kit.R;
import cn.com.vau.kit.model.fragment.RulerFragment;
import cn.com.vau.kit.model.pickcolor.BaseFloatPage;
import cn.com.vau.kit.model.pickcolor.TouchProxy;
import cn.com.vau.kit.util.KitScaleUtil;

import java.util.ArrayList;
import java.util.List;


public class AlignRulerMarkerFloatPage extends BaseFloatPage implements TouchProxy.OnTouchEventListener {
    private List<OnAlignRulerMarkerPositionChangeListener> mPositionChangeListeners = new ArrayList<>();

    private TouchProxy mTouchProxy = new TouchProxy(this);

    private WindowManager mWindowManager;

    @Override
    protected View onCreateView(Context context, ViewGroup view) {
        return LayoutInflater.from(context).inflate(R.layout.kit_float_align_ruler_marker, null);
    }

    @Override
    protected void onViewCreated(View view) {
        super.onViewCreated(view);
        initView();
    }

    private void initView() {
        getRootView().setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                detector.onTouchEvent(event);
                return mTouchProxy.onTouchEvent(v, event);
            }
        });
    }


    private GestureDetector detector = new GestureDetector(new GestureDetector.SimpleOnGestureListener() {
        @Override
        public boolean onDoubleTap(MotionEvent e) {
            RulerFragment.removeRuler();
            return super.onDoubleTap(e);
        }
    });

    @Override
    protected void onLayoutParamsCreated(WindowManager.LayoutParams params) {
        params.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.x = KitScaleUtil.getWidthPixels(getContext()) / 2;
        params.y = KitScaleUtil.getHeightPixels() / 2;
    }

    @Override
    protected void onCreate(Context context) {
        super.onCreate(context);
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removePositionChangeListeners();
    }

    @Override
    public void onMove(int x, int y, int dx, int dy) {
        getLayoutParams().x += dx;
        getLayoutParams().y += dy;
        mWindowManager.updateViewLayout(getRootView(), getLayoutParams());
        for (OnAlignRulerMarkerPositionChangeListener listener : mPositionChangeListeners) {
            listener.onPositionChanged(getLayoutParams().x + getRootView().getWidth() / 2, getLayoutParams().y + getRootView().getHeight() / 2);
        }
    }

    @Override
    public void onUp(int x, int y) {

    }

    @Override
    public void onDown(int x, int y) {

    }

    public interface OnAlignRulerMarkerPositionChangeListener {
        void onPositionChanged(int x, int y);
    }

    public void addPositionChangeListener(OnAlignRulerMarkerPositionChangeListener positionChangeListener) {
        mPositionChangeListeners.add(positionChangeListener);
    }

    public void removePositionChangeListener(OnAlignRulerMarkerPositionChangeListener positionChangeListener) {
        mPositionChangeListeners.remove(positionChangeListener);
    }

    public void removePositionChangeListeners() {
        mPositionChangeListeners.clear();
    }

    @Override
    public void onEnterForeground() {
        super.onEnterForeground();
        getRootView().setVisibility(View.VISIBLE);
    }

    @Override
    public void onEnterBackground() {
        super.onEnterBackground();
        getRootView().setVisibility(View.GONE);
    }
}
