plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
}

android {
    namespace = "cn.com.vau.kit"
    compileSdk = 34

    defaultConfig {
        minSdk = 23

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {

    implementation("androidx.core:core-ktx:1.13.1")
    implementation("androidx.appcompat:appcompat:1.7.0")
    implementation("com.google.android.material:material:1.12.0")
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.2.1")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    implementation("androidx.legacy:legacy-support-v4:1.0.0")
    implementation("nl.qbusict:cupboard:2.2.0")
    implementation("com.google.code.gson:gson:2.8.9")
    implementation("com.facebook.fresco:fresco:1.13.0")
//    implementation ("io.github.knight-zxw:blockcanary:0.0.5")
//    implementation ("io.github.knight-zxw:blockcanary-ui:0.0.5")


}


//遍历项目里面所有的第三方的资源
//gradle.buildFinished {
//
//
//    val result = mutableSetOf<String>()
//
//// 遍历所有的 project 模块
//    getGradle()?.allprojects {  ->
//        println("====projectName=${project.name}")
//        val configurations = project.configurations
//        configurations.forEach { files: Configuration ->
//            println("name=${files.name}")
//            val name = files.name
//            if (name!= null) {
//                val dep = files.dependencies
//                dep.forEach { dependency: Dependency ->
//                    if (dependency.group!= null) {
//                        val dependencyInfo = "${dependency.group}:${dependency.name}:${dependency.version}"
//                        result.add(dependencyInfo)
//                    }
//                }
//            }
//        }
//    }
//
//    val value = StringBuilder()
//    result.forEach { s ->
//        value.append(s)
//        value.append("###")
//        println(s)
//    }
//    val file = File("${projectDir}/src/main/java/cn/com/vau/kit/LibraryInfo.java")
//    file.writeText(
//        """
//        package cn.com.vau.kit;
//
//        /**
//         * create by array
//         * date on 2025/01/20
//         */
//        public class LibraryInfo {
//            public static String libraryInfo = "${value}";
//        }
//        """.trimIndent()
//    )
//    println("filePath=${file.absolutePath}")
//}