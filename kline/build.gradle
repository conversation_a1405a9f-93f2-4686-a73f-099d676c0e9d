apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: "kotlin-parcelize"
apply plugin: 'kotlin-kapt'
android {
    namespace = "com.example.myapplication"
    compileSdk = 34

    defaultConfig {
//        applicationId = "com.example.myapplication"
        minSdk = 23
        targetSdk = 34
//        versionCode = 1
//        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildTypes {
        release {
//            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    dataBinding {
        enable = true
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
    }
//    composeOptions {
//        kotlinCompilerExtensionVersion = "1.5.1"
//    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {

    implementation("androidx.core:core-ktx:1.10.1")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.4")
//    implementation(libs.androidx.activity.compose)
//    implementation(platform(libs.androidx.compose.bom))
//    implementation(libs.androidx.ui)
//    implementation(libs.androidx.ui.graphics)
//    implementation(libs.androidx.ui.tooling.preview)
//    implementation(libs.androidx.material3)
    implementation("androidx.room:room-common:2.6.1")
    implementation project(':lib_xpopup')
    implementation 'androidx.cardview:cardview:1.0.0'
//    implementation "androidx.room:room-runtime:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"
    implementation("androidx.room:room-ktx:2.6.1")
//    testImplementation(libs.junit)
//    androidTestImplementation(libs.androidx.junit)
//    androidTestImplementation(libs.androidx.espresso.core)
//    androidTestImplementation(platform(libs.androidx.compose.bom))
//    androidTestImplementation(libs.androidx.ui.test.junit4)
//    debugImplementation(libs.androidx.ui.tooling)
//    debugImplementation(libs.androidx.ui.test.manifest)
    implementation ("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation ("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation 'androidx.activity:activity-ktx:1.8.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
//    api ("com.github.PhilJay:MPAndroidChart:v3.0.3")
//    implementation 'io.github.cymchad:BaseRecyclerViewAdapterHelper4:4.1.4'
    implementation "io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.14"
    implementation platform('com.google.firebase:firebase-bom:33.5.0')
    implementation 'com.google.firebase:firebase-crashlytics'
}