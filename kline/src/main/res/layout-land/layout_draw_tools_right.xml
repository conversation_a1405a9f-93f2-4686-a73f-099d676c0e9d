<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    tools:ignore="MissingDefaultResource">

    <data>

    </data>

    <LinearLayout
        tools:visibility="visible"
        android:id="@+id/draw_tools_right_layout"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

<!--        <androidx.constraintlayout.widget.ConstraintLayout-->
<!--            android:id="@+id/base_line"-->
<!--            android:layout_weight="1"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="0dp">-->

<!--            <com.upex.common.widget.view.BaseTextView-->
<!--                android:id="@+id/trend_line"-->
<!--                android:gravity="center"-->
<!--                tools:fontFamily="@font/iconfont"-->
<!--                app:isFontText="true"-->
<!--                android:textSize="18sp"-->
<!--                android:text="@string/icon_draw_trend_line"-->
<!--                android:textColor="?attr/colorSubtitle"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent" />-->

<!--            <com.upex.common.widget.view.BaseTextView-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="parent"-->
<!--                android:id="@+id/trend_line_triangle"-->
<!--                android:layout_marginStart="6dp"-->
<!--                android:textSize="9sp"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                android:gravity="center"-->
<!--                tools:fontFamily="@font/iconfont"-->
<!--                app:autoMirrored="true"-->
<!--                app:isFontText="true"-->
<!--                android:text="@string/icon_draw_left_arrow"-->
<!--                android:textColor="?attr/colorSubtitle"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content" />-->

<!--        </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--        <androidx.constraintlayout.widget.ConstraintLayout-->
<!--            android:id="@+id/waves"-->
<!--            android:layout_weight="1"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="0dp">-->

<!--            <com.upex.common.widget.view.BaseTextView-->
<!--                android:id="@+id/wave_line"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_weight="1"-->
<!--                android:gravity="center"-->
<!--                android:text="@string/icon_draw_three_wave"-->
<!--                android:textColor="?attr/colorSubtitle"-->
<!--                android:textSize="18sp"-->
<!--                app:isFontText="true"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                tools:fontFamily="@font/iconfont" />-->

<!--            <com.upex.common.widget.view.BaseTextView-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="parent"-->
<!--                android:id="@+id/wave_line_triangle"-->
<!--                android:layout_marginStart="6dp"-->
<!--                android:textSize="9sp"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                android:gravity="center"-->
<!--                tools:fontFamily="@font/iconfont"-->
<!--                app:autoMirrored="true"-->
<!--                app:isFontText="true"-->
<!--                android:text="@string/icon_draw_left_arrow"-->
<!--                android:textColor="?attr/colorSubtitle"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content" />-->


<!--        </androidx.constraintlayout.widget.ConstraintLayout>-->


<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:id="@+id/rect_line"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            tools:fontFamily="@font/iconfont"-->
<!--            app:isFontText="true"-->
<!--            android:textSize="19sp"-->
<!--            android:text="@string/icon_draw_rect"-->
<!--            android:layout_width="match_parent"-->
<!--            android:textColor="?attr/colorSubtitle"-->
<!--            android:layout_height="0dp" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:id="@+id/fibonacci_line"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            tools:fontFamily="@font/iconfont"-->
<!--            app:isFontText="true"-->
<!--            android:textSize="18sp"-->
<!--            android:text="@string/icon_draw_fibonacci"-->
<!--            android:layout_width="match_parent"-->
<!--            android:textColor="?attr/colorSubtitle"-->
<!--            android:layout_height="0dp" />-->

<!--        <View-->
<!--            android:layout_gravity="center_horizontal"-->
<!--            android:layout_marginStart="5dp"-->
<!--            android:layout_marginEnd="5dp"-->
<!--            android:layout_width="32dp"-->
<!--            android:layout_height="1px"-->
<!--            android:background="?attr/colorLine" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:id="@+id/draw_tool_continuation"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            tools:fontFamily="@font/iconfont"-->
<!--            app:isFontText="true"-->
<!--            android:textSize="19sp"-->
<!--            android:text="@string/icon_draw_continuation"-->
<!--            android:layout_width="match_parent"-->
<!--            android:textColor="?attr/colorSubtitle"-->
<!--            android:layout_height="0dp" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:id="@+id/draw_tool_delete"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            tools:fontFamily="@font/iconfont"-->
<!--            app:isFontText="true"-->
<!--            android:textSize="18sp"-->
<!--            android:text="@string/icon_draw_delete"-->
<!--            android:layout_width="match_parent"-->
<!--            android:textColor="?attr/colorSubtitle"-->
<!--            android:layout_height="0dp" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:id="@+id/draw_tool_eye"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            tools:fontFamily="@font/iconfont"-->
<!--            app:isFontText="true"-->
<!--            android:textSize="18sp"-->
<!--            android:text="@string/icon_draw_line_close_eye"-->
<!--            android:layout_width="match_parent"-->
<!--            android:textColor="?attr/colorSubtitle"-->
<!--            android:layout_height="0dp" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:id="@+id/draw_tool_close"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            tools:fontFamily="@font/iconfont"-->
<!--            app:isFontText="true"-->
<!--            android:textSize="18sp"-->
<!--            android:text="@string/icon_draw_close"-->
<!--            android:layout_width="match_parent"-->
<!--            android:textColor="?attr/colorSubtitle"-->
<!--            android:layout_height="0dp" />-->
    </LinearLayout>
</layout>