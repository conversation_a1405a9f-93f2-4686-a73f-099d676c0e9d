<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/ll_bt"
    android:clipChildren="false"
    android:orientation="vertical">

    <TextView
        android:paddingBottom="2dp"
        android:paddingTop="2dp"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:id="@+id/marker_tv_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_my_shape"
        android:gravity="center"
        android:text=""
        android:textColor="@color/color_bg_myshape_stork" />

</LinearLayout>