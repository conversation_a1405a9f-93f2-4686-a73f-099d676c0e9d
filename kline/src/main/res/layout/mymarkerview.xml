<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:paddingBottom="6dp"
        android:paddingTop="6dp"
        android:paddingRight="20dp"
        android:paddingLeft="15dp"
        android:gravity="center"
        android:id="@+id/marker_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@mipmap/icon_bg_jiantou"
        android:text=""
        android:textColor="@color/color_bg_myshape_stork" />

</LinearLayout>