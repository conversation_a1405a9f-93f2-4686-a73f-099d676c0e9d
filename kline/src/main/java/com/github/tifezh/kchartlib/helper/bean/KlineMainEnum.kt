package com.github.tifezh.kchartlib.helper.bean

interface KlineEnum {
    fun getShowName(): String
    fun getValue(): String
}

enum class KlineMainEnum(var showTitle: String, private val value: String) : KlineEnum {
    NONE("", ""),
    MA("MA", "ma"),
    EMA("EMA", "ema"),
    BOLL("BOLL", "boll"),
    MIKE("MIKE", "mike"),
    BBI("BBI", "bbi"),
    SAR("SAR", "sar");

    override fun getShowName(): String {
        return showTitle
    }

    override fun getValue(): String {
        return value
    }
}

class KlineMainEnumUtils {
    companion object {
        @JvmStatic
        fun value2KlineMainEnum(value: String?): KlineMainEnum {
            val lowerValue = value?.lowercase() ?: return KlineMainEnum.NONE
            return when (lowerValue) {
                KlineMainEnum.MA.getValue() -> KlineMainEnum.MA
                KlineMainEnum.EMA.getValue() -> KlineMainEnum.EMA
                KlineMainEnum.BOLL.getValue() -> KlineMainEnum.BOLL
                KlineMainEnum.MIKE.getValue() -> KlineMainEnum.MIKE
                KlineMainEnum.BBI.getValue() -> KlineMainEnum.BBI
                KlineMainEnum.SAR.getValue() -> KlineMainEnum.SAR
                else -> KlineMainEnum.NONE
            }
        }
    }
}