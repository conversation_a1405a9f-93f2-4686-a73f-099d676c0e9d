package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
@Keep
public class MacdIndexEntity extends BaseIndexEntity {
    // 主图 boll
    public float dea;
    public float dif;
    public float macd;

    @Override
    public void reset() {
        super.reset();
        dea = 0f;
        dif = 0f;
        macd = 0f;
    }

    public float getDea() {
        return dea;
    }

    public float getDif() {
        return dif;
    }

    public float getMacd() {
        return macd;
    }

    public void setMacd(float dif, float dea, float macd) {
        this.dif = dif;
        this.dea = dea;
        this.macd = macd;
        setComputed(true);
    }

    @NonNull
    @Override
    public String toString() {
        return "MACDIndexEntity{" +
                "dea=" + dea +
                ", dif=" + dif +
                ", macd=" + macd +
                '}';
    }
}