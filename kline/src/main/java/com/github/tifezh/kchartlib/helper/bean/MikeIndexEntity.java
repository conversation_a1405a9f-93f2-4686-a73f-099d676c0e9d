package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Keep
public class MikeIndexEntity extends BaseIndexEntity {
    private final List<Float> mikes = new CopyOnWriteArrayList<>();

    @Override
    public void reset() {
        super.reset();
        mikes.clear();
    }

    public void setMikes(float wr, float mr, float sr, float ws, float ms, float ss) {
        mikes.clear();
        mikes.add(wr);
        mikes.add(mr);
        mikes.add(sr);
        mikes.add(ws);
        mikes.add(ms);
        mikes.add(ss);
        setComputed(true);
    }

    public List<Float> getMikes() {
        return mikes;
    }

    @NonNull
    @Override
    public String toString() {
        return "MIKEIndexEntity{" +
                "mikes=" + mikes +
                '}';
    }
}