package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

@Keep
public class VolIndexEntity extends BaseIndexEntity {
    // 主图 boll
    public float vMA5;
    public float vMA10;

    @Override
    public void reset() {
        super.reset();
        vMA5 = 0f;
        vMA10 = 0f;
    }

    public void setVol(float vMA5, float vMA10) {
        this.vMA5 = vMA5;
        this.vMA10 = vMA10;
        setComputed(true);
    }

    public float getVMA5() {
        return vMA5;
    }


    public float getVMA10() {
        return vMA10;
    }


    @NonNull
    @Override
    public String toString() {
        return "VolIndexEntity{" +
                "vMA5=" + vMA5 +
                ", vMA10=" + vMA10 +
                '}';
    }
}