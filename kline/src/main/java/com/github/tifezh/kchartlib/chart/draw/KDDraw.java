package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.LruCache;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.EntityImpl.KDJImpl;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.chart.impl.IKChartView;
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter;
import com.github.tifezh.kchartlib.chart.impl.SimpleChartDraw;
import com.github.tifezh.kchartlib.utils.DpConstant;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * KDJ实现类
 * Created by tifezh on 2016/6/19.
 */

public class KDDraw extends SimpleChartDraw<KDJImpl> {

    private final Paint mKPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint mDPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final List<Paint> paintList = new ArrayList<>();

    private final Context mContext;
    private int kdKValue = 9;
    private int kdDValue = 3;

    public KDDraw(Context context) {
        mContext = context;
        float lineWidth = context.getResources().getDimension(R.dimen.chart_line_width);
        float textSize = context.getResources().getDimension(R.dimen.chart_zhibiao_text_size);

        mKPaint.setColor(ContextCompat.getColor(context, R.color.kchartlib_ma1));
        mKPaint.setStrokeWidth(lineWidth);
        mKPaint.setStrokeCap(Paint.Cap.ROUND);
        mKPaint.setTextSize(textSize);

        mDPaint.setColor(ContextCompat.getColor(context, R.color.kchartlib_ma2));
        mDPaint.setStrokeWidth(lineWidth);
        mDPaint.setStrokeCap(Paint.Cap.ROUND);
        mDPaint.setTextSize(textSize);


        paintList.add(mKPaint);
        paintList.add(mDPaint);
    }

    @Override
    public void drawTranslated(@Nullable KDJImpl lastPoint, @NonNull KDJImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {

    }
    private float[] kLines;
    private float[] dLines;

    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
//        super.drawOnScreen(canvas, view);
        List<Pair<Float, KLineImpl>> mPointList = view.getPointList();
        if (mPointList == null || mPointList.isEmpty()) {
            return;
        }
        int pointSize = mPointList.size();
        int linesSize = pointSize * 4;
        int idxK = 0;
        int idxD = 0;
        boolean isNeedDraw = false;
        if (kLines == null || kLines.length != linesSize) {
            kLines = new float[linesSize];
        }
        if (dLines == null || dLines.length != linesSize) {
            dLines = new float[linesSize];
        }
        for (int i = 1; i < pointSize; i++) {
            Pair<Float, KLineImpl> lastPointPair = mPointList.get(i - 1);
            Pair<Float, KLineImpl> cuxPointPair = mPointList.get(i);
            Float lastX = lastPointPair.first;
            KDJImpl lastPoint = lastPointPair.second;
            Float curX = cuxPointPair.first;
            KDJImpl curPoint = cuxPointPair.second;

            if (lastPoint == null || curPoint == null) {
                continue;
            }
            kLines[idxK++] = lastX;
            kLines[idxK++] = getChildY(lastPoint.getK());
            kLines[idxK++] = curX;
            kLines[idxK++] = getChildY(curPoint.getK());

            dLines[idxD++] = lastX;
            dLines[idxD++] = getChildY(lastPoint.getD());
            dLines[idxD++] = curX;
            dLines[idxD++] = getChildY(curPoint.getD());

            isNeedDraw = true;
        }

        if (isNeedDraw) {
            if (kLines.length > idxK) {
                canvas.drawLines(kLines, 0, idxK, mKPaint);
            } else {
                canvas.drawLines(kLines, mKPaint);
            }
            if (dLines.length > idxD) {
                canvas.drawLines(dLines, 0, idxD, mDPaint);
            } else {
                canvas.drawLines(dLines, mDPaint);
            }
        }
    }

    @Override
    public void onDrawScreenLine(@Nullable KDJImpl lastPoint, @NonNull KDJImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (position >= 0) {
            if (lastPoint != null) {
                drawChildLine(canvas, mKPaint, lastX, lastPoint.getK(), curX, curPoint.getK());
                drawChildLine(canvas, mDPaint, lastX, lastPoint.getD(), curX, curPoint.getD());
//                drawChildLine(canvas, mJPaint, lastX, lastPoint.getJ(), curX, curPoint.getJ());
            }
        }
    }

    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {
        x += DpConstant.dp10();

        String text = String.format(Locale.ENGLISH, "KD(%d,%d)", kdKValue, kdDValue);
        canvas.drawText(text, x, y, mKPaint);
        if (position < 0) {
            return;
        }
        x += getTextWidth(text) + MainDraw.mTextMargin;
        KDJImpl point = (KDJImpl) view.getItem(position);
        if (null == point) {
            return;
        }
        text = "K:" + view.formatValue(point.getK());
        canvas.drawText(text, x, y, mKPaint);
        x += getTextWidth(text) + MainDraw.mTextMargin;
        text = "D:" + view.formatValue(point.getD());
        canvas.drawText(text, x, y, mDPaint);
    }

    private final LruCache<String, Float> kTextCache = new LruCache<>(16);

    private Float getTextWidth(String text){
        if(TextUtils.isEmpty(text)){
            return 0f;
        }
        Float width = kTextCache.get(text);
        if(width==null || width<=0f){
            width =  mKPaint.measureText(text);
            kTextCache.put(text,width);
        }
        return width;
    }

    @Override
    public float getMaxValue(KDJImpl point, int position) {
        return Math.max(point.getK(), Math.max(point.getD(), point.getJ()));
    }

    @Override
    public float getMinValue(KDJImpl point, int position) {
        return Math.min(point.getK(), Math.min(point.getD(), point.getJ()));
    }

    @Override
    public void setTypeFace(Typeface typeFace) {
        if (typeFace == null) return;
        for (Paint paint : paintList) {
            paint.setTypeface(typeFace);
        }
    }

    @Override
    public void setValueFormatter(IValueFormatter valueFormatter) {

    }

    @Override
    public IValueFormatter getValueFormatter() {
        return null;
    }

    /**
     * 设置K颜色
     */
    public void setKColor(int color) {
        mKPaint.setColor(color);
    }

    /**
     * 设置D颜色
     */
    public void setDColor(int color) {
        mDPaint.setColor(color);
    }


    public void setLineWidth(int lineWidth) {
        mKPaint.setStrokeWidth(lineWidth);
        mDPaint.setStrokeWidth(lineWidth);
    }

    public void setKdKValue(int kdKValue) {
        this.kdKValue = kdKValue;
    }

    public void setKdDValue(int kdDValue) {
        this.kdDValue = kdDValue;
    }

}
