package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.BaseKChartView;
import com.github.tifezh.kchartlib.chart.EntityImpl.CandleImpl;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.chart.impl.IKChartView;
import com.github.tifezh.kchartlib.chart.impl.SimpleChartDraw;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class MADraw extends SimpleChartDraw<CandleImpl> {

    private final float textSize;
    private final Context mContext;
    private final List<Paint> maPaints = new ArrayList<>();
    private final Rect maRect = new Rect();
    private float mMaLineWidth;
    private float[][] maLines;
    private int[] linesLengths;

    public MADraw(Context context) {
        this.mContext = context;
        mMaLineWidth = context.getResources().getDimension(R.dimen.chart_line_width);
        textSize = context.getResources().getDimension(R.dimen.chart_zhibiao_text_size);
        List<Integer> colors = new ArrayList<>(4);
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma1));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma2));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma3));
        //        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma4));
        for (int i = 0; i < 3; i++) {
            Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            paint.setColor(colors.get(i));
            paint.setTextLocale(Locale.ENGLISH);
            paint.setStrokeWidth(mMaLineWidth);
            paint.setStrokeCap(Paint.Cap.ROUND);
            paint.setTextSize(textSize);
            maPaints.add(paint);
        }
    }
    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {
        x += MainDraw.mTextMarginStart;
        CandleImpl point = (KLineImpl) view.getItem(position);
        int chartWidth = view.getChartWidth();
        int rightTextContentWidth = ((BaseKChartView) view).getRightTextContentWidth();
        int lastWidth = chartWidth - rightTextContentWidth;
        for (int i = 0; i < maPaints.size(); i++) {
            Paint paint = maPaints.get(i);
            int value = indicatorValues.get(i);
            String price = "--";
            if(position >= value - 1) {
                if(point.getMAs().size() > i) {
                    price = view.formatValue(point.getMAs().get(i));
                }
            }
            String text = "MA" + value + ":" + price;
            paint.getTextBounds(text, 0, text.length(), maRect);
            float width = maRect.width() + MainDraw.mTextMargin;
            if(x + width > lastWidth) {
                y += maRect.height() + MainDraw.mTextMargin * 0.5f;
                x = MainDraw.mTextMarginStart;
            }
            canvas.drawText(text, x, y, paint);//绘制主指标顶部文案
            x += width;
        }
    }
    @Override
    public float getMaxValue(CandleImpl point, int positon) {
        float max = Float.MIN_VALUE;
        for (int i = 0; i < point.getMAs().size(); i++) {
            float f = point.getMAs().get(i);
            max = Math.max(max, f);
        }
        return Math.max(point.getHighPrice(), max);
    }
    @Override
    public float getMinValue(CandleImpl point, int positon) {
        float min = Float.MAX_VALUE;
        for (int i = 0; i < point.getMAs().size(); i++) {
            float f = point.getMAs().get(i);
            min = Math.min(min, f);
        }
        min = Math.min(point.getLowPrice(), min);
        if(min == 0)
            min = Float.MAX_VALUE;
        return min;
    }
    @Override
    public void setTypeFace(Typeface typeFace) {

    }
    @Override
    public void drawTranslated(@Nullable CandleImpl lastPoint, @NonNull CandleImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {

    }
    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
        //        super.drawOnScreen(canvas, view);
        List<Pair<Float, KLineImpl>> mPointList = view.getPointList();
        if(mPointList == null || mPointList.isEmpty()) {
            return;
        }
        int pointSize = mPointList.size();
        int maPaintSize = maPaints.size();
        if(maPaintSize < 1) {
            return;
        }
        if(maLines == null || maLines.length != maPaintSize) {
            maLines = new float[maPaintSize][];
        }
        if(linesLengths == null || linesLengths.length != pointSize) {
            linesLengths = new int[pointSize];
        }
        boolean isNeedDraw = false;
        for (int j = 0; j < maPaintSize; j++) {
            // i >= value + 1 - mStartIndex
            int value = indicatorValues.get(j);
            int start = Math.max(value + 1 - mStartIndex, 1);
            int length = pointSize - start;
            if (length < 1) {
                continue;
            }
            int pointsArrSize = (length) * 4;
            float[] points = maLines[j];
            if (points == null || points.length != pointsArrSize) {
                points = new float[pointsArrSize];
                maLines[j] = points;
            }
            int idx = 0;
            for (int i = start; i < pointSize; i++) {
                Pair<Float, KLineImpl> lastPointPair = mPointList.get(i - 1);
                Pair<Float, KLineImpl> cuxPointPair = mPointList.get(i);
                Float lastX = lastPointPair.first;
                CandleImpl lastPoint = lastPointPair.second;
                Float curX = cuxPointPair.first;
                CandleImpl curPoint = cuxPointPair.second;

                if (lastPoint == null || curPoint == null || lastPoint.getMAs().size() <= j || curPoint.getMAs().size() <= j) {
                    continue;
                }
                points[idx++] = lastX;
                points[idx++] = view.getMainY(lastPoint.getMAs().get(j));//主指标 绘线坐标赋值
                points[idx++] = curX;
                points[idx++] = view.getMainY(curPoint.getMAs().get(j));
                isNeedDraw = true;
            }
            linesLengths[j] = idx;
        }
        if(!isNeedDraw) {
            return;
        }
        for (int i = 0; i < maPaintSize; i++) {
            int length = linesLengths[i];
            if(maLines[i] == null) {
                continue;
            }
            if(maLines[i].length > length) {
                canvas.drawLines(maLines[i], 0, length, maPaints.get(i));
            } else {
                canvas.drawLines(maLines[i], maPaints.get(i));
            }
        }
    }

    @Override
    public void onDrawScreenLine(@Nullable CandleImpl lastPoint, @NonNull CandleImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        //        Log.d("onDrawScreenLine","==indicatorValues.size() > i=="+(indicatorValues.size() > maPaints.size()));
        try {
            for (int i = 0; i < maPaints.size(); i++) {
                if(indicatorValues.size() > i) {
                    int value = indicatorValues.get(i);
                    if(position >= value) {
                        if(lastPoint != null && lastPoint.getMAs().size() > i) {
                            view.drawMainLine(canvas, maPaints.get(i), lastX, lastPoint.getMAs().get(i), curX, curPoint.getMAs().get(i));
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(new Exception("MADraw.onDrawScreenLine e" + e.getMessage()));
        }
    }

    public void setMAColors(List<Integer> maColors) {
        maPaints.clear();
        for (int i = 0; i < maColors.size(); i++) {
            Paint maPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            maPaint.setColor(maColors.get(i));
            maPaint.setStrokeWidth(mMaLineWidth);
            maPaint.setStrokeCap(Paint.Cap.ROUND);
            maPaint.setTextSize(textSize);
            maPaints.add(maPaint);
        }
    }

    public void setMaLineWidht(int lineWidht) {
        mMaLineWidth = lineWidht;
        for (Paint paint : maPaints) {
            paint.setStrokeWidth(lineWidht);
        }
    }

    public void setMAValues(List<Integer> indicatorValues) {
        this.indicatorValues.clear();
        this.indicatorValues.addAll(indicatorValues);
    }

    @Override
    public int getTextH() {
        return maRect.height();
    }

}
