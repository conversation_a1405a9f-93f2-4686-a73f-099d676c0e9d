package com.github.tifezh.kchartlib.helper.chart;


import android.text.TextUtils;

import com.github.tifezh.kchartlib.chart.BaseKChartAdapter;
import com.github.tifezh.kchartlib.helper.bean.KLineEntity;
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum;
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnum;
import com.upex.common.utils.ExpandKt;
import com.upex.common.utils.TimeUtils;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 数据适配器
 * Created by tifezh on 2016/6/18.
 */

public class KChartAdapter extends BaseKChartAdapter {

    public static long STEP_ONE_MONTH = 1000L * 60 * 60 * 24 * 30;

    private List<KLineEntity> datas = new ArrayList<>();
    //滑动的size
    private int sizeForMoreScroll = 0;
    //缩放的size
    private int sizeForMoreScale = 0;

    private long step = 1L;

    public KChartAdapter() {

    }

    @Override
    public int getCount() {
        return datas.size();
    }

    @Override
    public Object getItem(int position) {
        return datas.get(position);
    }

    @Override
    public @Nullable Object getLastItem() {
        return !datas.isEmpty() ? datas.get(datas.size() - 1) : null;
    }

    @Override
    public String getDate(int position) {
        if (position > datas.size()) {
            position = datas.size() - 1;
        }
        String timestampFormat = datas.get(position).timestampFormat;
        if (!TextUtils.isEmpty(timestampFormat)) {
            return timestampFormat;
        }
        datas.get(position).timestampFormat = TimeUtils.getKlineTradeDate(Long.parseLong(datas.get(position).timestamp) + step / 2, "dd/MM HH:mm");
        return datas.get(position).timestampFormat;
    }

    /**
     * 向头部添加数据
     *
     * @param data
     */
    public void addHeaderData(List<KLineEntity> data) {
        if (data != null && !data.isEmpty()) {
            datas.addAll(data);
            notifyDataSetChanged();
        }
    }

    /**
     * 向尾部添加数据
     *
     * @param data
     */
    public void addData(List<KLineEntity> data) {
        if (data != null && !data.isEmpty()) {
            this.datas.addAll(0, data);
            DataHelper.calculate(this.datas, KlineMainEnum.getEntries(), KlineOtherEnum.getEntries());
            notifyDataSetChanged();
        }
    }

    public void setDatas(List<KLineEntity> data, Boolean isNeedScrollToStart) {
        if (data != null) {
            this.datas.clear();
            this.datas.addAll(data);
            DataHelper.calculate(this.datas, KlineMainEnum.getEntries(), KlineOtherEnum.getEntries());
            if (isNeedScrollToStart) {
                setSizeForMoreScroll(Integer.MAX_VALUE);
            }
//            if (isFirst){
//                notifyInvalidated();
//            }else {
//                notifyChanged();
//            }
            notifyDataSetChanged();
        }
    }

    public void addSingleData(KLineEntity data) {
        if (data != null) {
            this.datas.add(data);
            DataHelper.calculate(this.datas, KlineMainEnum.getEntries(), KlineOtherEnum.getEntries());
            notifyDataSetChanged();
        }
    }

    public void updateLastData(Float originalBid) {
        if (datas != null && !datas.isEmpty() && originalBid > 0) {
            KLineEntity lastData = (KLineEntity) getLastItem();
            if (lastData != null) {
                lastData.close = originalBid;
                if (originalBid > lastData.high) lastData.high = originalBid;
                if (originalBid < lastData.low) lastData.low = originalBid;
            }
        }
    }

    public void verifyData(List<KLineEntity> data) {
        int size = data.size();
        if (size > 5) {
            data = data.subList(size - 5, size);
        }
        KLineEntity lastData = (KLineEntity) getLastItem();
        if (lastData != null) {
            for (KLineEntity e : data) {
                for (int x = datas.size() - 1; x >= 0; x--) {
                    KLineEntity d = datas.get(x);
                    if (d.timestampLong == e.timestampLong) {
                        datas.set(x, e);
                        break;
                    }
                }
            }
            DataHelper.calculate(this.datas, KlineMainEnum.getEntries(), KlineOtherEnum.getEntries());
            notifyDataSetChanged();
        }
    }

    public void drawNextData(Float originalBid, int period) {
        KLineEntity lastData = (KLineEntity) getLastItem();
        if (lastData != null) {
            KLineEntity newData = new KLineEntity();
            newData.open = originalBid;
            newData.close = originalBid;
            newData.high = originalBid;
            newData.low = originalBid;
            newData.volume = 0f;
            long timeStamp = ExpandKt.toLongCatching(lastData.timestamp, 0L) + 60L * period;
            newData.timestamp = String.valueOf(timeStamp);
            newData.timestampLong = timeStamp;
//            Log.i("wj", "drawNextData: 自动创建: "+newData);
            addSingleData(newData);
        }
    }

    public List<KLineEntity> getDatas() {
        return datas;
    }

    public int getSizeForMoreScroll() {
        return sizeForMoreScroll;
    }

    public void setSizeForMoreScroll(int sizeForMoreScroll) {
        this.sizeForMoreScroll = sizeForMoreScroll;
    }

    public int getSizeForMoreScale() {
        return sizeForMoreScale;
    }

    public void setSizeForMoreScale(int sizeForMoreScale) {
        this.sizeForMoreScale = sizeForMoreScale;
    }

    public void setStep(long value) {
        this.step = value;
    }

    public long getStep() {
        return step;
    }
}
