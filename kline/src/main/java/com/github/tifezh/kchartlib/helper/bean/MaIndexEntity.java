package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
@Keep
public class MaIndexEntity extends BaseIndexEntity {
    private final List<Float> mas = new CopyOnWriteArrayList<>();

    @Override
    public void reset() {
        super.reset();
        mas.clear();
    }

    public void add(Float ma) {
        mas.add(ma);
    }

    public List<Float> getMas() {
        return mas;
    }


    @NonNull
    @Override
    public String toString() {
        return "MaIndexEntity{" + "mas=" + mas + '}';
    }
}