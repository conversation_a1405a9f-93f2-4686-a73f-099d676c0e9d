package com.upex.common.drawTools

import android.graphics.Color
import com.example.myapplication.R
import com.upex.common.language.Keys
import com.upex.common.utils.display.DensityUtil

enum class DrawToolsType(val drawName: String,val  icon: Int,val saveName:String) {
    //趋势线
    TrendLine(Keys.Markets_Kline_draw_trendline, 0,"trendline"),
    //延伸趋势线
    ExtendTrendLine(Keys.Markets_Kline_draw_extended_trendline, 0,"extended_trendline"),
    //射线
    Ray(Keys.Markets_Kline_draw_rays, 0,"rays"),
    //水平直线
    HorizontalStraightLine(Keys.Markets_Kline_draw_horizontal_line, 0,"horizontal_line"),
    //垂直线段
    VerticalSegment(Keys.Markets_Kline_draw_vertical_line, 0,"vertical_line"),
    //平行线
    ParallelLine(Keys.Markets_Kline_draw_parallel_lines, 0,"parallel_lines"),
    //价格线
    PriceLine(Keys.Markets_Kline_draw_price_line, 0,"price_line"),
    //三浪
    ThreeWaves(Keys.Markets_Kline_draw_waves_three, 0,"waves_three"),
    //五浪
    FiveWaves(Keys.Markets_Kline_draw_waves_five, 0,"waves_five"),
    //矩形
    Rect(Keys.Markets_Kline_DrawRectangle, 0,"Rectangle"),
    //斐波那契
    Fibonacci(Keys.Markets_Kline_DrawFibonacoci, 0,"Fibonacoci"),
    //贝瑟尔曲线
    Bezier(Keys.Markets_Kline_DrawBezier, 0,"Bezier"),
    //荧光笔
    Highlighter(Keys.Markets_Kline_DrawHighlighter, 0,"Highlighter");

    companion object {
        fun getDrawByName(type: String): DrawToolsType {
            return values().find { it.saveName == type } ?: TrendLine
        }
    }
}

enum class DrawToolsColors(val color: Int,val type: Int) {

    COLOR_0(Color.parseColor("#DA453D"),0),
    COLOR_1(Color.parseColor("#DE8345"),1),
    COLOR_2(Color.parseColor("#E7BB41"),2),
    COLOR_3(Color.parseColor("#58CA8D"),3),
    COLOR_4(Color.parseColor("#007FFF"),4),
    COLOR_5(Color.parseColor("#8E59E1"),5),

    FILL_COLOR_0(setTransparency(Color.parseColor("#DA453D"),256 / 3),6),
    FILL_COLOR_1(setTransparency(Color.parseColor("#DE8345"),256 / 3),7),
    FILL_COLOR_2(setTransparency(Color.parseColor("#E7BB41"),256 / 3),8),
    FILL_COLOR_3(Color.parseColor("#00000000"),9),
    FILL_COLOR_4(setTransparency(Color.parseColor("#58CA8D"),256 / 3),10),
    FILL_COLOR_5(setTransparency(Color.parseColor("#1DA2B4"),256 / 3),11),
    FILL_COLOR_6(setTransparency(Color.parseColor("#8E59E1"),256 / 3),12);

    companion object {
        fun convertEnumByType(id: Int?): DrawToolsColors {
            id ?: return COLOR_0
            val drawToolsColors = values().find { id == it.type } ?: COLOR_0
            return values().find { id == it.type } ?: COLOR_0
        }

        fun convertEnumByColor(id: Int?): DrawToolsColors {
            id ?: return COLOR_0
            return values().find { id == it.color } ?: COLOR_0
        }
    }
}

enum class DrawToolsLineWidth(val icon: Int,val  width: Int,val type: Int) {
    LineWidth0(0, DensityUtil.dp2px(0.5f),0),
    LineWidth1(0,DensityUtil.dp2px(1f),1),
    LineWidth2(0,DensityUtil.dp2px(1.5f),2),
    LineWidth3(0,DensityUtil.dp2px(2f),3);

    companion object {
        fun convertEnum(id: Int?): DrawToolsLineWidth {
            id ?: return LineWidth0
            return values()
                .find { id == it.width } ?: LineWidth0
        }

        fun convertEnumByType(id: Int?): DrawToolsLineWidth {
            id ?: return LineWidth0
            return values().find { id == it.type } ?: LineWidth0
        }
    }
}

enum class DrawToolsLineStyle(val icon: Int, val dash: FloatArray?,val number: Int) {
    LineStyle0(0,floatArrayOf(DensityUtil.dp2px(1f).toFloat(), DensityUtil.dp2px(1f).toFloat()),0),
    LineStyle1(0,floatArrayOf(DensityUtil.dp2px(2f).toFloat(), DensityUtil.dp2px(2f).toFloat()),1),
    LineStyle2(0,floatArrayOf(DensityUtil.dp2px(3f).toFloat(), DensityUtil.dp2px(3f).toFloat()),2),
    LineStyle3(0,floatArrayOf(0f, 0f),3);

    companion object {
        fun convertEnum(id: Int?): DrawToolsLineStyle {
            id ?: return LineStyle0
            return values().find { id == it.number } ?: LineStyle0
        }
    }
}

val megaSwap : String = "megaswap"
val spot : String = "spot"
val newprice : String = "newprice"
val markprice : String = "markprice"
val indexprice : String = "indexprice"


