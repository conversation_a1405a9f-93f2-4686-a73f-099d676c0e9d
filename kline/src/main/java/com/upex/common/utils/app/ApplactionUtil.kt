package com.upex.common.utils.app

import android.app.Application
import android.content.Context
import android.util.Log
import java.io.File

class ApplicationUtil {


    companion object {
        @JvmField
        var isMainFinishing: Boolean = false


        lateinit var app: Application
        lateinit var context: Context

        val cacheDir: String by lazy {
            context.cacheDir.absolutePath
        }


        val filesDir: String by lazy {
            context.filesDir.absolutePath
        }


        val externalFilesDir:String?  by lazy {
            context.getExternalFilesDir(null)?.absolutePath
        }
        val externalCacheDir: String? by lazy {
            context.externalCacheDir?.absolutePath
        }

        fun init(app: Application) {
            Companion.app = app
            context = app
        }

        /**
         * 根据文件类型获取file对象
         */
        fun externalFileDir(type: String?): File? {
            return externalFilesDir?.let {
                if (type == null) {
                    File(it)
                } else {
                    File(it.plus(File.separator).plus(type))
                }
            } ?: null
        }
    }

}