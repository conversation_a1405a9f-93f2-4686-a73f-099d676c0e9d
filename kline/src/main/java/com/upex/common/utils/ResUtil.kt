//package com.upex.common.utils
//
//import android.content.Context
//import android.content.res.ColorStateList
//import android.graphics.Color
//import android.graphics.PorterDuff
//import android.graphics.PorterDuffColorFilter
//import android.graphics.drawable.Drawable
//import androidx.annotation.AttrRes
//import androidx.annotation.ColorInt
//import androidx.annotation.ColorRes
//import androidx.annotation.DrawableRes
//import androidx.annotation.FloatRange
//import androidx.core.content.ContextCompat
//import com.example.myapplication.R
//import com.upex.common.utils.app.ApplicationUtil
//
//object ResUtil{
//
//    @JvmStatic
//    private fun getContext(): Context {
//        return ApplicationUtil.context
//    }
//
//    @JvmStatic
//    fun getStringIdentifier(id: String): String {
//        val context = getContext()
//        try {
//            return context.getString(
//                context.resources.getIdentifier(
//                    id,
//                    "string",
//                    context.packageName
//                )
//            )
//        } catch (e: Exception) {
//            e.printStackTrace()
//            return ""
//        }
//    }
//
//    /**
//     * 根据资源名字，获取资源id
//     */
//    @JvmStatic
//    fun getMipmapID(name: String?): Int {
//        val context = getContext()
//        //如果没有在"mipmap"下找到imageName,将会返回0
//        try {
//            return context.resources.getIdentifier(name, "mipmap", context.packageName)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            return 0
//        }
//    }
//
//    /**
//     * 根据资源名字，获取资源id
//     * 获取失败，使用默认
//     */
//    @JvmStatic
//    fun getMipmapByStr(name: String?, defaultID: Int): Int {
//        var id = 0;
//        //如果没有在"mipmap"下找到imageName,将会返回0
//        try {
//            id = getMipmapID(name)
//            if (id == 0) {
//                id = defaultID
//            }
//            return id
//        } catch (e: Exception) {
//           e.printStackTrace()
//            return defaultID
//        }
//    }
//
//
//    @JvmStatic
//    fun getString(id: Int): String {
//        val context = getContext()
//        try {
//            return context.getString(id)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            return ""
//        }
//    }
//
//    @JvmStatic
//    fun getColorStateList(context: Context, @ColorRes id: Int): ColorStateList {
//        return ContextCompat.getColorStateList(context, id)
//            ?: ColorStateList(arrayOf(), intArrayOf())
//    }
//
//    @JvmStatic
//    fun getColor(id: Int): Int {
//        return ToolResUtil.getColor(id)
//    }
//
//    @JvmStatic
//    fun getColorResWithAlpha(@ColorRes colorResId:Int,@FloatRange(from = 0.0,to = 1.0) alpha: Float):Int{
//        return getColorWithAlpha(getColor(colorResId),alpha)
//    }
//
//    @JvmStatic
//    fun getDimen(id: Int): Float {
//        return getContext().resources.getDimension(id)
//    }
//
//    @JvmStatic
//    fun getDimenPixe(id: Int): Int {
//        return getContext().resources.getDimensionPixelSize(id)
//    }
//
//    @JvmStatic
//    fun getDrawable(@DrawableRes id: Int): Drawable? {
//        return ToolResUtil.getDrawable(id)
//    }
//
//    @JvmStatic
//    fun getDrawable(context: Context, @DrawableRes attrId: Int): Drawable? {
//        return ToolResUtil.getDrawable(context, attrId)
//    }
//
//    @JvmStatic
//    fun getThemeColor(@AttrRes attrId: Int): Int {
//        return ToolResUtil.getThemeColor(attrId)
//    }
//
//    @JvmStatic
//    fun getThemeColor(context: Context, @AttrRes attrId: Int): Int {
//        return ToolResUtil.getThemeColor(context, attrId)
//    }
//
//    @JvmStatic
//    fun getThemeString(@AttrRes attrId: Int): CharSequence {
//        return ToolResUtil.getThemeString(attrId)
//    }
//
//    @JvmStatic
//    fun getThemeId(@AttrRes attrId: Int): Int {
//        return ToolResUtil.getThemeId(attrId)
//    }
//
//    @JvmStatic
//    fun getThemeDrawable(@AttrRes attrId: Int): Drawable? {
//        return ToolResUtil.getThemeDrawable(attrId)
//    }
//
//    @JvmStatic
//    fun getDrawable(
//        @DrawableRes drawableId: Int,
//        @AttrRes attrColorId: Int? = null
//    ): Drawable? {
//        return ToolResUtil.getDrawable(drawableId)?.let {
//            val newDrawable = it
//            if (attrColorId != null) {
//                newDrawable.colorFilter = PorterDuffColorFilter(
//                    getThemeColor(attrColorId),
//                    PorterDuff.Mode.SRC_IN
//                )
//            }
//            return newDrawable
//        }
//    }
//
//    @JvmStatic
//    fun getThemeDrawable(context: Context, @AttrRes attrId: Int): Drawable? {
//        return ToolResUtil.getThemeDrawable(context, attrId)
//    }
//
//    @JvmField
//    var COLOR_RED = ToolResUtil.COLOR_RED
//
//    @JvmField
//    var COLOR_BLUE = ToolResUtil.COLOR_BLUE
//
//    @JvmField
//    var COLOR_3E4 = ToolResUtil.COLOR_3E4
//
//        @JvmField
//        var COLOR_H_02 = ToolResUtil.COLOR_H_02
//
//    @JvmField
//    var COLOR_AEB = ToolResUtil.COLOR_AEB
//
//
//    @JvmField
//    var COLOR_8EC = ToolResUtil.COLOR_8EC
//
//    @JvmField
//    var COLOR_100 = ToolResUtil.COLOR_100
//
//    @JvmField
//    var COLOR_FF8400 = ToolResUtil.COLOR_FF8400
//
//
//    @JvmField
//    var Color_B_00 = ToolResUtil.Color_B_00
//
//
//    @JvmField
//    var Color_B_01 = ToolResUtil.Color_B_01
//
//
//    @JvmField
//    var Color_R_00 = ToolResUtil.Color_R_00
//
//    @JvmField
//    var Color_R1_00 = ToolResUtil.Color_R1_00
//
//    @JvmField
//    var Color_R_01 = ToolResUtil.Color_R_01
//
//    @JvmField
//    var Color_G_00 = ToolResUtil.Color_G_00
//
//    @JvmField
//    var Color_GL_00 = ToolResUtil.Color_GL_00
//
//    @JvmField
//    var Color_GL_01 = ToolResUtil.Color_GL_01
//
//    @JvmField
//    var Color_G_01 = ToolResUtil.Color_G_01
//
//    @JvmField
//    var Color_G1_00 = ToolResUtil.Color_G1_00
//
//    @JvmField
//    var Color_J_00 = ToolResUtil.Color_J_00
//
//    @JvmField
//    var Color_J_01 = ToolResUtil.Color_J_01
//
//    @JvmField
//    var Color_J_02 = ToolResUtil.Color_J_02
//
//    @JvmField
//    var Color_Y_00 = ToolResUtil.Color_Y_00
//
//    @JvmField
//    var Color_Y_01 = ToolResUtil.Color_Y_01
//
//    @JvmField
//    var colorTitle = ToolResUtil.colorTitle
//
//    @JvmField
//    var colorSubtitle = ToolResUtil.colorSubtitle
//
//
//    @JvmField
//    var colorDescription = ToolResUtil.colorDescription
//
//
//    @JvmField
//    var colorHint = ToolResUtil.colorHint
//
//
//    @JvmField
//    var colorOutline = ToolResUtil.colorOutline
//
//    @JvmField
//    var colorLine = ToolResUtil.colorLine
//
//    @JvmField
//    var colorWhite = ToolResUtil.colorWhite
//
//    @JvmField
//    var colorBgDivider = ToolResUtil.colorBgDivider
//
//    @JvmField
//    var colorBlockPrimary = ToolResUtil.colorBlockPrimary
//
//    @JvmField
//    var colorBlockSecondary = ToolResUtil.colorBlockSecondary
//
//    @JvmField
//    var colorFrontGround = ToolResUtil.colorFrontGround
//
//    @JvmField
//    var colorBackground = ToolResUtil.colorBackground
//
//    @JvmField
//    var colorButtonHighlight = ToolResUtil.colorButtonHighlight
//
//    @JvmField
//    var colorTextOnButtonHighlight = ToolResUtil.colorTextOnButtonHighlight
//
//    @JvmField
//    var COLOR_TRANS = ToolResUtil.COLOR__Trans
//
//    @JvmField
//    var colorBtnCheckedStatus = ToolResUtil.colorBtnCheckedStatus
//
//    @JvmField
//    var iconCommonUnselect: Drawable? = ToolResUtil.iconCommonUnselect
//
//    @JvmField
//    var iconCommonSelected: Drawable? = ToolResUtil.iconCommonSelected
//
//    @JvmField
//    var colorCardDecoration = ToolResUtil.colorCardDecoration
//
//    @JvmStatic
//    fun getIconSelectCommmon(isSelect: Boolean): Drawable? {
//        return if (isSelect) ToolResUtil.getThemeDrawable(R.attr.drawable_icon_common_select) else ToolResUtil.getThemeDrawable(
//            R.attr.drawable_icon_common_unselect
//        )
//    }
//
//    @JvmStatic
//    fun getColorTitle(context: Context): Int {
//        return ToolResUtil.getColorTitle(context)
//    }
//    @JvmStatic
//    fun getColorCardDecoration(context: Context): Int {
//        return ToolResUtil.getColorCardDecoration(context)
//    }
//    @JvmStatic
//    fun getColorSubtitle(context: Context): Int {
//        return ToolResUtil.getColorSubtitle(context)
//    }
//
//    @JvmStatic
//    fun getColorDescription(context: Context): Int {
//        return ToolResUtil.getColorDescription(context)
//    }
//
//    @JvmStatic
//    fun getColorHint(context: Context): Int {
//        return ToolResUtil.getColorHint(context)
//    }
//
//    @JvmStatic
//    fun getColorOutline(context: Context): Int {
//        return ToolResUtil.getColorOutline(context)
//    }
//
//    @JvmStatic
//    fun getColorLine(context: Context): Int {
//        return ToolResUtil.getColorLine(context)
//    }
//
//    @JvmStatic
//    fun getColorBgDivider(context: Context): Int {
//        return ToolResUtil.getColorBgDivider(context)
//    }
//
//    @JvmStatic
//    fun getColorBlockPrimary(context: Context): Int {
//        return ToolResUtil.getColorBlockPrimary(context)
//    }
//
//    @JvmStatic
//    fun getColorBlockSecondary(context: Context): Int {
//        return ToolResUtil.getColorBlockSecondary(context)
//    }
//
//    @JvmStatic
//    fun getColorFrontGround(context: Context): Int {
//        return ToolResUtil.getColorFrontGround(context)
//    }
//
//    @JvmStatic
//    fun getColorBackground(context: Context): Int {
//        return ToolResUtil.getColorBackground(context)
//    }
//
//    @JvmStatic
//    fun getColorWhite(context: Context): Int {
//        return ToolResUtil.getColorWhite(context)
//    }
//
//
//    /**
//     * 切换皮肤 重新获取颜色
//     */
//    @JvmStatic
//    fun onSkinChanged() {
//
//        COLOR_RED = ToolResUtil.COLOR_RED
//
//        COLOR_BLUE = ToolResUtil.COLOR_BLUE
//
//        COLOR_3E4 = ToolResUtil.COLOR_3E4
//
//        COLOR_AEB = ToolResUtil.COLOR_AEB
//
//        COLOR_H_02 = ToolResUtil.COLOR_H_02
//
//        COLOR_8EC = ToolResUtil.COLOR_8EC
//
//        COLOR_100 = ToolResUtil.COLOR_100
//
//        colorWhite = ToolResUtil.colorWhite
//
//        Color_B_00 = ToolResUtil.Color_B_00
//
//        Color_B_01 = ToolResUtil.Color_B_01
//
//        Color_R_00 = ToolResUtil.Color_R_00
//
//        Color_R_01 = ToolResUtil.Color_R_01
//
//        Color_G_00 = ToolResUtil.Color_G_00
//
//        Color_G_01 = ToolResUtil.Color_G_01
//
//        Color_GL_00 = ToolResUtil.Color_GL_00
//
//        Color_GL_01 = ToolResUtil.Color_GL_01
//
//        Color_J_00 = ToolResUtil.Color_J_00
//
//        Color_J_01 = ToolResUtil.Color_J_01
//
//
//        colorTitle = ToolResUtil.colorTitle
//
//        colorSubtitle = ToolResUtil.colorSubtitle
//
//        colorDescription = ToolResUtil.colorDescription
//
//        colorHint = ToolResUtil.colorHint
//
//        colorOutline = ToolResUtil.colorOutline
//
//        colorLine = ToolResUtil.colorLine
//
//        colorBgDivider = ToolResUtil.colorBgDivider
//
//        colorBlockPrimary = ToolResUtil.colorBlockPrimary
//
//        colorBlockSecondary = ToolResUtil.colorBlockSecondary
//
//        colorFrontGround = ToolResUtil.colorFrontGround
//
//        colorBackground = ToolResUtil.colorBackground
//
//        COLOR_FF8400 = ToolResUtil.COLOR_FF8400
//
//        iconCommonUnselect = ToolResUtil.iconCommonUnselect
//        iconCommonSelected = ToolResUtil.iconCommonSelected
//
//        colorButtonHighlight = ToolResUtil.colorButtonHighlight
//
//        colorTextOnButtonHighlight = ToolResUtil.colorTextOnButtonHighlight
//
//        colorCardDecoration = ToolResUtil.colorCardDecoration
//
//        COLOR_TRANS = ToolResUtil.COLOR__Trans
//
//    }
//
//
//    @JvmStatic
//    fun getColorWithAlpha(
//        @ColorInt color: Int,
//        @FloatRange(from = 0.0, to = 1.0) alpha: Float,
//    ): Int {
//        return ToolResUtil.getColorWithAlpha(color, alpha)
//    }
//
//    @ColorInt
//    fun argb2rgb(@ColorInt color: Int): Int {
//        val a = Color.alpha(color)
//        val r = Color.red(color)
//        val g = Color.green(color)
//        val b = Color.blue(color)
//        val source = mapOf("a" to a, "r" to r, "g" to g, "b" to b).mapValues { it.value / 255f }
//
//
//        val nR =
//            255 * ((1 - source.getValue("a") * 1) + (source.getValue("a") * source.getValue("r"))).toInt()
//        val nG =
//            255 * ((1 - source.getValue("a") * 1) + (source.getValue("a") * source.getValue("g"))).toInt()
//        val nB =
//            255 * ((1 - source.getValue("a") * 1) + (source.getValue("a") * source.getValue("b"))).toInt()
//
//        return Color.rgb(nR, nG, nB)
//    }
//}
